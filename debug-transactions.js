const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://rrvdmpagsvhjdurfmqec.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.SbZmq3nlQudzUN-xzJHj-NS23xnFVSlyx1RZWWZTy6U'

const supabase = createClient(supabaseUrl, supabaseKey)

async function investigateTransactionIssue() {
  try {
    console.log('🔍 Investigating transaction categorization issue...\n')

    const companyId = '5277bdf7-bbeb-4cab-bcbb-e55ee177b4dd'

    // Check actual transaction counts by type
    const { data: bankTransactions, error: bankError } = await supabase
      .from('transactions')
      .select('id, amount, date, reference, description, transaction_type, file_id')
      .eq('company_id', companyId)
      .eq('transaction_type', 'bank_statement')

    const { data: ledgerTransactions, error: ledgerError } = await supabase
      .from('transactions')
      .select('id, amount, date, reference, description, transaction_type, file_id')
      .eq('company_id', companyId)
      .eq('transaction_type', 'ledger_entry')

    if (bankError || ledgerError) {
      console.error('Database errors:', { bankError, ledgerError })
      return
    }

    console.log(`📊 Actual transaction counts:`)
    console.log(`   Bank statements: ${bankTransactions.length}`)
    console.log(`   Ledger entries: ${ledgerTransactions.length}`)
    console.log(`   Total: ${bankTransactions.length + ledgerTransactions.length}`)

    if (bankTransactions.length === 1000 && ledgerTransactions.length === 1000) {
      console.log('\n❌ PROBLEM IDENTIFIED: Both types have 1000 transactions!')
      console.log('This means transactions are incorrectly categorized - there should be:')
      console.log('   - 506 bank_statement transactions')
      console.log('   - 494 ledger_entry transactions')
      console.log('\n🔧 Need to fix transaction type categorization!')
    } else {
      console.log('✅ Transaction counts look correct')
    }

    // Sample a few transactions to see their structure
    console.log('\n📝 Sample bank transactions:')
    bankTransactions.slice(0, 3).forEach((tx, i) => {
      console.log(`   ${i+1}. Amount: ${tx.amount}, Date: ${tx.date}, Ref: ${tx.reference || 'N/A'}`)
    })

    console.log('\n📝 Sample ledger transactions:')
    ledgerTransactions.slice(0, 3).forEach((tx, i) => {
      console.log(`   ${i+1}. Amount: ${tx.amount}, Date: ${tx.date}, Ref: ${tx.reference || 'N/A'}`)
    })

    // Check file association
    const fileStats = {}
    bankTransactions.forEach(tx => {
      fileStats[tx.file_id] = (fileStats[tx.file_id] || 0) + 1
    })
    ledgerTransactions.forEach(tx => {
      fileStats[tx.file_id] = (fileStats[tx.file_id] || 0) + 1
    })

    console.log('\n📁 Transactions by file:')
    for (const [fileId, count] of Object.entries(fileStats)) {
      console.log(`   File ${fileId}: ${count} transactions`)
    }

    // Check reconciliations table schema
    console.log('\n🗄️ Checking reconciliations table schema...')
    const { data: reconciliations, error: reconError } = await supabase
      .from('reconciliations')
      .select('*')
      .limit(1)

    if (reconError) {
      console.log(`❌ Reconciliations table error: ${reconError.message}`)
      if (reconError.message.includes('confidence_score')) {
        console.log('🔧 Need to add confidence_score column to reconciliations table!')
      }
    } else {
      console.log('✅ Reconciliations table accessible')
    }

  } catch (error) {
    console.error('Investigation failed:', error)
  }
}

investigateTransactionIssue()