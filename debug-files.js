// Debug script to check file status
const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

async function debugFiles() {
  try {
    console.log('🔍 Debugging uploaded files...')
    
    // Get all files
    const { data: files, error } = await supabaseAdmin
      .from('files')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(10)
      
    if (error) {
      console.error('❌ Error:', error)
      return
    }
    
    console.log(`📁 Found ${files?.length || 0} files:`)
    
    files?.forEach((file, index) => {
      console.log(`\n${index + 1}. ${file.original_filename}`)
      console.log(`   📄 ID: ${file.id}`)
      console.log(`   📊 Status: ${file.status}`)
      console.log(`   🗂️  Type: ${file.mime_type}`)
      console.log(`   🏷️  File Type: ${file.metadata?.file_type || 'unknown'}`)
      console.log(`   📅 Created: ${file.created_at}`)
    })
    
    // Check if we have the required files
    const hasBank = files?.some(f => f.metadata?.file_type === 'bank_statement' || f.mime_type?.includes('pdf'))
    const hasLedger = files?.some(f => f.metadata?.file_type === 'ledger' || f.mime_type?.includes('sheet'))
    
    console.log(`\n✅ Analysis:`)
    console.log(`   🏦 Has Bank Statement: ${hasBank ? 'YES' : 'NO'}`)
    console.log(`   📊 Has Ledger File: ${hasLedger ? 'YES' : 'NO'}`)
    console.log(`   🚀 Ready for Processing: ${hasBank && hasLedger ? 'YES' : 'NO'}`)
    
    if (hasBank && hasLedger) {
      console.log('\n🎉 All files are ready! The seamless workflow should work now.')
      console.log('💡 Try refreshing the page or clicking the "Refresh & Continue" button.')
    } else {
      console.log('\n⚠️  Missing required files. Please upload both:')
      if (!hasBank) console.log('   - Bank statement (PDF)')
      if (!hasLedger) console.log('   - Ledger file (Excel/CSV)')
    }
    
  } catch (error) {
    console.error('💥 Debug failed:', error)
  }
}

debugFiles()
