const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://rrvdmpagsvhjdurfmqec.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJydmRtcGFnc3ZoamR1cmZtcWVjIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1ODEzMDM1MiwiZXhwIjoyMDczNzA2MzUyfQ.SbZmq3nlQudzUN-xzJHj-NS23xnFVSlyx1RZWWZTy6U'

const supabase = createClient(supabaseUrl, supabaseKey)

async function fixTransactionDuplication() {
  try {
    console.log('🔧 Fixing transaction duplication issue...\n')

    const companyId = '5277bdf7-bbeb-4cab-bcbb-e55ee177b4dd'

    // 1. Clear all existing transactions to start fresh
    console.log('1. Clearing existing transactions...')
    const { error: deleteError } = await supabase
      .from('transactions')
      .delete()
      .eq('company_id', companyId)

    if (deleteError) {
      console.error('Error deleting transactions:', deleteError)
      return
    }
    console.log('✅ Existing transactions cleared')

    // 2. Clear existing reconciliations
    console.log('2. Clearing existing reconciliations...')
    const { error: deleteReconError } = await supabase
      .from('reconciliations')
      .delete()
      .eq('company_id', companyId)

    if (deleteReconError) {
      console.error('Error deleting reconciliations:', deleteReconError)
    } else {
      console.log('✅ Existing reconciliations cleared')
    }

    // 3. Re-import transactions correctly from JSON files
    console.log('3. Re-importing transactions with correct categorization...')

    const fs = require('fs')

    // Read bank transactions (PDF file)
    const bankTransactionsPath = '/Users/<USER>/Desktop/projects/acounting-app/src/lib/e2b/extracted_data/bank_transactions.json'
    const bankTransactions = JSON.parse(fs.readFileSync(bankTransactionsPath, 'utf8'))

    // Read ledger transactions (Excel file)
    const ledgerTransactionsPath = '/Users/<USER>/Desktop/projects/acounting-app/src/lib/e2b/extracted_data/ledger_transactions_complete.json'
    const ledgerTransactions = JSON.parse(fs.readFileSync(ledgerTransactionsPath, 'utf8'))

    console.log(`   Bank transactions to import: ${bankTransactions.length}`)
    console.log(`   Ledger transactions to import: ${ledgerTransactions.length}`)

    // File IDs
    const bankFileId = 'eece45b4-3c81-451c-818b-08c31a2ab7a3' // PDF file
    const ledgerFileId = 'ce077564-e35d-4993-a178-c200a953dd43' // Excel file

    // Insert bank transactions
    const bankInserts = bankTransactions.map(tx => ({
      company_id: companyId,
      file_id: bankFileId,
      transaction_type: 'bank_statement',
      date: tx.date,
      amount: parseFloat(tx.amount) || 0,
      description: tx.description,
      reference: tx.reference,
      balance: parseFloat(tx.balance) || 0,
      raw_data: tx
    }))

    // Insert ledger transactions
    const ledgerInserts = ledgerTransactions.map(tx => ({
      company_id: companyId,
      file_id: ledgerFileId,
      transaction_type: 'ledger_entry',
      date: tx.date,
      amount: parseFloat(tx.amount) || 0,
      description: tx.description,
      reference: tx.reference,
      account: tx.account_id,
      balance: parseFloat(tx.balance) || 0,
      raw_data: tx
    }))

    // Insert in batches
    console.log('   Inserting bank transactions...')
    const { error: bankError } = await supabase
      .from('transactions')
      .insert(bankInserts)

    if (bankError) {
      console.error('Error inserting bank transactions:', bankError)
      return
    }

    console.log('   Inserting ledger transactions...')
    const { error: ledgerError } = await supabase
      .from('transactions')
      .insert(ledgerInserts)

    if (ledgerError) {
      console.error('Error inserting ledger transactions:', ledgerError)
      return
    }

    console.log('✅ Transactions imported correctly')

    // 4. Verify the fix
    const { data: bankCount } = await supabase
      .from('transactions')
      .select('id', { count: 'exact' })
      .eq('company_id', companyId)
      .eq('transaction_type', 'bank_statement')

    const { data: ledgerCount } = await supabase
      .from('transactions')
      .select('id', { count: 'exact' })
      .eq('company_id', companyId)
      .eq('transaction_type', 'ledger_entry')

    console.log('\n📊 Final verification:')
    console.log(`   Bank statements: ${bankCount?.length || 0}`)
    console.log(`   Ledger entries: ${ledgerCount?.length || 0}`)
    console.log(`   Total: ${(bankCount?.length || 0) + (ledgerCount?.length || 0)}`)

    if ((bankCount?.length || 0) === bankTransactions.length &&
        (ledgerCount?.length || 0) === ledgerTransactions.length) {
      console.log('🎉 Transaction categorization fixed successfully!')
      console.log('\nNow reconciliation should find proper matches instead of 1000/1000')
    } else {
      console.log('❌ Transaction counts still incorrect')
    }

  } catch (error) {
    console.error('Fix failed:', error)
  }
}

fixTransactionDuplication()