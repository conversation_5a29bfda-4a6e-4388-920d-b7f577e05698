/**
 * API Endpoints Test Script
 * Tests all the reconciliation API endpoints with real data
 */

// Use built-in fetch if available (Node 18+), otherwise use node-fetch
const fetch = globalThis.fetch || require('node-fetch');

const BASE_URL = 'http://localhost:3001';

console.log('🧪 Testing API Endpoints');
console.log('========================');

// Helper function to make API requests
async function apiRequest(endpoint, options = {}) {
  const url = `${BASE_URL}${endpoint}`;
  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json',
    },
  };

  const response = await fetch(url, { ...defaultOptions, ...options });
  const data = await response.json();

  return {
    status: response.status,
    ok: response.ok,
    data
  };
}

async function testApiEndpoints() {
  console.log('\n🔍 Testing API Endpoints...');
  
  try {
    // Test 1: Get Reconciliation Data
    console.log('\n1️⃣ Testing GET /api/reconciliation');
    const reconciliationResponse = await apiRequest('/api/reconciliation');
    
    if (reconciliationResponse.ok) {
      console.log('✅ Reconciliation API working');
      console.log(`   Summary: ${JSON.stringify(reconciliationResponse.data.summary, null, 2)}`);
      console.log(`   Transactions: ${reconciliationResponse.data.transactions?.length || 0}`);
    } else {
      console.log('❌ Reconciliation API failed:', reconciliationResponse.data.error);
    }

    // Test 2: Start Transaction Matching
    console.log('\n2️⃣ Testing POST /api/match-transactions');
    const matchingResponse = await apiRequest('/api/match-transactions', {
      method: 'POST',
      body: JSON.stringify({
        options: {
          exactMatchFields: ['reference', 'date', 'amount'],
          fuzzyMatchThreshold: 70,
          dateToleranceDays: 3,
          amountTolerancePercentage: 5,
          descriptionSimilarityThreshold: 60
        }
      })
    });

    if (matchingResponse.ok) {
      console.log('✅ Transaction matching API working');
      console.log(`   Result: ${JSON.stringify(matchingResponse.data, null, 2)}`);
    } else {
      console.log('❌ Transaction matching API failed:', matchingResponse.data.error);
    }

    // Test 3: Get Reports
    console.log('\n3️⃣ Testing GET /api/reports');
    const reportsResponse = await apiRequest('/api/reports');
    
    if (reportsResponse.ok) {
      console.log('✅ Reports API working');
      console.log(`   Reports: ${reportsResponse.data.reports?.length || 0}`);
      console.log(`   Stats: ${JSON.stringify(reportsResponse.data.stats, null, 2)}`);
    } else {
      console.log('❌ Reports API failed:', reportsResponse.data.error);
    }

    // Test 4: Generate Report
    console.log('\n4️⃣ Testing POST /api/reports');
    const generateReportResponse = await apiRequest('/api/reports', {
      method: 'POST',
      body: JSON.stringify({
        reportType: 'reconciliation',
        reportName: 'Test Reconciliation Report',
        dateFrom: '2025-07-01',
        dateTo: '2025-07-31',
        includeJournalVouchers: true
      })
    });

    if (generateReportResponse.ok) {
      console.log('✅ Report generation API working');
      console.log(`   Report: ${JSON.stringify(generateReportResponse.data, null, 2)}`);
    } else {
      console.log('❌ Report generation API failed:', generateReportResponse.data.error);
    }

    // Test 5: Manual Match (if we have transactions)
    if (reconciliationResponse.ok && reconciliationResponse.data.transactions?.length >= 2) {
      console.log('\n5️⃣ Testing POST /api/reconciliation (manual match)');
      
      const transactions = reconciliationResponse.data.transactions;
      const bankTx = transactions.find(t => t.source === 'bank');
      const ledgerTx = transactions.find(t => t.source === 'ledger');

      if (bankTx && ledgerTx) {
        const manualMatchResponse = await apiRequest('/api/reconciliation', {
          method: 'POST',
          body: JSON.stringify({
            action: 'manual_match',
            bankTransactionId: bankTx.id,
            ledgerTransactionId: ledgerTx.id,
            notes: 'Test manual match'
          })
        });

        if (manualMatchResponse.ok) {
          console.log('✅ Manual matching API working');
          console.log(`   Result: ${JSON.stringify(manualMatchResponse.data, null, 2)}`);
        } else {
          console.log('❌ Manual matching API failed:', manualMatchResponse.data.error);
        }
      } else {
        console.log('⚠️  Skipping manual match test - no suitable transactions found');
      }
    }

    console.log('\n🎯 API Test Summary');
    console.log('==================');
    console.log('✅ All API endpoints tested');
    console.log('📊 Check the results above for any failures');
    
  } catch (error) {
    console.error('❌ API testing failed:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Troubleshooting:');
      console.log('1. Make sure the Next.js app is running: npm run dev');
      console.log('2. Check that the app is running on http://localhost:3000');
      console.log('3. Ensure the database is properly seeded');
    }
  }
}

// Check if the app is running
async function checkAppStatus() {
  try {
    const response = await fetch(`${BASE_URL}/api/health`);
    return response.ok;
  } catch (error) {
    // Try a basic endpoint if health doesn't exist
    try {
      const response = await fetch(`${BASE_URL}/api/reconciliation`);
      return true; // If we get any response, the server is running
    } catch (error2) {
      return false;
    }
  }
}

// Main execution
async function main() {
  console.log('🔍 Checking if app is running...');
  
  const isRunning = await checkAppStatus();
  if (!isRunning) {
    console.log(`❌ App is not running on ${BASE_URL}`);
    console.log('\n💡 To start the app:');
    console.log('1. Run: npm run dev');
    console.log('2. Wait for the app to start');
    console.log('3. Run this test script again');
    return;
  }

  console.log('✅ App is running, starting API tests...');
  await testApiEndpoints();
}

main();
