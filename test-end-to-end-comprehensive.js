#!/usr/bin/env node
/**
 * End-to-End Test Suite for Accounting Discrepancy Finder
 *
 * This script tests the complete workflow from authentication to report generation
 * using real Ethiopian financial data from RFSA documents.
 *
 * Test User: <EMAIL>
 * Password: Aa@********
 */

const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

// Polyfill fetch for Node.js
if (!globalThis.fetch) {
  globalThis.fetch = require('node-fetch')
  globalThis.FormData = require('form-data')
  globalThis.Blob = require('buffer').Blob
}

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseAnonKey || !supabaseServiceKey) {
  console.error('❌ Missing environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseAnonKey)
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey)

// Test configuration
const TEST_CONFIG = {
  user: {
    email: '<EMAIL>',
    password: 'Aa@********'
  },
  baseUrl: 'http://localhost:3000',
  testFiles: {
    bankStatement: path.join(__dirname, 'example-docs', 'RFSA bank statement July 2025_compressed.pdf'),
    ledger: path.join(__dirname, 'example-docs', 'RFSA_July_2025_CBE_bank_statement.xlsx')
  },
  resultsDir: path.join(__dirname, 'test-results'),
  timeout: 300000 // 5 minutes
}

class EndToEndTester {
  constructor() {
    this.results = {
      timestamp: new Date().toISOString(),
      user: TEST_CONFIG.user.email,
      tests: {},
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
        errors: []
      }
    }
    this.authSession = null
  }

  // Utility methods
  log(message, type = 'info') {
    const timestamp = new Date().toISOString()
    const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️'
    console.log(`${prefix} [${timestamp}] ${message}`)
  }

  async saveResult(testName, result) {
    this.results.tests[testName] = {
      ...result,
      timestamp: new Date().toISOString()
    }

    const resultPath = path.join(TEST_CONFIG.resultsDir, `${testName.toLowerCase().replace(/ /g, '-')}.json`)
    await fs.promises.writeFile(resultPath, JSON.stringify(result, null, 2))

    if (result.success) {
      this.results.summary.passed++
      this.log(`Test '${testName}' PASSED`, 'success')
    } else {
      this.results.summary.failed++
      this.results.summary.errors.push(result.error || 'Unknown error')
      this.log(`Test '${testName}' FAILED: ${result.error}`, 'error')
    }
    this.results.summary.total++
  }

  // Test 1: Authentication
  async testAuthentication() {
    this.log('Starting authentication test...')

    try {
      // Sign in with test credentials
      const { data, error } = await supabase.auth.signInWithPassword({
        email: TEST_CONFIG.user.email,
        password: TEST_CONFIG.user.password
      })

      if (error) {
        throw new Error(`Authentication failed: ${error.message}`)
      }

      if (!data.user) {
        throw new Error('No user data returned from authentication')
      }

      this.authSession = data.session

      // Verify user profile exists
      const { data: profile, error: profileError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', data.user.id)
        .single()

      if (profileError && profileError.code !== 'PGRST116') {
        throw new Error(`Profile check failed: ${profileError.message}`)
      }

      // Verify company association - simplified query
      const { data: companyUser, error: companyError } = await supabase
        .from('company_users')
        .select('*, company_id')
        .eq('user_id', data.user.id)
        .single()

      if (companyError) {
        throw new Error(`Company association check failed: ${companyError.message}`)
      }

      // Get company details separately
      const { data: company, error: companyDetailError } = await supabase
        .from('accounting_companies')
        .select('*')
        .eq('id', companyUser.company_id)
        .single()

      if (companyDetailError) {
        throw new Error(`Company details check failed: ${companyDetailError.message}`)
      }

      const result = {
        success: true,
        data: {
          userId: data.user.id,
          email: data.user.email,
          profileExists: !!profile,
          companyId: companyUser.company_id,
          companyName: company.name,
          sessionValid: !!data.session
        }
      }

      await this.saveResult('Authentication', result)
      return result

    } catch (error) {
      const result = {
        success: false,
        error: error.message,
        data: null
      }
      await this.saveResult('Authentication', result)
      return result
    }
  }

  // Test 2: File Upload
  async testFileUpload() {
    this.log('Starting file upload test...')

    try {
      if (!this.authSession) {
        throw new Error('No valid authentication session')
      }

      // Check if test files exist
      if (!fs.existsSync(TEST_CONFIG.testFiles.bankStatement)) {
        throw new Error('Bank statement test file not found')
      }
      if (!fs.existsSync(TEST_CONFIG.testFiles.ledger)) {
        throw new Error('Ledger test file not found')
      }

      const uploadResults = []

      // Upload bank statement
      const bankStatementBuffer = fs.readFileSync(TEST_CONFIG.testFiles.bankStatement)
      const bankFormData = new FormData()
      bankFormData.append('file', new Blob([bankStatementBuffer], { type: 'application/pdf' }), 'RFSA bank statement July 2025_compressed.pdf')

      const bankResponse = await fetch(`${TEST_CONFIG.baseUrl}/api/upload-file`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.authSession.access_token}`
        },
        body: bankFormData
      })

      if (!bankResponse.ok) {
        throw new Error(`Bank statement upload failed: ${bankResponse.statusText}`)
      }

      const bankResult = await bankResponse.json()
      uploadResults.push({ type: 'bank_statement', ...bankResult })

      // Upload ledger
      const ledgerBuffer = fs.readFileSync(TEST_CONFIG.testFiles.ledger)
      const ledgerFormData = new FormData()
      ledgerFormData.append('file', new Blob([ledgerBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }), 'RFSA_July_2025_CBE_bank_statement.xlsx')

      const ledgerResponse = await fetch(`${TEST_CONFIG.baseUrl}/api/upload-file`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.authSession.access_token}`
        },
        body: ledgerFormData
      })

      if (!ledgerResponse.ok) {
        throw new Error(`Ledger upload failed: ${ledgerResponse.statusText}`)
      }

      const ledgerResult = await ledgerResponse.json()
      uploadResults.push({ type: 'ledger', ...ledgerResult })

      // Verify files in database
      const { data: files, error: filesError } = await supabase
        .from('files')
        .select('*')
        .eq('status', 'uploaded')
        .order('created_at', { ascending: false })
        .limit(2)

      if (filesError) {
        throw new Error(`File verification failed: ${filesError.message}`)
      }

      const result = {
        success: true,
        data: {
          uploadResults,
          filesInDatabase: files.length,
          files: files.map(f => ({
            id: f.id,
            filename: f.original_filename,
            status: f.status,
            size: f.file_size
          }))
        }
      }

      await this.saveResult('File Upload', result)
      return result

    } catch (error) {
      const result = {
        success: false,
        error: error.message,
        data: null
      }
      await this.saveResult('File Upload', result)
      return result
    }
  }

  // Test 3: Processing Pipeline
  async testProcessingPipeline() {
    this.log('Starting processing pipeline test...')

    try {
      if (!this.authSession) {
        throw new Error('No valid authentication session')
      }

      // Trigger processing
      const processResponse = await fetch(`${TEST_CONFIG.baseUrl}/api/process-reconciliation`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.authSession.access_token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!processResponse.ok) {
        const errorText = await processResponse.text()
        throw new Error(`Processing failed: ${processResponse.statusText} - ${errorText}`)
      }

      const processResult = await processResponse.json()

      // Wait for processing to complete (with timeout)
      let processingComplete = false
      let attempts = 0
      const maxAttempts = 60 // 5 minutes with 5-second intervals

      while (!processingComplete && attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, 5000)) // Wait 5 seconds

        const { data: files, error: statusError } = await supabase
          .from('files')
          .select('status, processing_completed_at, processing_error')
          .eq('status', 'completed')

        if (statusError) {
          throw new Error(`Status check failed: ${statusError.message}`)
        }

        if (files.length >= 2) {
          processingComplete = true
        }
        attempts++
      }

      if (!processingComplete) {
        throw new Error('Processing did not complete within timeout period')
      }

      // Check transaction extraction results
      const { data: transactions, error: transactionError } = await supabase
        .from('transactions')
        .select('*')
        .order('created_at', { ascending: false })

      if (transactionError) {
        throw new Error(`Transaction check failed: ${transactionError.message}`)
      }

      const bankTransactions = transactions.filter(t => t.transaction_type === 'bank_statement')
      const ledgerTransactions = transactions.filter(t => t.transaction_type === 'ledger_entry')

      const result = {
        success: true,
        data: {
          processResult,
          totalTransactions: transactions.length,
          bankTransactions: bankTransactions.length,
          ledgerTransactions: ledgerTransactions.length,
          processingTime: attempts * 5, // seconds
          sampleTransactions: transactions.slice(0, 5).map(t => ({
            id: t.id,
            type: t.transaction_type,
            date: t.transaction_date,
            amount: t.amount,
            description: t.description
          }))
        }
      }

      await this.saveResult('Processing Pipeline', result)
      return result

    } catch (error) {
      const result = {
        success: false,
        error: error.message,
        data: null
      }
      await this.saveResult('Processing Pipeline', result)
      return result
    }
  }

  // Test 4: Reconciliation Workflow
  async testReconciliationWorkflow() {
    this.log('Starting reconciliation workflow test...')

    try {
      if (!this.authSession) {
        throw new Error('No valid authentication session')
      }

      // Trigger reconciliation
      const reconResponse = await fetch(`${TEST_CONFIG.baseUrl}/api/seamless-reconciliation`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.authSession.access_token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!reconResponse.ok) {
        const errorText = await reconResponse.text()
        throw new Error(`Reconciliation failed: ${reconResponse.statusText} - ${errorText}`)
      }

      const reconResult = await reconResponse.json()

      // Check reconciliation results in database
      const { data: reconciliations, error: reconError } = await supabase
        .from('reconciliations')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(1)

      if (reconError) {
        throw new Error(`Reconciliation check failed: ${reconError.message}`)
      }

      // Check for discrepancies
      const { data: discrepancies, error: discrepError } = await supabase
        .from('discrepancies')
        .select('*')
        .order('created_at', { ascending: false })

      if (discrepError) {
        throw new Error(`Discrepancy check failed: ${discrepError.message}`)
      }

      const result = {
        success: true,
        data: {
          reconResult,
          reconciliations: reconciliations.length,
          discrepancies: discrepancies.length,
          latestReconciliation: reconciliations[0] ? {
            id: reconciliations[0].id,
            status: reconciliations[0].status,
            summary: reconciliations[0].summary
          } : null,
          sampleDiscrepancies: discrepancies.slice(0, 3).map(d => ({
            id: d.id,
            type: d.discrepancy_type,
            amount: d.amount_difference,
            description: d.description
          }))
        }
      }

      await this.saveResult('Reconciliation Workflow', result)
      return result

    } catch (error) {
      const result = {
        success: false,
        error: error.message,
        data: null
      }
      await this.saveResult('Reconciliation Workflow', result)
      return result
    }
  }

  // Test 5: Report Generation
  async testReportGeneration() {
    this.log('Starting report generation test...')

    try {
      if (!this.authSession) {
        throw new Error('No valid authentication session')
      }

      // Get latest reconciliation
      const { data: reconciliations, error: reconError } = await supabase
        .from('reconciliations')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(1)

      if (reconError || !reconciliations.length) {
        throw new Error('No reconciliation found for report generation')
      }

      const reconciliationId = reconciliations[0].id

      // Generate report
      const reportResponse = await fetch(`${TEST_CONFIG.baseUrl}/api/reports`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.authSession.access_token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          reconciliation_id: reconciliationId,
          report_type: 'comprehensive'
        })
      })

      if (!reportResponse.ok) {
        const errorText = await reportResponse.text()
        throw new Error(`Report generation failed: ${reportResponse.statusText} - ${errorText}`)
      }

      const reportResult = await reportResponse.json()

      // Check report in database
      const { data: reports, error: reportError } = await supabase
        .from('reports')
        .select('*')
        .eq('reconciliation_id', reconciliationId)
        .order('created_at', { ascending: false })
        .limit(1)

      if (reportError) {
        throw new Error(`Report check failed: ${reportError.message}`)
      }

      const result = {
        success: true,
        data: {
          reportResult,
          reportId: reports[0]?.id,
          reportType: reports[0]?.report_type,
          reportStatus: reports[0]?.status,
          reportSummary: reports[0]?.summary,
          reportCreated: reports[0]?.created_at
        }
      }

      await this.saveResult('Report Generation', result)
      return result

    } catch (error) {
      const result = {
        success: false,
        error: error.message,
        data: null
      }
      await this.saveResult('Report Generation', result)
      return result
    }
  }

  // Run all tests
  async runAllTests() {
    this.log('🚀 Starting End-to-End Test Suite for Accounting Discrepancy Finder', 'info')
    this.log(`Test User: ${TEST_CONFIG.user.email}`)
    this.log(`Base URL: ${TEST_CONFIG.baseUrl}`)
    this.log('=' * 80)

    try {
      // Test 1: Authentication
      await this.testAuthentication()

      // Test 2: File Upload
      await this.testFileUpload()

      // Test 3: Processing Pipeline
      await this.testProcessingPipeline()

      // Test 4: Reconciliation Workflow
      await this.testReconciliationWorkflow()

      // Test 5: Report Generation
      await this.testReportGeneration()

      // Save final summary
      await this.saveFinalSummary()

      this.log('=' * 80)
      this.log(`🎉 Test Suite Complete! Passed: ${this.results.summary.passed}/${this.results.summary.total}`,
               this.results.summary.failed === 0 ? 'success' : 'warning')

      if (this.results.summary.failed > 0) {
        this.log('❌ Failures:', 'error')
        this.results.summary.errors.forEach(error => this.log(`  - ${error}`, 'error'))
      }

    } catch (error) {
      this.log(`💥 Test suite failed with critical error: ${error.message}`, 'error')
      process.exit(1)
    }
  }

  async saveFinalSummary() {
    const summaryPath = path.join(TEST_CONFIG.resultsDir, 'final-summary.json')
    await fs.promises.writeFile(summaryPath, JSON.stringify(this.results, null, 2))

    // Generate markdown report
    const markdownReport = this.generateMarkdownReport()
    const reportPath = path.join(TEST_CONFIG.resultsDir, 'test-report.md')
    await fs.promises.writeFile(reportPath, markdownReport)
  }

  generateMarkdownReport() {
    return `# End-to-End Test Report

**Generated:** ${this.results.timestamp}
**Test User:** ${this.results.user}
**Total Tests:** ${this.results.summary.total}
**Passed:** ${this.results.summary.passed}
**Failed:** ${this.results.summary.failed}

## Test Results

${Object.entries(this.results.tests).map(([testName, result]) => `
### ${testName}
- **Status:** ${result.success ? '✅ PASSED' : '❌ FAILED'}
- **Timestamp:** ${result.timestamp}
${result.error ? `- **Error:** ${result.error}` : ''}
${result.data ? `- **Data:** \`\`\`json\n${JSON.stringify(result.data, null, 2)}\n\`\`\`` : ''}
`).join('\n')}

## Summary

${this.results.summary.failed === 0 ?
  '🎉 All tests passed successfully! The accounting application is working correctly.' :
  `⚠️ ${this.results.summary.failed} test(s) failed. Please review the failures above.`
}

## Test Environment
- **Supabase URL:** ${supabaseUrl}
- **Database Tables:** accounting_companies, user_profiles, company_users, files, transactions, reconciliations, reports, discrepancies
- **Test Files:** RFSA Ethiopian Bank Statement and Ledger (July 2025)
- **Python Environment:** ${process.env.VIRTUAL_ENV || 'System Python'}
`
  }
}

// Run the test suite
async function main() {
  const tester = new EndToEndTester()
  await tester.runAllTests()
}

if (require.main === module) {
  main().catch(console.error)
}

module.exports = EndToEndTester