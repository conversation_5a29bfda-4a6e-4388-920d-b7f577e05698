# Accounting Discrepancy Finder: User Journey & Flow

## Current Issues Analysis

Based on the code review and transaction data extraction, we've identified several critical issues with the current application:

1. **Broken Workflow**: The app doesn't properly guide users through the reconciliation process
2. **Misleading Status Indicators**: Status labels don't accurately reflect the processing state
3. **Missing Transaction Processing**: Core functionality for transaction matching is incomplete
4. **Poor User Guidance**: Users don't know what to do after uploading files
5. **Disconnected Components**: The UI components exist but aren't properly connected to the backend

## Ideal User Journey

### 1. Authentication & Onboarding

**Current State**: Basic authentication exists but lacks proper onboarding.

**Ideal Flow**:
1. User signs up/logs in with Supabase Auth
2. First-time users see a guided tour of the reconciliation process
3. Company setup wizard ensures proper data isolation
4. Clear dashboard shows reconciliation status and pending tasks

### 2. Document Upload

**Current State**: Upload functionality exists but doesn't properly guide users to the next step.

**Ideal Flow**:
1. User navigates to Upload page with clear instructions
2. Selects document type (bank statement or ledger)
3. Uploads file with drag-and-drop or file picker
4. Sees immediate feedback on upload success
5. Clear "Process Now" or "Upload More" options presented
6. Status changes to "Uploaded" (not misleadingly labeled as "Completed")

### 3. Document Processing

**Current State**: Processing exists but doesn't provide adequate feedback or next steps.

**Ideal Flow**:
1. User initiates processing from either Upload confirmation or Files page
2. Clear progress indicator shows processing stages:
   - Uploading to secure storage
   - Extracting with AI (Mistral for PDFs, Gemini for Excel)
   - Parsing transactions
   - Storing in database
3. Real-time updates via TanStack Query
4. Upon completion, status changes to "Extracted" with transaction count
5. Clear "Start Reconciliation" button appears

### 4. Transaction Matching

**Current State**: This critical functionality is missing entirely.

**Ideal Flow**:
1. User navigates to Reconciliation page
2. System automatically matches transactions based on:
   - Reference numbers (exact matches)
   - Date + Amount combinations (fuzzy matches)
   - Description similarity (for complex cases)
3. Matching statistics displayed (matched, unmatched, potential matches)
4. Interactive interface to review and confirm/reject potential matches
5. Ability to manually match transactions
6. Status updates to "Matched" with match percentage

### 5. Discrepancy Review

**Current State**: This functionality is missing entirely.

**Ideal Flow**:
1. User navigates to Review page
2. Clear categorization of discrepancies:
   - Bank-only transactions (not in ledger)
   - Ledger-only transactions (not in bank)
   - Amount mismatches (same reference but different amounts)
   - Date discrepancies (timing differences)
3. Filtering and sorting options
4. Ability to add notes and categorize discrepancies
5. Journal voucher recommendations for corrections
6. Status updates to "Reviewed"

### 6. Report Generation

**Current State**: This functionality is missing entirely.

**Ideal Flow**:
1. User navigates to Reports page
2. Generates reconciliation report with:
   - Summary statistics
   - Matched transactions
   - Unmatched transactions
   - Discrepancy details
   - Journal voucher recommendations
3. Export options (PDF, Excel, CSV)
4. Option to share with team members
5. Status updates to "Completed"

## Implementation in App Components

### 1. Workflow Progress Component

The `WorkflowProgress` component already exists but needs proper integration:

```tsx
// Current implementation in workflow-progress.tsx
export const RECONCILIATION_STEPS: WorkflowStep[] = [
  {
    id: 'upload',
    title: 'Upload Documents',
    description: 'Upload bank statements and ledger files',
    status: 'pending'
  },
  {
    id: 'processing',
    title: 'Extract Transactions',
    description: 'AI processes documents to extract transaction data',
    status: 'pending'
  },
  {
    id: 'matching',
    title: 'Match Transactions',
    description: 'Automatically match bank and ledger transactions',
    status: 'pending'
  },
  {
    id: 'review',
    title: 'Review Results',
    description: 'Review matches and resolve discrepancies',
    status: 'pending'
  },
  {
    id: 'complete',
    title: 'Generate Reports',
    description: 'Export reconciliation reports and journal vouchers',
    status: 'pending'
  }
]
```

This component should be displayed on all dashboard pages with the current step highlighted.

### 2. Status Mapping

The status mapping utility exists but needs proper implementation across the app:

```typescript
// Current implementation in status-mapping.ts
export type DatabaseStatus = 'uploaded' | 'uploading' | 'processing' | 'completed' | 'failed';
export type UIStatus = 'uploaded' | 'uploading' | 'processing' | 'extracted' | 'failed' | 'no_files' | 'files_uploaded' | 'ready_for_reconciliation';

export const mapDatabaseStatusToUIStatus = (status: DatabaseStatus): UIStatus => {
  switch (status) {
    case 'completed':
      return 'extracted'; // Map DB 'completed' to UI 'extracted'
    case 'uploaded':
      return 'files_uploaded'; // Map DB 'uploaded' to UI 'files_uploaded'
    case 'uploading':
    case 'processing':
    case 'failed':
      return status;
    default:
      return 'uploaded';
  }
};
```

This needs to be expanded to include all workflow states:
- `matched` - After transaction matching
- `reviewed` - After discrepancy review
- `report_generated` - After report generation

### 3. Next Steps Card

The `NextStepsCard` component exists but needs proper integration with the workflow:

```tsx
// Current implementation in next-steps-card.tsx
export const getNextSteps = (scenario: UIStatus): NextStep[] => {
  switch (scenario) {
    case 'no_files':
      return [
        {
          id: 'upload_bank',
          title: 'Upload Bank Statement',
          description: 'Upload your PDF bank statement for the period you want to reconcile',
          action: 'Upload Now',
          href: '/dashboard/upload?type=bank',
          priority: 'high',
          icon: Upload,
          estimated: '2 min'
        },
        // ...
      ]
    // ...
  }
}
```

This needs to be expanded to include guidance for all workflow states, particularly the missing reconciliation steps.

### 4. Document Processing

The document processing functionality exists but needs better error handling and user feedback:

```typescript
// Current implementation in document-processor.ts
async processDocument(request: DocumentProcessingRequest): Promise<ProcessingResponse> {
  // ...
  if (result.success && result.data?.transactions) {
    // Store transactions in database
    await this.storeTransactions(
      request.fileId,
      request.companyId,
      result.data.transactions,
      request.documentType === 'ledger_entry' ? 'ledger_entry' : 'bank_statement',
      result.data
    )

    // Update file status to completed
    await this.updateFileStatus(request.fileId, 'completed', {
      transaction_count: result.data.transactions.length,
      processing_time: result.processingTime,
      ai_model: aiModel
    })
    // ...
  }
  // ...
}
```

This needs to be enhanced with:
- Better progress reporting
- Handling of partial success cases
- Retry mechanisms for failed extractions
- Preview of extracted data

### 5. Transaction Matching (Missing)

A new component needs to be created for transaction matching:

```typescript
// Proposed implementation
interface MatchingResult {
  exactMatches: number;
  fuzzyMatches: number;
  unmatched: {
    bank: number;
    ledger: number;
  };
  matchedTransactions: MatchedTransaction[];
  unmatchedBankTransactions: Transaction[];
  unmatchedLedgerTransactions: Transaction[];
  potentialMatches: PotentialMatch[];
}

async function matchTransactions(
  bankTransactions: Transaction[],
  ledgerTransactions: Transaction[]
): Promise<MatchingResult> {
  // Implementation of matching algorithm
}
```

### 6. Discrepancy Review (Missing)

A new component needs to be created for discrepancy review:

```typescript
// Proposed implementation
interface Discrepancy {
  type: 'bank_only' | 'ledger_only' | 'amount_mismatch' | 'date_discrepancy';
  bankTransaction?: Transaction;
  ledgerTransaction?: Transaction;
  difference?: number;
  notes?: string;
  recommendation?: string;
}

function analyzeDiscrepancies(matchingResult: MatchingResult): Discrepancy[] {
  // Implementation of discrepancy analysis
}
```

### 7. Report Generation (Missing)

A new component needs to be created for report generation:

```typescript
// Proposed implementation
interface ReconciliationReport {
  summary: {
    period: string;
    bankName: string;
    accountNumber: string;
    totalBankTransactions: number;
    totalLedgerTransactions: number;
    matchedTransactions: number;
    unmatchedTransactions: number;
    discrepancies: number;
  };
  matchedTransactions: MatchedTransaction[];
  unmatchedBankTransactions: Transaction[];
  unmatchedLedgerTransactions: Transaction[];
  discrepancies: Discrepancy[];
  journalVouchers: JournalVoucher[];
}

function generateReport(
  matchingResult: MatchingResult,
  discrepancies: Discrepancy[]
): ReconciliationReport {
  // Implementation of report generation
}
```

## Technical Implementation Plan

### 1. Fix Status Mapping and UI Components

- Update status types to include all workflow states
- Ensure consistent status mapping across the application
- Fix UI components to accurately reflect the current state

### 2. Implement Transaction Matching

- Create a new API endpoint for transaction matching
- Implement matching algorithms (exact, fuzzy, and heuristic)
- Develop UI for reviewing and confirming matches

### 3. Implement Discrepancy Review

- Create a new API endpoint for discrepancy analysis
- Develop UI for reviewing and categorizing discrepancies
- Implement journal voucher recommendation engine

### 4. Implement Report Generation

- Create a new API endpoint for report generation
- Develop UI for customizing and generating reports
- Implement export functionality

### 5. Connect Workflow Components

- Ensure WorkflowProgress component reflects the current state
- Update NextStepsCard to provide guidance for all states
- Implement proper navigation between workflow steps

## Conclusion

The Accounting Discrepancy Finder application has a solid foundation with its Next.js, Supabase, and E2B architecture, but it's missing critical functionality for the core reconciliation workflow. By implementing the missing components and fixing the existing ones according to this user journey document, the application can become a powerful tool for automating bank statement and ledger reconciliation.

The most critical issues to address are:

1. Implementing the transaction matching functionality
2. Creating the discrepancy review interface
3. Developing the report generation capability
4. Ensuring proper workflow guidance through the UI

With these improvements, users will have a clear, guided experience from upload to final reconciliation report, eliminating the current confusion and frustration.
