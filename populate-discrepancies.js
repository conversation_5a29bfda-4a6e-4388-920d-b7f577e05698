import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://kqgdscnfpqbqvgdtdoeg.supabase.co';
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.rucj2xjD1NrDQgAKvJcqq-VH0iE9-XDDmTh0Ixb06vA';

const supabase = createClient(supabaseUrl, supabaseKey);

async function populateDiscrepancies() {
  const companyId = '5277bdf7-bbeb-4cab-bcbb-e55ee177b4dd';

  console.log('Starting discrepancy population...');

  // Clear existing discrepancies
  await supabase
    .from('discrepancies')
    .delete()
    .eq('company_id', companyId);

  console.log('Cleared existing discrepancies');

  // Get unmatched bank transactions
  const { data: unmatchedBankTxs, error: bankError } = await supabase
    .from('transactions')
    .select('*')
    .eq('company_id', companyId)
    .eq('transaction_type', 'bank_statement')
    .not('id', 'in', `(
      SELECT bank_transaction_id
      FROM reconciliations
      WHERE company_id = '${companyId}'
        AND bank_transaction_id IS NOT NULL
    )`)
    .limit(20); // Start with 20 for testing

  if (bankError) {
    console.error('Error fetching unmatched bank transactions:', bankError);
    return;
  }

  console.log(`Found ${unmatchedBankTxs?.length || 0} unmatched bank transactions`);

  // Insert bank-only discrepancies
  if (unmatchedBankTxs && unmatchedBankTxs.length > 0) {
    for (const tx of unmatchedBankTxs) {
      const { error: insertError } = await supabase
        .from('discrepancies')
        .insert({
          company_id: companyId,
          discrepancy_type: 'bank_only',
          amount: tx.amount,
          description: `Bank transaction not found in ledger: ${tx.description || 'No description'}`,
          bank_transaction_id: tx.id,
          status: 'pending',
          severity: Math.abs(tx.amount) > 100000 ? 'critical' :
                   Math.abs(tx.amount) > 50000 ? 'high' :
                   Math.abs(tx.amount) > 10000 ? 'medium' : 'low',
          metadata: {
            bank_date: tx.date,
            bank_amount: tx.amount,
            bank_reference: tx.reference,
            bank_description: tx.description,
            suggested_action: Math.abs(tx.amount) > 10000 ? 'manual_review' : 'investigate'
          }
        });

      if (insertError) {
        console.error('Error inserting bank discrepancy:', insertError);
      }
    }
    console.log(`Inserted ${unmatchedBankTxs.length} bank-only discrepancies`);
  }

  // Get unmatched ledger transactions
  const { data: unmatchedLedgerTxs, error: ledgerError } = await supabase
    .from('transactions')
    .select('*')
    .eq('company_id', companyId)
    .eq('transaction_type', 'ledger_entry')
    .not('id', 'in', `(
      SELECT ledger_transaction_id
      FROM reconciliations
      WHERE company_id = '${companyId}'
        AND ledger_transaction_id IS NOT NULL
    )`)
    .limit(20); // Start with 20 for testing

  if (ledgerError) {
    console.error('Error fetching unmatched ledger transactions:', ledgerError);
    return;
  }

  console.log(`Found ${unmatchedLedgerTxs?.length || 0} unmatched ledger transactions`);

  // Insert ledger-only discrepancies
  if (unmatchedLedgerTxs && unmatchedLedgerTxs.length > 0) {
    for (const tx of unmatchedLedgerTxs) {
      const { error: insertError } = await supabase
        .from('discrepancies')
        .insert({
          company_id: companyId,
          discrepancy_type: 'ledger_only',
          amount: tx.amount,
          description: `Ledger entry not found in bank statement: ${tx.description || 'No description'}`,
          ledger_transaction_id: tx.id,
          status: 'pending',
          severity: Math.abs(tx.amount) > 100000 ? 'critical' :
                   Math.abs(tx.amount) > 50000 ? 'high' :
                   Math.abs(tx.amount) > 10000 ? 'medium' : 'low',
          metadata: {
            ledger_date: tx.date,
            ledger_amount: tx.amount,
            ledger_reference: tx.reference,
            ledger_description: tx.description,
            suggested_action: Math.abs(tx.amount) > 10000 ? 'manual_review' : 'investigate'
          }
        });

      if (insertError) {
        console.error('Error inserting ledger discrepancy:', insertError);
      }
    }
    console.log(`Inserted ${unmatchedLedgerTxs.length} ledger-only discrepancies`);
  }

  // Get final count
  const { data: finalDiscrepancies } = await supabase
    .from('discrepancies')
    .select('discrepancy_type')
    .eq('company_id', companyId);

  console.log(`\nFinal discrepancy count: ${finalDiscrepancies?.length || 0}`);
  if (finalDiscrepancies) {
    const bankOnly = finalDiscrepancies.filter(d => d.discrepancy_type === 'bank_only').length;
    const ledgerOnly = finalDiscrepancies.filter(d => d.discrepancy_type === 'ledger_only').length;
    console.log(`- Bank-only: ${bankOnly}`);
    console.log(`- Ledger-only: ${ledgerOnly}`);
  }

  console.log('Discrepancy population complete!');
}

populateDiscrepancies().catch(console.error);