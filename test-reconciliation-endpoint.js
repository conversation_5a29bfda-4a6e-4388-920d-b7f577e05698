async function testReconciliation() {
  try {
    console.log('🧪 Testing reconciliation endpoint...')

    // This would normally require authentication, but let's test the endpoint logic
    const response = await fetch('http://localhost:3000/api/seamless-reconciliation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    const result = await response.text()
    console.log('Response status:', response.status)
    console.log('Response:', result)

    if (response.status === 401) {
      console.log('✅ Expected authentication error - endpoint is accessible')
      console.log('The fix should resolve the "No uploaded files found" error when properly authenticated')
    } else if (response.status === 404) {
      console.log('❌ Still getting 404 - the file status fix may not be working')
    } else {
      console.log('🎉 Unexpected response - check the output above')
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
}

testReconciliation()