const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

async function testAndFixReconciliation() {
  console.log('🔧 Testing and fixing reconciliation function...')

  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY
  )

  try {
    // First, let's test the current function
    console.log('📋 Testing current function...')
    const { data: companies } = await supabase
      .from('accounting_companies')
      .select('id')
      .limit(1)

    if (!companies || companies.length === 0) {
      console.error('❌ No companies found')
      return
    }

    const companyId = companies[0].id
    console.log(`🏢 Using company ID: ${companyId}`)

    // Try the function call
    const { data: testData, error: testError } = await supabase
      .rpc('get_reconciliation_transactions', {
        p_company_id: companyId,
        p_limit: 5
      })

    if (testError) {
      console.error('❌ Current function failed:', testError)

      // Apply the fix
      console.log('🔨 Applying database fix...')
      const { readFileSync } = require('fs')
      const fixSQL = readFileSync('./fix-reconciliation-function-v2.sql', 'utf8')

      // Use psql command to execute the SQL directly
      const { execSync } = require('child_process')
      const fs = require('fs')

      // Write the SQL to a temporary file
      fs.writeFileSync('/tmp/fix-reconciliation.sql', fixSQL)

      try {
        // Extract database connection info from Supabase URL
        const dbUrl = process.env.NEXT_PUBLIC_SUPABASE_URL.replace('https://', '').replace('.supabase.co', '')
        const dbHost = `db.${dbUrl}.supabase.co`

        console.log('🔗 Applying fix via direct database connection...')

        // Note: This would require database connection details that aren't available in the environment
        console.log('⚠️  Database fix needs to be applied manually through Supabase dashboard or direct psql connection')
        console.log('📝 SQL file ready at: fix-reconciliation-function-v2.sql')

      } catch (execError) {
        console.log('⚠️  Could not apply fix automatically, please run the SQL manually')
        console.log('📁 Fix available in: fix-reconciliation-function-v2.sql')
      }

    } else {
      console.log('✅ Current function works!')
      console.log('📊 Sample data:', testData?.[0] ? Object.keys(testData[0]) : 'No data')
    }

    // Check reconciliation summary
    console.log('📈 Checking reconciliation summary...')
    const { data: summary, error: summaryError } = await supabase
      .rpc('get_reconciliation_summary', {
        p_company_id: companyId
      })

    if (summaryError) {
      console.error('❌ Summary function failed:', summaryError)
    } else {
      console.log('✅ Summary function works!')
      console.log('📊 Summary:', summary?.[0])
    }

  } catch (error) {
    console.error('❌ Script error:', error)
  }
}

testAndFixReconciliation()