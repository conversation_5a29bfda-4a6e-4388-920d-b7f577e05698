#!/bin/bash

echo "🔧 Applying reconciliation function fix..."

# Check if we have the Supabase CLI
if command -v supabase &> /dev/null; then
    echo "📋 Found Supabase CLI, applying fix..."
    supabase db exec --file fix-reconciliation-function-v2.sql --local
    if [ $? -eq 0 ]; then
        echo "✅ Database fix applied successfully!"
    else
        echo "❌ Failed to apply fix via Supabase CLI"
    fi
else
    echo "⚠️  Supabase CLI not found"
    echo "📝 Please apply the SQL file manually:"
    echo "   1. Open your Supabase dashboard"
    echo "   2. Go to SQL Editor"
    echo "   3. Run the contents of fix-reconciliation-function-v2.sql"
    echo ""
    echo "📄 SQL contents:"
    cat fix-reconciliation-function-v2.sql
fi

echo ""
echo "🧪 Testing the fix..."
node test-and-fix-reconciliation.js