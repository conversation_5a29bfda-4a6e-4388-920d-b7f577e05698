#!/usr/bin/env node
/**
 * Test Seamless Reconciliation API
 */

const { createClient } = require('@supabase/supabase-js')

// Polyfill fetch for Node.js
if (!globalThis.fetch) {
  globalThis.fetch = require('node-fetch')
}

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function testSeamlessReconciliation() {
  console.log('🚀 Testing Seamless Reconciliation API...')

  try {
    // Authenticate first
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'Aa@12345678'
    })

    if (authError) {
      throw new Error(`Authentication failed: ${authError.message}`)
    }

    console.log('✅ Authentication successful')

    // Get access token
    const accessToken = authData.session.access_token

    // Call seamless reconciliation API
    const response = await fetch('http://localhost:3000/api/seamless-reconciliation', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      }
    })

    console.log('📡 API Response Status:', response.status)

    if (!response.ok) {
      const errorText = await response.text()
      console.error('❌ API Error:', errorText)
      return
    }

    const result = await response.json()
    console.log('✅ Seamless Reconciliation Result:')
    console.log(JSON.stringify(result, null, 2))

    // Check if processing started
    if (result.reconciliation_id) {
      console.log('🔄 Processing started, reconciliation ID:', result.reconciliation_id)

      // Monitor progress for a bit
      let attempts = 0
      const maxAttempts = 30 // 2.5 minutes

      while (attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, 5000)) // Wait 5 seconds

        const { data: reconciliation, error: checkError } = await supabase
          .from('reconciliations')
          .select('*')
          .eq('id', result.reconciliation_id)
          .single()

        if (checkError) {
          console.log('⚠️ Status check error:', checkError.message)
        } else {
          console.log(`📊 Status: ${reconciliation.status} | ${reconciliation.summary?.total_matches || 0} matches found`)

          if (reconciliation.status === 'completed') {
            console.log('🎉 Reconciliation completed!')
            console.log('Summary:', reconciliation.summary)
            break
          }
        }

        attempts++
      }
    }

  } catch (error) {
    console.error('💥 Error:', error.message)
  }
}

testSeamlessReconciliation()