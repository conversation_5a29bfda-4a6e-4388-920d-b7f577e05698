// Copy and paste this entire script into your browser console
// at http://localhost:3002/dashboard/seamless

// Force show the Start Reconciliation button
(function() {
  console.log('🔧 Applying emergency fix to show Start Reconciliation button...');
  
  // Create a big green button if one doesn't exist
  function createStartButton() {
    console.log('Creating new Start Reconciliation button...');
    
    // Find the upload zone
    const uploadZone = document.querySelector('.space-y-3');
    if (!uploadZone) {
      console.error('❌ Could not find upload zone');
      return;
    }
    
    // Create button container
    const buttonContainer = document.createElement('div');
    buttonContainer.className = 'mt-6 text-center';
    
    // Create button
    const button = document.createElement('button');
    button.className = 'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-green-600 hover:bg-green-700 text-white px-12 py-4 text-lg h-10 px-4 py-2 animate-pulse';
    button.style.fontSize = '18px';
    button.style.padding = '16px 48px';
    button.style.height = 'auto';
    
    // Add play icon
    const playIcon = document.createElement('span');
    playIcon.className = 'mr-3';
    playIcon.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-play"><polygon points="5 3 19 12 5 21 5 3"></polygon></svg>';
    button.appendChild(playIcon);
    
    // Add text
    const buttonText = document.createTextNode('Start Reconciliation');
    button.appendChild(buttonText);
    
    // Add click handler
    button.onclick = function() {
      console.log('🚀 Starting reconciliation...');
      button.disabled = true;
      button.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-refresh-cw animate-spin mr-3"><path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"></path><path d="M21 3v5h-5"></path><path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"></path><path d="M3 21v-5h5"></path></svg> Processing...';
      
      // Call the actual reconciliation function
      try {
        // Try to find and call the original function
        const event = new MouseEvent('click', {
          bubbles: true,
          cancelable: true,
          view: window
        });
        
        // Find any button that might be the real one
        const allButtons = document.querySelectorAll('button');
        let found = false;
        
        for (const btn of allButtons) {
          if (btn.innerText.includes('Start Reconciliation') && btn !== button) {
            console.log('Found original button, clicking it...');
            btn.dispatchEvent(event);
            found = true;
            break;
          }
        }
        
        if (!found) {
          // If we can't find the button, try to call the function directly
          console.log('No original button found, trying to call function directly...');
          
          // This will work if the page has a startSeamlessReconciliation function
          if (typeof window.startSeamlessReconciliation === 'function') {
            window.startSeamlessReconciliation();
          } else {
            // Last resort - reload the page with a special parameter
            window.location.href = '/dashboard/seamless?start=true';
          }
        }
      } catch (error) {
        console.error('Error starting reconciliation:', error);
        button.disabled = false;
        button.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-play mr-3"><polygon points="5 3 19 12 5 21 5 3"></polygon></svg> Start Reconciliation';
      }
    };
    
    // Add button to container
    buttonContainer.appendChild(button);
    
    // Add container after upload zone
    uploadZone.appendChild(buttonContainer);
    
    console.log('✅ Start Reconciliation button created successfully!');
  }
  
  // Try to find and show existing button first
  const buttons = document.querySelectorAll('button');
  let found = false;
  
  for (const button of buttons) {
    if (button.innerText.includes('Start Reconciliation')) {
      button.style.display = 'inline-flex';
      button.disabled = false;
      button.classList.add('animate-pulse');
      console.log('✅ Found and enabled existing Start Reconciliation button!');
      found = true;
      break;
    }
  }
  
  // If no button found, create one
  if (!found) {
    createStartButton();
  }
  
  console.log('🎉 Fix applied! You should now see the Start Reconciliation button.');
})();
