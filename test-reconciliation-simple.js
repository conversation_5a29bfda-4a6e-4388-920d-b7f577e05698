const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

async function testReconciliationAPI() {
  console.log('🧪 Testing reconciliation API...')

  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY
  )

  try {
    // Get a company ID to test with
    const { data: companies } = await supabase
      .from('accounting_companies')
      .select('id')
      .limit(1)

    if (!companies || companies.length === 0) {
      console.error('❌ No companies found')
      return
    }

    const companyId = companies[0].id
    console.log(`🏢 Testing with company ID: ${companyId}`)

    // Test the reconciliation function
    console.log('📋 Testing get_reconciliation_transactions function...')
    const { data: transactions, error: transError } = await supabase
      .rpc('get_reconciliation_transactions', {
        p_company_id: companyId,
        p_limit: 5
      })

    if (transError) {
      console.log('❌ get_reconciliation_transactions failed:', transError.message)
      console.log('🔧 Error details:', transError)
    } else {
      console.log('✅ get_reconciliation_transactions works!')
      console.log('📊 Returned', transactions?.length || 0, 'transactions')
      if (transactions?.[0]) {
        console.log('📋 Sample transaction keys:', Object.keys(transactions[0]))
      }
    }

    // Test the reconciliation summary
    console.log('📈 Testing get_reconciliation_summary function...')
    const { data: summary, error: summaryError } = await supabase
      .rpc('get_reconciliation_summary', {
        p_company_id: companyId
      })

    if (summaryError) {
      console.log('❌ get_reconciliation_summary failed:', summaryError.message)
    } else {
      console.log('✅ get_reconciliation_summary works!')
      console.log('📊 Summary:', summary?.[0])
    }

    // Check what we have in the database
    console.log('📊 Checking database state...')
    const { data: reconciliations } = await supabase
      .from('reconciliations')
      .select('status')
      .eq('company_id', companyId)

    console.log(`🔍 Found ${reconciliations?.length || 0} reconciliations`)
    if (reconciliations?.length > 0) {
      const statusCounts = reconciliations.reduce((acc, r) => {
        acc[r.status] = (acc[r.status] || 0) + 1
        return acc
      }, {})
      console.log('📈 Status breakdown:', statusCounts)
    }

  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

testReconciliationAPI()