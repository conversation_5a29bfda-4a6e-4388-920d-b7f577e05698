#!/usr/bin/env node

/**
 * Quick OCR Test Script
 * 
 * This script tests the Mistral OCR extraction with a provided API key
 * and verifies the complete end-to-end workflow.
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Helper function to run shell commands
function runCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    const process = spawn(command, args, { 
      stdio: 'inherit',
      env: { ...process.env, ...options.env }
    });
    
    process.on('close', (code) => {
      resolve({ code });
    });
    
    process.on('error', (error) => {
      reject(error);
    });
  });
}

async function testOCRExtraction() {
  console.log('🧪 Testing OCR Extraction with Mistral API');
  console.log('==========================================');
  
  // Check if API key is provided as argument
  const apiKey = process.argv[2];
  if (!apiKey) {
    console.log('❌ Please provide your Mistral API key as an argument');
    console.log('Usage: node test-ocr-with-api-key.js YOUR_MISTRAL_API_KEY');
    console.log('');
    console.log('🔗 Get your API key from: https://console.mistral.ai/');
    process.exit(1);
  }
  
  console.log('✅ API key provided');
  console.log('🔄 Running OCR extraction...');
  
  const scriptPath = path.join(__dirname, 'src/lib/e2b/extract_transactions_ocr.py');
  
  try {
    const result = await runCommand('python3', [scriptPath], {
      env: { MISTRAL_API_KEY: apiKey }
    });
    
    if (result.code === 0) {
      console.log('✅ OCR extraction completed!');
      await verifyResults();
    } else {
      console.log('❌ OCR extraction failed');
      process.exit(1);
    }
  } catch (error) {
    console.log('❌ Error running extraction:', error.message);
    process.exit(1);
  }
}

async function verifyResults() {
  console.log('\n🔍 Verifying Results');
  console.log('====================');
  
  const dataDir = path.join(__dirname, 'src/lib/e2b/extracted_data');
  const bankFile = path.join(dataDir, 'bank_transactions.json');
  
  if (fs.existsSync(bankFile)) {
    const bankData = JSON.parse(fs.readFileSync(bankFile, 'utf8'));
    console.log(`📊 Extracted ${bankData.length} bank transactions`);
    
    if (bankData.length > 5) {
      console.log('🎉 SUCCESS: Much better than the previous 5 transactions!');
      
      // Show first few transactions
      console.log('\n📋 Sample transactions:');
      bankData.slice(0, 5).forEach((tx, i) => {
        console.log(`   ${i + 1}. ${tx.date} - ${tx.description.substring(0, 30)}... - $${tx.amount || tx.credit || tx.debit || 0}`);
      });
      
      console.log('\n🚀 Ready to test the complete workflow!');
      console.log('Next steps:');
      console.log('1. npm run dev');
      console.log('2. Visit http://localhost:3000/dashboard');
      console.log('3. Check Files page for updated counts');
      console.log('4. Test reconciliation workflow');
      
    } else {
      console.log('⚠️  Still only got 5 or fewer transactions');
      console.log('This might indicate OCR issues');
    }
  } else {
    console.log('❌ Bank transactions file not found');
  }
}

if (require.main === module) {
  testOCRExtraction();
}
