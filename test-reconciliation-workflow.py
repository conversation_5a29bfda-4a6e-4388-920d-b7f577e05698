#!/usr/bin/env python3
"""
Comprehensive test of the reconciliation workflow end-to-end
This will test transaction matching, discrepancy detection, and report generation
"""

import json
import os
from datetime import datetime
import pandas as pd
from collections import defaultdict
import difflib

class ReconciliationEngine:
    """Complete reconciliation engine for testing"""
    
    def __init__(self):
        self.matches = []
        self.unmatched_bank = []
        self.unmatched_ledger = []
        self.discrepancies = []
    
    def load_transactions(self, bank_file, ledger_file):
        """Load transaction data from JSON files"""
        with open(bank_file, 'r') as f:
            self.bank_transactions = json.load(f)
        
        with open(ledger_file, 'r') as f:
            self.ledger_transactions = json.load(f)
        
        print(f"Loaded {len(self.bank_transactions)} bank transactions")
        print(f"Loaded {len(self.ledger_transactions)} ledger transactions")
    
    def match_by_reference(self):
        """Match transactions by reference number (highest confidence)"""
        print("Matching by reference numbers...")
        
        # Create reference lookup for bank transactions
        bank_refs = {}
        for i, txn in enumerate(self.bank_transactions):
            ref = str(txn.get('reference', '')).strip()
            if ref and ref != 'nan':
                bank_refs[ref] = (i, txn)
        
        matched_ledger_indices = set()
        
        for i, ledger_txn in enumerate(self.ledger_transactions):
            ref = str(ledger_txn.get('reference', '')).strip()
            if ref and ref != 'nan' and ref in bank_refs:
                bank_idx, bank_txn = bank_refs[ref]
                
                match = {
                    'type': 'reference_match',
                    'confidence': 0.95,
                    'bank_transaction': bank_txn,
                    'ledger_transaction': ledger_txn,
                    'bank_index': bank_idx,
                    'ledger_index': i,
                    'reference': ref
                }
                
                # Check for amount discrepancies
                bank_amount = float(bank_txn.get('amount', 0))
                ledger_amount = float(ledger_txn.get('amount', 0))
                
                if abs(bank_amount - ledger_amount) > 0.01:  # Allow for rounding
                    match['amount_discrepancy'] = {
                        'bank_amount': bank_amount,
                        'ledger_amount': ledger_amount,
                        'difference': bank_amount - ledger_amount
                    }
                
                self.matches.append(match)
                matched_ledger_indices.add(i)
                
                # Remove from bank_refs to avoid duplicate matches
                del bank_refs[ref]
        
        print(f"Found {len(self.matches)} reference matches")
        return matched_ledger_indices
    
    def match_by_date_amount(self, matched_ledger_indices):
        """Match remaining transactions by date and amount"""
        print("Matching by date and amount...")
        
        # Get unmatched bank transactions
        matched_bank_indices = {match['bank_index'] for match in self.matches}
        unmatched_bank = [(i, txn) for i, txn in enumerate(self.bank_transactions) 
                         if i not in matched_bank_indices]
        
        # Get unmatched ledger transactions
        unmatched_ledger = [(i, txn) for i, txn in enumerate(self.ledger_transactions) 
                           if i not in matched_ledger_indices]
        
        # Create date-amount lookup for bank transactions
        bank_lookup = defaultdict(list)
        for i, txn in unmatched_bank:
            date = txn.get('date', '')
            amount = float(txn.get('amount', 0))
            key = f"{date}_{amount:.2f}"
            bank_lookup[key].append((i, txn))
        
        new_matched_ledger = set()
        
        for i, ledger_txn in unmatched_ledger:
            date = ledger_txn.get('date', '')
            amount = float(ledger_txn.get('amount', 0))
            key = f"{date}_{amount:.2f}"
            
            if key in bank_lookup and bank_lookup[key]:
                bank_idx, bank_txn = bank_lookup[key].pop(0)
                
                match = {
                    'type': 'date_amount_match',
                    'confidence': 0.85,
                    'bank_transaction': bank_txn,
                    'ledger_transaction': ledger_txn,
                    'bank_index': bank_idx,
                    'ledger_index': i,
                    'match_key': key
                }
                
                self.matches.append(match)
                new_matched_ledger.add(i)
        
        print(f"Found {len(new_matched_ledger)} date-amount matches")
        return matched_ledger_indices.union(new_matched_ledger)
    
    def fuzzy_match_descriptions(self, matched_ledger_indices):
        """Fuzzy match remaining transactions by description similarity"""
        print("Performing fuzzy matching on descriptions...")
        
        # Get remaining unmatched transactions
        matched_bank_indices = {match['bank_index'] for match in self.matches}
        unmatched_bank = [(i, txn) for i, txn in enumerate(self.bank_transactions) 
                         if i not in matched_bank_indices]
        
        unmatched_ledger = [(i, txn) for i, txn in enumerate(self.ledger_transactions) 
                           if i not in matched_ledger_indices]
        
        new_matches = []
        
        for i, ledger_txn in unmatched_ledger:
            ledger_desc = str(ledger_txn.get('description', '')).lower()
            best_match = None
            best_ratio = 0
            
            for j, bank_txn in unmatched_bank:
                bank_desc = str(bank_txn.get('description', '')).lower()
                
                # Calculate similarity ratio
                ratio = difflib.SequenceMatcher(None, ledger_desc, bank_desc).ratio()
                
                if ratio > best_ratio and ratio > 0.6:  # 60% similarity threshold
                    best_ratio = ratio
                    best_match = (j, bank_txn)
            
            if best_match:
                bank_idx, bank_txn = best_match
                
                match = {
                    'type': 'fuzzy_match',
                    'confidence': best_ratio,
                    'bank_transaction': bank_txn,
                    'ledger_transaction': ledger_txn,
                    'bank_index': bank_idx,
                    'ledger_index': i,
                    'similarity_ratio': best_ratio
                }
                
                new_matches.append(match)
                matched_ledger_indices.add(i)
                
                # Remove from unmatched_bank to avoid duplicate matches
                unmatched_bank = [(idx, txn) for idx, txn in unmatched_bank if idx != bank_idx]
        
        self.matches.extend(new_matches)
        print(f"Found {len(new_matches)} fuzzy matches")
        return matched_ledger_indices
    
    def identify_unmatched_transactions(self, matched_ledger_indices):
        """Identify transactions that couldn't be matched"""
        print("Identifying unmatched transactions...")
        
        matched_bank_indices = {match['bank_index'] for match in self.matches}
        
        self.unmatched_bank = [txn for i, txn in enumerate(self.bank_transactions) 
                              if i not in matched_bank_indices]
        
        self.unmatched_ledger = [txn for i, txn in enumerate(self.ledger_transactions) 
                                if i not in matched_ledger_indices]
        
        print(f"Unmatched bank transactions: {len(self.unmatched_bank)}")
        print(f"Unmatched ledger transactions: {len(self.unmatched_ledger)}")
    
    def analyze_discrepancies(self):
        """Analyze discrepancies in matched transactions"""
        print("Analyzing discrepancies...")
        
        for match in self.matches:
            if 'amount_discrepancy' in match:
                self.discrepancies.append({
                    'type': 'amount_discrepancy',
                    'match': match,
                    'discrepancy': match['amount_discrepancy']
                })
        
        print(f"Found {len(self.discrepancies)} amount discrepancies")
    
    def generate_reconciliation_report(self):
        """Generate comprehensive reconciliation report"""
        print("Generating reconciliation report...")
        
        # Calculate balances
        bank_balance = sum(float(txn.get('amount', 0)) for txn in self.bank_transactions)
        ledger_balance = sum(float(txn.get('amount', 0)) for txn in self.ledger_transactions)
        
        # Calculate matched amounts
        matched_bank_amount = sum(float(match['bank_transaction'].get('amount', 0)) 
                                 for match in self.matches)
        matched_ledger_amount = sum(float(match['ledger_transaction'].get('amount', 0)) 
                                   for match in self.matches)
        
        # Calculate unmatched amounts
        unmatched_bank_amount = sum(float(txn.get('amount', 0)) for txn in self.unmatched_bank)
        unmatched_ledger_amount = sum(float(txn.get('amount', 0)) for txn in self.unmatched_ledger)
        
        report = {
            'reconciliation_summary': {
                'total_bank_transactions': len(self.bank_transactions),
                'total_ledger_transactions': len(self.ledger_transactions),
                'matched_transactions': len(self.matches),
                'unmatched_bank_transactions': len(self.unmatched_bank),
                'unmatched_ledger_transactions': len(self.unmatched_ledger),
                'match_percentage': (len(self.matches) / max(len(self.bank_transactions), len(self.ledger_transactions))) * 100
            },
            'balance_analysis': {
                'bank_balance': bank_balance,
                'ledger_balance': ledger_balance,
                'balance_difference': bank_balance - ledger_balance,
                'matched_bank_amount': matched_bank_amount,
                'matched_ledger_amount': matched_ledger_amount,
                'unmatched_bank_amount': unmatched_bank_amount,
                'unmatched_ledger_amount': unmatched_ledger_amount
            },
            'match_breakdown': {
                'reference_matches': len([m for m in self.matches if m['type'] == 'reference_match']),
                'date_amount_matches': len([m for m in self.matches if m['type'] == 'date_amount_match']),
                'fuzzy_matches': len([m for m in self.matches if m['type'] == 'fuzzy_match'])
            },
            'discrepancies': {
                'count': len(self.discrepancies),
                'total_amount_difference': sum(d['discrepancy']['difference'] for d in self.discrepancies),
                'details': self.discrepancies
            },
            'unmatched_transactions': {
                'bank': self.unmatched_bank[:10],  # First 10 for brevity
                'ledger': self.unmatched_ledger[:10]
            },
            'recommendations': self.generate_recommendations()
        }
        
        return report
    
    def generate_recommendations(self):
        """Generate recommendations based on reconciliation results"""
        recommendations = []
        
        if len(self.unmatched_bank) > 0:
            recommendations.append({
                'type': 'unmatched_bank_transactions',
                'message': f"Review {len(self.unmatched_bank)} unmatched bank transactions",
                'action': 'Check if these are bank fees, interest, or missing ledger entries'
            })
        
        if len(self.unmatched_ledger) > 0:
            recommendations.append({
                'type': 'unmatched_ledger_transactions',
                'message': f"Review {len(self.unmatched_ledger)} unmatched ledger transactions",
                'action': 'Check if these are pending transactions or data entry errors'
            })
        
        if len(self.discrepancies) > 0:
            recommendations.append({
                'type': 'amount_discrepancies',
                'message': f"Resolve {len(self.discrepancies)} amount discrepancies",
                'action': 'Review transactions with amount differences for data entry errors'
            })
        
        return recommendations
    
    def run_complete_reconciliation(self, bank_file, ledger_file):
        """Run the complete reconciliation workflow"""
        print("=" * 60)
        print("STARTING COMPLETE RECONCILIATION WORKFLOW")
        print("=" * 60)
        
        # Load data
        self.load_transactions(bank_file, ledger_file)
        
        # Step 1: Match by reference
        matched_indices = self.match_by_reference()
        
        # Step 2: Match by date and amount
        matched_indices = self.match_by_date_amount(matched_indices)
        
        # Step 3: Fuzzy match by description
        matched_indices = self.fuzzy_match_descriptions(matched_indices)
        
        # Step 4: Identify unmatched
        self.identify_unmatched_transactions(matched_indices)
        
        # Step 5: Analyze discrepancies
        self.analyze_discrepancies()
        
        # Step 6: Generate report
        report = self.generate_reconciliation_report()
        
        return report

def main():
    """Main test function"""
    
    # File paths
    bank_file = "/Users/<USER>/Desktop/projects/acounting-app/src/lib/e2b/extracted_data/bank_transactions_complete.json"
    ledger_file = "/Users/<USER>/Desktop/projects/acounting-app/src/lib/e2b/extracted_data/ledger_transactions_complete.json"
    
    # Check if complete extraction files exist, otherwise use current ones
    if not os.path.exists(bank_file):
        bank_file = "/Users/<USER>/Desktop/projects/acounting-app/src/lib/e2b/extracted_data/bank_transactions.json"
    
    if not os.path.exists(ledger_file):
        ledger_file = "/Users/<USER>/Desktop/projects/acounting-app/src/lib/e2b/extracted_data/ledger_transactions.json"
    
    print(f"Using bank file: {bank_file}")
    print(f"Using ledger file: {ledger_file}")
    
    # Run reconciliation
    engine = ReconciliationEngine()
    report = engine.run_complete_reconciliation(bank_file, ledger_file)
    
    # Save test results
    test_results_dir = "/Users/<USER>/Desktop/projects/acounting-app/test-results"
    os.makedirs(test_results_dir, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Save detailed report
    report_file = os.path.join(test_results_dir, f"reconciliation_report_{timestamp}.json")
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    # Save summary
    summary_file = os.path.join(test_results_dir, f"reconciliation_summary_{timestamp}.txt")
    with open(summary_file, 'w') as f:
        f.write("RECONCILIATION TEST RESULTS\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"Test Date: {datetime.now().isoformat()}\n")
        f.write(f"Bank File: {bank_file}\n")
        f.write(f"Ledger File: {ledger_file}\n\n")
        
        f.write("SUMMARY:\n")
        f.write(f"Bank Transactions: {report['reconciliation_summary']['total_bank_transactions']}\n")
        f.write(f"Ledger Transactions: {report['reconciliation_summary']['total_ledger_transactions']}\n")
        f.write(f"Matched Transactions: {report['reconciliation_summary']['matched_transactions']}\n")
        f.write(f"Match Percentage: {report['reconciliation_summary']['match_percentage']:.1f}%\n\n")
        
        f.write("BALANCE ANALYSIS:\n")
        f.write(f"Bank Balance: {report['balance_analysis']['bank_balance']:,.2f} ETB\n")
        f.write(f"Ledger Balance: {report['balance_analysis']['ledger_balance']:,.2f} ETB\n")
        f.write(f"Difference: {report['balance_analysis']['balance_difference']:,.2f} ETB\n\n")
        
        f.write("MATCH BREAKDOWN:\n")
        f.write(f"Reference Matches: {report['match_breakdown']['reference_matches']}\n")
        f.write(f"Date+Amount Matches: {report['match_breakdown']['date_amount_matches']}\n")
        f.write(f"Fuzzy Matches: {report['match_breakdown']['fuzzy_matches']}\n\n")
        
        f.write("DISCREPANCIES:\n")
        f.write(f"Count: {report['discrepancies']['count']}\n")
        f.write(f"Total Amount Difference: {report['discrepancies']['total_amount_difference']:,.2f} ETB\n\n")
        
        f.write("RECOMMENDATIONS:\n")
        for rec in report['recommendations']:
            f.write(f"- {rec['message']}: {rec['action']}\n")
    
    print("=" * 60)
    print("RECONCILIATION TEST COMPLETED")
    print("=" * 60)
    print(f"Results saved to: {test_results_dir}")
    print(f"Detailed report: {report_file}")
    print(f"Summary: {summary_file}")
    print("=" * 60)
    
    # Print key metrics
    print("KEY RESULTS:")
    print(f"Bank Transactions: {report['reconciliation_summary']['total_bank_transactions']}")
    print(f"Ledger Transactions: {report['reconciliation_summary']['total_ledger_transactions']}")
    print(f"Matched: {report['reconciliation_summary']['matched_transactions']} ({report['reconciliation_summary']['match_percentage']:.1f}%)")
    print(f"Balance Difference: {report['balance_analysis']['balance_difference']:,.2f} ETB")
    print(f"Discrepancies: {report['discrepancies']['count']}")

if __name__ == "__main__":
    main()
