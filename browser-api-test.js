// Quick API test to trigger reconciliation with existing files
async function testReconciliationDirectly() {
  console.log('🧪 Testing Fixed Reconciliation Algorithm via API')

  try {
    // Get workflow status to see current state
    const statusResponse = await fetch('/api/workflow-status')
    if (!statusResponse.ok) {
      throw new Error(`Status API failed: ${statusResponse.status}`)
    }

    const status = await statusResponse.json()
    console.log('📊 Current Status:', status.summary)

    // Get file IDs from the status
    const fileIds = status.files ? status.files.map(f => f.id) : []
    console.log(`🔄 Found ${fileIds.length} files to process`)

    if (fileIds.length === 0) {
      console.log('❌ No files found to process')
      return
    }

    // Trigger reconciliation
    console.log('🚀 Triggering reconciliation with fixed algorithm...')
    const reconResponse = await fetch('/api/process-reconciliation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ fileIds })
    })

    const result = await reconResponse.text()
    console.log('📤 API Response Status:', reconResponse.status)
    console.log('📄 API Response:', result)

    // Check new status after processing
    const newStatusResponse = await fetch('/api/workflow-status')
    if (newStatusResponse.ok) {
      const newStatus = await newStatusResponse.json()
      console.log('📊 New Status:', newStatus.summary)

      // Highlight the key metrics
      console.log('\n🎯 KEY METRICS:')
      console.log(`   Bank Transactions: ${newStatus.summary.bankTransactions}`)
      console.log(`   Ledger Transactions: ${newStatus.summary.ledgerTransactions}`)
      console.log(`   Matched Transactions: ${newStatus.summary.matchedTransactions}`)
      console.log(`   Total Reconciliation Records: ${newStatus.summary.totalReconciliationRecords || 'N/A'}`)

      if (newStatus.summary.matchedTransactions <= Math.min(newStatus.summary.bankTransactions, newStatus.summary.ledgerTransactions)) {
        console.log('✅ SUCCESS: No over-matching detected!')
      } else {
        console.log('❌ PROBLEM: Still showing impossible match counts')
      }
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
}

// Run the test
testReconciliationDirectly()