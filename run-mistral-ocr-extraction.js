#!/usr/bin/env node

/**
 * Mistral OCR Extraction Runner
 * 
 * This script runs the Mistral OCR extraction to replace the inaccurate 
 * bank statement data with properly extracted transactions from the PDF.
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting Mistral OCR Extraction');
console.log('==================================');

// Check if Mistral API key is set
if (!process.env.MISTRAL_API_KEY) {
  console.log('❌ MISTRAL_API_KEY environment variable not set!');
  console.log('');
  console.log('💡 To set your API key:');
  console.log('   export MISTRAL_API_KEY="your-mistral-api-key-here"');
  console.log('');
  console.log('🔗 Get your API key from: https://console.mistral.ai/');
  process.exit(1);
}

// Check if required Python packages are installed
async function checkPythonDependencies() {
  console.log('🔍 Checking Python dependencies...');
  
  const requiredPackages = [
    'pdf2image',
    'Pillow',
    'requests',
    'pandas',
    'openpyxl'
  ];
  
  for (const pkg of requiredPackages) {
    try {
      const result = await runCommand('python3', ['-c', `import ${pkg.replace('-', '_').toLowerCase()}`]);
      if (result.code !== 0) {
        throw new Error(`Package ${pkg} not found`);
      }
    } catch (error) {
      console.log(`❌ Missing Python package: ${pkg}`);
      console.log('');
      console.log('💡 To install required packages:');
      console.log('   pip3 install pdf2image Pillow requests pandas openpyxl');
      console.log('');
      console.log('📦 On macOS, you also need poppler:');
      console.log('   brew install poppler');
      process.exit(1);
    }
  }
  
  console.log('✅ All Python dependencies are installed');
}

// Check if poppler is installed (required for pdf2image)
async function checkPoppler() {
  console.log('🔍 Checking poppler installation...');
  
  try {
    const result = await runCommand('pdftoppm', ['-h']);
    console.log('✅ Poppler is installed');
  } catch (error) {
    console.log('❌ Poppler not found!');
    console.log('');
    console.log('💡 To install poppler:');
    console.log('   macOS: brew install poppler');
    console.log('   Ubuntu: sudo apt-get install poppler-utils');
    console.log('   Windows: Download from https://poppler.freedesktop.org/');
    process.exit(1);
  }
}

// Helper function to run shell commands
function runCommand(command, args = []) {
  return new Promise((resolve, reject) => {
    const process = spawn(command, args, { stdio: 'pipe' });
    
    let stdout = '';
    let stderr = '';
    
    process.stdout.on('data', (data) => {
      stdout += data.toString();
    });
    
    process.stderr.on('data', (data) => {
      stderr += data.toString();
    });
    
    process.on('close', (code) => {
      resolve({ code, stdout, stderr });
    });
    
    process.on('error', (error) => {
      reject(error);
    });
  });
}

// Run the OCR extraction
async function runOCRExtraction() {
  console.log('🔄 Running Mistral OCR extraction...');
  console.log('This may take 2-5 minutes depending on the PDF size...');
  
  const scriptPath = path.join(__dirname, 'src/lib/e2b/extract_transactions_ocr.py');
  
  try {
    const result = await runCommand('python3', [scriptPath]);
    
    if (result.code === 0) {
      console.log('✅ OCR extraction completed successfully!');
      console.log('');
      console.log('📊 Output:');
      console.log(result.stdout);
      
      if (result.stderr) {
        console.log('⚠️  Warnings:');
        console.log(result.stderr);
      }
    } else {
      console.log('❌ OCR extraction failed!');
      console.log('');
      console.log('Error output:');
      console.log(result.stderr);
      console.log('');
      console.log('Standard output:');
      console.log(result.stdout);
      process.exit(1);
    }
  } catch (error) {
    console.log('❌ Failed to run OCR extraction script!');
    console.log('Error:', error.message);
    process.exit(1);
  }
}

// Verify the extracted data
async function verifyExtractedData() {
  console.log('🔍 Verifying extracted data...');
  
  const dataDir = path.join(__dirname, 'src/lib/e2b/extracted_data');
  const bankTransactionsFile = path.join(dataDir, 'bank_transactions.json');
  const ledgerTransactionsFile = path.join(dataDir, 'ledger_transactions.json');
  const combinedFile = path.join(dataDir, 'combined_transactions.json');
  
  try {
    // Check bank transactions
    if (fs.existsSync(bankTransactionsFile)) {
      const bankData = JSON.parse(fs.readFileSync(bankTransactionsFile, 'utf8'));
      console.log(`✅ Bank transactions: ${bankData.length} transactions extracted`);
      
      if (bankData.length > 5) {
        console.log('🎉 SUCCESS: Extracted significantly more transactions than the previous 5!');
      } else {
        console.log('⚠️  Warning: Still only extracted 5 or fewer transactions');
      }
    } else {
      console.log('❌ Bank transactions file not found');
    }
    
    // Check ledger transactions
    if (fs.existsSync(ledgerTransactionsFile)) {
      const ledgerData = JSON.parse(fs.readFileSync(ledgerTransactionsFile, 'utf8'));
      console.log(`✅ Ledger transactions: ${ledgerData.length} transactions extracted`);
    } else {
      console.log('❌ Ledger transactions file not found');
    }
    
    // Check combined data
    if (fs.existsSync(combinedFile)) {
      const combinedData = JSON.parse(fs.readFileSync(combinedFile, 'utf8'));
      console.log(`✅ Combined data: ${combinedData.metadata?.bank_transaction_count || 0} bank + ${combinedData.metadata?.ledger_transaction_count || 0} ledger transactions`);
      console.log(`📅 Extraction method: ${combinedData.metadata?.extraction_method || 'unknown'}`);
      console.log(`🕐 Extraction date: ${combinedData.metadata?.extraction_date || 'unknown'}`);
    } else {
      console.log('❌ Combined transactions file not found');
    }
    
  } catch (error) {
    console.log('❌ Error verifying extracted data:', error.message);
  }
}

// Show next steps
function showNextSteps() {
  console.log('');
  console.log('🎯 Next Steps:');
  console.log('==============');
  console.log('1. Start the development server: npm run dev');
  console.log('2. Navigate to: http://localhost:3000/dashboard');
  console.log('3. Check the Files page to see updated transaction counts');
  console.log('4. Test the reconciliation workflow with the new data');
  console.log('5. Run API tests: node test-api-endpoints.js');
  console.log('');
  console.log('🔗 The app should now work with accurate OCR-extracted data!');
}

// Main execution
async function main() {
  try {
    await checkPythonDependencies();
    await checkPoppler();
    await runOCRExtraction();
    await verifyExtractedData();
    showNextSteps();
  } catch (error) {
    console.log('❌ Extraction failed:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { main };
