#!/usr/bin/env node

/**
 * Extract Real Ethiopian Transaction Data
 * 
 * This script extracts actual transaction data from the Ethiopian RFSA documents
 * using Mistral AI for PDF processing and Gemini for Excel processing.
 * 
 * Documents:
 * - RFSA bank statement July 2025_compressed.pdf
 * - RFSA_July_2025_CBE_bank_statement.xlsx
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { GoogleGenerativeAI } from '@google/generative-ai';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const MISTRAL_API_KEY = process.env.MISTRAL_API_KEY;
const GEMINI_API_KEY = process.env.GOOGLE_GENERATIVE_AI_API_KEY;

if (!MISTRAL_API_KEY || !GEMINI_API_KEY) {
    console.error('❌ Missing API keys. Please set MISTRAL_API_KEY and GOOGLE_GENERATIVE_AI_API_KEY in .env.local');
    process.exit(1);
}

// Initialize Gemini
const genAI = new GoogleGenerativeAI(GEMINI_API_KEY);

// File paths
const EXAMPLE_DOCS_DIR = path.join(__dirname, 'example-docs');
const EXTRACTED_DATA_DIR = path.join(__dirname, 'src/lib/e2b/extracted_data');
const BANK_STATEMENT_PDF = path.join(EXAMPLE_DOCS_DIR, 'RFSA bank statement July 2025_compressed.pdf');
const LEDGER_EXCEL = path.join(EXAMPLE_DOCS_DIR, 'RFSA_July_2025_CBE_bank_statement.xlsx');

console.log('🇪🇹 Extracting Real Ethiopian Transaction Data');
console.log('===============================================');

/**
 * Extract transactions from PDF bank statement using Mistral Vision API
 */
async function extractBankTransactionsFromPDF() {
    console.log('\n📄 Processing Bank Statement PDF with Mistral AI...');
    
    try {
        // Convert PDF to images first (we'll use a Python script for this)
        const pdfToImageScript = `
import fitz  # PyMuPDF
import base64
import io
from PIL import Image
import json

def pdf_to_images(pdf_path):
    """Convert PDF pages to base64 encoded images"""
    doc = fitz.open(pdf_path)
    images = []
    
    for page_num in range(len(doc)):
        page = doc.load_page(page_num)
        pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))  # 2x zoom for better quality
        img_data = pix.tobytes("png")
        
        # Convert to base64
        img_base64 = base64.b64encode(img_data).decode('utf-8')
        images.append({
            'page': page_num + 1,
            'image': img_base64
        })
    
    doc.close()
    return images

# Process the PDF
pdf_path = "${BANK_STATEMENT_PDF}"
images = pdf_to_images(pdf_path)
print(json.dumps({'images': images}))
`;

        // Write and execute Python script
        fs.writeFileSync('/tmp/pdf_to_images.py', pdfToImageScript);
        
        const { spawn } = await import('child_process');
        
        return new Promise((resolve, reject) => {
            const python = spawn('python3', ['/tmp/pdf_to_images.py']);
            let output = '';
            let error = '';
            
            python.stdout.on('data', (data) => {
                output += data.toString();
            });
            
            python.stderr.on('data', (data) => {
                error += data.toString();
            });
            
            python.on('close', async (code) => {
                if (code !== 0) {
                    console.error('❌ PDF processing failed:', error);
                    reject(new Error(error));
                    return;
                }
                
                try {
                    const result = JSON.parse(output);
                    console.log(`✅ Converted PDF to ${result.images.length} images`);
                    
                    // Now process with Mistral Vision API
                    const transactions = await processPDFImagesWithMistral(result.images);
                    resolve(transactions);
                } catch (parseError) {
                    console.error('❌ Failed to parse PDF conversion result:', parseError);
                    reject(parseError);
                }
            });
        });
        
    } catch (error) {
        console.error('❌ Error processing bank statement:', error);
        throw error;
    }
}

/**
 * Process PDF images with Mistral Vision API
 */
async function processPDFImagesWithMistral(images) {
    console.log('🔍 Analyzing bank statement images with Mistral Vision...');
    
    const prompt = `
You are analyzing an Ethiopian bank statement from RFSA (Rural Financial Services Association) for July 2025.

Extract ALL transactions from this bank statement image. For each transaction, provide:
- date (YYYY-MM-DD format)
- description (transaction description)
- reference (reference number if available)
- debit (amount if it's a debit/withdrawal, 0 otherwise)
- credit (amount if it's a credit/deposit, 0 otherwise)
- balance (running balance after transaction)
- amount (positive for credits, negative for debits)

Return ONLY a valid JSON array of transaction objects. Include the opening balance as the first transaction.

Example format:
[
  {
    "date": "2025-07-01",
    "description": "Opening Balance",
    "reference": "",
    "debit": 0,
    "credit": 0,
    "balance": 1234.56,
    "amount": 0
  },
  {
    "date": "2025-07-02",
    "description": "Salary Payment",
    "reference": "SAL001",
    "debit": 0,
    "credit": 5000.00,
    "balance": 6234.56,
    "amount": 5000.00
  }
]

Extract ALL transactions visible in the image. Be precise with amounts and dates.
`;

    try {
        const allTransactions = [];
        
        for (let i = 0; i < images.length; i++) {
            console.log(`📊 Processing page ${i + 1}/${images.length}...`);
            
            const response = await fetch('https://api.mistral.ai/v1/chat/completions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${MISTRAL_API_KEY}`
                },
                body: JSON.stringify({
                    model: 'pixtral-12b-2409',
                    messages: [
                        {
                            role: 'user',
                            content: [
                                {
                                    type: 'text',
                                    text: prompt
                                },
                                {
                                    type: 'image_url',
                                    image_url: {
                                        url: `data:image/png;base64,${images[i].image}`
                                    }
                                }
                            ]
                        }
                    ],
                    max_tokens: 4000
                })
            });
            
            if (!response.ok) {
                throw new Error(`Mistral API error: ${response.statusText}`);
            }
            
            const result = await response.json();
            const content = result.choices[0].message.content;
            
            try {
                // Extract JSON from the response
                const jsonMatch = content.match(/\[[\s\S]*\]/);
                if (jsonMatch) {
                    const pageTransactions = JSON.parse(jsonMatch[0]);
                    allTransactions.push(...pageTransactions);
                    console.log(`✅ Extracted ${pageTransactions.length} transactions from page ${i + 1}`);
                }
            } catch (parseError) {
                console.error(`❌ Failed to parse transactions from page ${i + 1}:`, parseError);
                console.log('Raw response:', content);
            }
        }
        
        // Remove duplicates and sort by date
        const uniqueTransactions = removeDuplicateTransactions(allTransactions);
        uniqueTransactions.sort((a, b) => new Date(a.date) - new Date(b.date));
        
        console.log(`✅ Total bank transactions extracted: ${uniqueTransactions.length}`);
        return uniqueTransactions;
        
    } catch (error) {
        console.error('❌ Mistral API error:', error);
        throw error;
    }
}

/**
 * Extract transactions from Excel ledger using Gemini
 */
async function extractLedgerTransactionsFromExcel() {
    console.log('\n📊 Processing Ledger Excel with Gemini AI...');
    
    try {
        // First, convert Excel to CSV for easier processing
        const excelToCsvScript = `
import pandas as pd
import json
import sys

def excel_to_data(excel_path):
    """Convert Excel file to structured data"""
    try:
        # Try different sheet names and formats
        sheets_to_try = [0, 'Sheet1', 'Ledger', 'Transactions', 'July 2025']
        
        for sheet in sheets_to_try:
            try:
                df = pd.read_excel(excel_path, sheet_name=sheet)
                
                # Convert to JSON for easier handling
                data = df.to_dict('records')
                
                # Clean up the data
                cleaned_data = []
                for row in data:
                    cleaned_row = {}
                    for key, value in row.items():
                        if pd.notna(value):
                            cleaned_row[str(key).strip()] = str(value).strip() if isinstance(value, str) else value
                    if cleaned_row:  # Only add non-empty rows
                        cleaned_data.append(cleaned_row)
                
                return {
                    'success': True,
                    'data': cleaned_data,
                    'columns': list(df.columns),
                    'sheet_used': sheet
                }
            except Exception as e:
                continue
        
        return {'success': False, 'error': 'No readable sheets found'}
        
    except Exception as e:
        return {'success': False, 'error': str(e)}

# Process the Excel file
excel_path = "${LEDGER_EXCEL}"
result = excel_to_data(excel_path)
print(json.dumps(result))
`;

        // Write and execute Python script
        fs.writeFileSync('/tmp/excel_to_data.py', excelToCsvScript);
        
        const { spawn } = await import('child_process');
        
        return new Promise((resolve, reject) => {
            const python = spawn('python3', ['/tmp/excel_to_data.py']);
            let output = '';
            let error = '';
            
            python.stdout.on('data', (data) => {
                output += data.toString();
            });
            
            python.stderr.on('data', (data) => {
                error += data.toString();
            });
            
            python.on('close', async (code) => {
                if (code !== 0) {
                    console.error('❌ Excel processing failed:', error);
                    reject(new Error(error));
                    return;
                }
                
                try {
                    const result = JSON.parse(output);
                    
                    if (!result.success) {
                        throw new Error(result.error);
                    }
                    
                    console.log(`✅ Loaded Excel data from sheet: ${result.sheet_used}`);
                    console.log(`📋 Columns found: ${result.columns.join(', ')}`);
                    
                    // Now process with Gemini
                    const transactions = await processExcelDataWithGemini(result.data, result.columns);
                    resolve(transactions);
                } catch (parseError) {
                    console.error('❌ Failed to parse Excel conversion result:', parseError);
                    reject(parseError);
                }
            });
        });
        
    } catch (error) {
        console.error('❌ Error processing ledger:', error);
        throw error;
    }
}

/**
 * Process Excel data with Gemini AI
 */
async function processExcelDataWithGemini(excelData, columns) {
    console.log('🤖 Analyzing ledger data with Gemini AI...');
    
    const prompt = `
You are analyzing an Ethiopian company ledger from RFSA for July 2025.

The Excel data contains the following columns: ${columns.join(', ')}

Here is a sample of the data:
${JSON.stringify(excelData.slice(0, 10), null, 2)}

Extract ALL ledger transactions from this data. For each transaction, provide:
- date (YYYY-MM-DD format)
- description (transaction description)
- reference (reference number, journal voucher number, etc.)
- journal (journal type: GJ for General Journal, PJ for Purchase Journal, SJ for Sales Journal, etc.)
- debit (debit amount, 0 if credit)
- credit (credit amount, 0 if debit)
- balance (running balance)
- amount (positive for credits, negative for debits)

Return ONLY a valid JSON array of transaction objects. Include opening balance if present.

Example format:
[
  {
    "date": "2025-07-01",
    "description": "Opening Balance",
    "reference": "",
    "journal": "GJ",
    "debit": 0,
    "credit": 0,
    "balance": 1234.56,
    "amount": 0
  },
  {
    "date": "2025-07-02",
    "description": "Office Supplies Purchase",
    "reference": "PJ001",
    "journal": "PJ",
    "debit": 500.00,
    "credit": 0,
    "balance": 734.56,
    "amount": -500.00
  }
]

Process ALL rows of data provided. Be precise with amounts, dates, and references.
`;

    try {
        const model = genAI.getGenerativeModel({ model: 'gemini-1.5-pro' });
        
        const fullPrompt = prompt + '\n\nFull data to process:\n' + JSON.stringify(excelData, null, 2);
        
        const result = await model.generateContent(fullPrompt);
        const response = await result.response;
        const content = response.text();
        
        // Extract JSON from the response
        const jsonMatch = content.match(/\[[\s\S]*\]/);
        if (!jsonMatch) {
            throw new Error('No valid JSON found in Gemini response');
        }
        
        const transactions = JSON.parse(jsonMatch[0]);
        
        // Sort by date
        transactions.sort((a, b) => new Date(a.date) - new Date(b.date));
        
        console.log(`✅ Total ledger transactions extracted: ${transactions.length}`);
        return transactions;
        
    } catch (error) {
        console.error('❌ Gemini API error:', error);
        throw error;
    }
}

/**
 * Remove duplicate transactions based on date, amount, and reference
 */
function removeDuplicateTransactions(transactions) {
    const seen = new Set();
    return transactions.filter(txn => {
        const key = `${txn.date}-${txn.amount}-${txn.reference}`;
        if (seen.has(key)) {
            return false;
        }
        seen.add(key);
        return true;
    });
}

/**
 * Save extracted data to JSON files
 */
function saveExtractedData(bankTransactions, ledgerTransactions) {
    console.log('\n💾 Saving extracted data...');
    
    // Ensure directory exists
    if (!fs.existsSync(EXTRACTED_DATA_DIR)) {
        fs.mkdirSync(EXTRACTED_DATA_DIR, { recursive: true });
    }
    
    // Save bank transactions
    const bankFile = path.join(EXTRACTED_DATA_DIR, 'bank_transactions.json');
    fs.writeFileSync(bankFile, JSON.stringify(bankTransactions, null, 2));
    console.log(`✅ Bank transactions saved: ${bankFile} (${bankTransactions.length} transactions)`);
    
    // Save ledger transactions
    const ledgerFile = path.join(EXTRACTED_DATA_DIR, 'ledger_transactions.json');
    fs.writeFileSync(ledgerFile, JSON.stringify(ledgerTransactions, null, 2));
    console.log(`✅ Ledger transactions saved: ${ledgerFile} (${ledgerTransactions.length} transactions)`);
    
    // Create combined file for reconciliation
    const combined = {
        bank_transactions: bankTransactions,
        ledger_transactions: ledgerTransactions,
        extraction_date: new Date().toISOString(),
        source_files: {
            bank_statement: 'RFSA bank statement July 2025_compressed.pdf',
            ledger: 'RFSA_July_2025_CBE_bank_statement.xlsx'
        },
        summary: {
            bank_count: bankTransactions.length,
            ledger_count: ledgerTransactions.length,
            total_count: bankTransactions.length + ledgerTransactions.length,
            date_range: {
                start: Math.min(
                    ...bankTransactions.map(t => t.date),
                    ...ledgerTransactions.map(t => t.date)
                ),
                end: Math.max(
                    ...bankTransactions.map(t => t.date),
                    ...ledgerTransactions.map(t => t.date)
                )
            }
        }
    };
    
    const combinedFile = path.join(EXTRACTED_DATA_DIR, 'combined_transactions.json');
    fs.writeFileSync(combinedFile, JSON.stringify(combined, null, 2));
    console.log(`✅ Combined data saved: ${combinedFile}`);
    
    return combined;
}

/**
 * Main execution function
 */
async function main() {
    try {
        console.log('🔍 Checking for required files...');
        
        if (!fs.existsSync(BANK_STATEMENT_PDF)) {
            throw new Error(`Bank statement not found: ${BANK_STATEMENT_PDF}`);
        }
        
        if (!fs.existsSync(LEDGER_EXCEL)) {
            throw new Error(`Ledger file not found: ${LEDGER_EXCEL}`);
        }
        
        console.log('✅ All required files found');
        
        // Extract bank transactions from PDF
        const bankTransactions = await extractBankTransactionsFromPDF();
        
        // Extract ledger transactions from Excel
        const ledgerTransactions = await extractLedgerTransactionsFromExcel();
        
        // Save all data
        const combined = saveExtractedData(bankTransactions, ledgerTransactions);
        
        console.log('\n🎉 Extraction Complete!');
        console.log('========================');
        console.log(`📊 Bank Transactions: ${combined.summary.bank_count}`);
        console.log(`📋 Ledger Transactions: ${combined.summary.ledger_count}`);
        console.log(`📅 Date Range: ${combined.summary.date_range.start} to ${combined.summary.date_range.end}`);
        console.log(`💾 Files saved to: ${EXTRACTED_DATA_DIR}`);
        
        console.log('\n✅ Real Ethiopian transaction data is now ready for reconciliation!');
        
    } catch (error) {
        console.error('\n❌ Extraction failed:', error.message);
        process.exit(1);
    }
}

// Install required Python packages if needed
async function installPythonDependencies() {
    console.log('📦 Installing required Python packages...');
    
    const { spawn } = await import('child_process');
    
    return new Promise((resolve, reject) => {
        const pip = spawn('pip3', ['install', 'PyMuPDF', 'pandas', 'openpyxl', 'Pillow']);
        
        pip.on('close', (code) => {
            if (code === 0) {
                console.log('✅ Python dependencies installed');
                resolve();
            } else {
                console.log('⚠️ Some dependencies may already be installed');
                resolve(); // Continue anyway
            }
        });
        
        pip.on('error', (error) => {
            console.log('⚠️ Could not install Python dependencies automatically');
            console.log('Please run: pip3 install PyMuPDF pandas openpyxl Pillow');
            resolve(); // Continue anyway
        });
    });
}

// Run the extraction
if (import.meta.url === `file://${process.argv[1]}`) {
    installPythonDependencies().then(() => main());
}

export { extractBankTransactionsFromPDF, extractLedgerTransactionsFromExcel };
