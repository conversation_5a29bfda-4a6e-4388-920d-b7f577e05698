// Script to fix stuck file uploads by updating their status
const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

// Create Supabase client with service role for admin operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

async function fixFileStatus() {
  try {
    console.log('Fixing stuck file uploads...')
    
    // Update files from 'uploading' to 'uploaded'
    const { data, error } = await supabaseAdmin
      .from('files')
      .update({ status: 'uploaded' })
      .eq('status', 'uploading')
      
    if (error) {
      console.error('Error updating file status:', error)
      return
    }
    
    console.log('Success! Files updated to "uploaded" status')
    console.log('You can now go to the reconciliation page and click "Start Processing"')
    
  } catch (error) {
    console.error('Unexpected error:', error)
  }
}

fixFileStatus()
