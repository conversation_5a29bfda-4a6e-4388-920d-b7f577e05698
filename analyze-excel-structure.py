#!/usr/bin/env python3
"""
Analyze Excel file structure to understand why we're only extracting 50 transactions
when we should have 500+
"""

import pandas as pd
import openpyxl
import sys

def analyze_excel_file(file_path):
    print(f"Analyzing Excel file: {file_path}")
    print("=" * 60)
    
    # Load workbook to check sheets
    workbook = openpyxl.load_workbook(file_path, data_only=True)
    print(f"Sheet names: {workbook.sheetnames}")
    print(f"Number of sheets: {len(workbook.sheetnames)}")
    print()
    
    # Analyze each sheet
    for sheet_name in workbook.sheetnames:
        print(f"Sheet: '{sheet_name}'")
        print("-" * 40)
        
        sheet = workbook[sheet_name]
        
        # Get dimensions
        max_row = sheet.max_row
        max_col = sheet.max_column
        print(f"Max row: {max_row}")
        print(f"Max column: {max_col}")
        
        # Check if sheet has data
        if max_row > 1:
            # Read with pandas to get better analysis
            try:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                print(f"DataFrame shape: {df.shape}")
                print(f"Columns: {list(df.columns)}")
                print(f"Non-empty rows: {len(df.dropna(how='all'))}")
                
                # Look for transaction-like data
                date_cols = [col for col in df.columns if 'date' in str(col).lower()]
                amount_cols = [col for col in df.columns if any(word in str(col).lower() for word in ['amount', 'debit', 'credit', 'balance'])]
                
                print(f"Potential date columns: {date_cols}")
                print(f"Potential amount columns: {amount_cols}")
                
                # Show first few rows
                print("\nFirst 5 rows:")
                print(df.head().to_string())
                
                # Show last few rows to see if there's more data
                print(f"\nLast 5 rows (of {len(df)} total):")
                print(df.tail().to_string())
                
            except Exception as e:
                print(f"Error reading sheet with pandas: {e}")
        
        print("\n")
    
    workbook.close()

if __name__ == "__main__":
    file_path = "/Users/<USER>/Desktop/projects/acounting-app/example-docs/RFSA_July_2025_CBE_bank_statement.xlsx"
    analyze_excel_file(file_path)
