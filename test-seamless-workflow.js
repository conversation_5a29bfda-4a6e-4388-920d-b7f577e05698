// Test script for the seamless reconciliation workflow
const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

// Create Supabase client with service role for admin operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

async function testSeamlessWorkflow() {
  try {
    console.log('🧪 Testing Seamless Reconciliation Workflow...')
    
    // 1. Check if we have uploaded files
    const { data: files, error: filesError } = await supabaseAdmin
      .from('files')
      .select('*')
      .eq('status', 'uploaded')
      
    if (filesError) {
      console.error('❌ Error fetching files:', filesError)
      return
    }
    
    console.log(`✅ Found ${files?.length || 0} uploaded files`)
    
    if (!files || files.length === 0) {
      console.log('📝 No uploaded files found. The workflow will start from file upload.')
      return
    }
    
    // 2. Test the seamless reconciliation API
    console.log('🚀 Testing seamless reconciliation API...')
    
    const response = await fetch('http://localhost:3000/api/seamless-reconciliation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    })
    
    if (!response.ok) {
      const errorData = await response.json()
      console.error('❌ API Error:', errorData.error)
      return
    }
    
    const result = await response.json()
    console.log('✅ Seamless reconciliation completed successfully!')
    console.log('📊 Summary:', result.summary)
    console.log(`🎯 Found ${result.matches} transaction matches`)
    
    // 3. Check if transactions were stored
    const { data: transactions, error: transError } = await supabaseAdmin
      .from('transactions')
      .select('count(*)')
      .single()
      
    if (!transError && transactions) {
      console.log(`💾 Stored ${transactions.count} transactions in database`)
    }
    
    console.log('🎉 Seamless workflow test completed successfully!')
    console.log('🌐 Visit http://localhost:3000/dashboard/seamless to try the UI')
    
  } catch (error) {
    console.error('💥 Test failed:', error.message)
  }
}

testSeamlessWorkflow()
