#!/usr/bin/env python3
"""
Extract ALL ledger transactions from Excel file - no API key needed
"""

import pandas as pd
import json
import os
from datetime import datetime

def extract_all_ledger_transactions(excel_path):
    """Extract ALL transactions from the Excel ledger file"""
    print("Extracting ALL ledger transactions...")
    
    # Read the Excel file
    df = pd.read_excel(excel_path, sheet_name='Sheet1')
    print(f"Total rows in Excel: {len(df)}")
    
    # Clean up column names based on the structure we found
    df.columns = ['account_id', 'account_description', 'date', 'reference', 'journal', 'description', 'debit', 'credit', 'balance']
    
    # Filter out header rows and summary rows
    # Keep only rows with valid dates and transaction data
    df = df.dropna(subset=['date'])
    
    # Remove header rows
    df = df[df['date'] != 'Date']
    df = df[~df['date'].astype(str).str.contains('Account ID', na=False)]
    df = df[~df['date'].astype(str).str.contains('General Ledger', na=False)]
    df = df[~df['date'].astype(str).str.contains('For the Period', na=False)]
    df = df[~df['date'].astype(str).str.contains('Filter Criteria', na=False)]
    
    # Keep only rows with valid dates
    df = df[pd.to_datetime(df['date'], errors='coerce').notna()]
    
    print(f"Rows with valid dates after filtering: {len(df)}")
    
    transactions = []
    
    for _, row in df.iterrows():
        try:
            # Parse date
            date_obj = pd.to_datetime(row['date'])
            date_str = date_obj.strftime('%Y-%m-%d')
            
            # Get amounts - handle NaN and string values
            def safe_float(val):
                if pd.isna(val) or val == '' or str(val).lower() == 'nan':
                    return 0.0
                try:
                    return float(str(val).replace(',', ''))
                except:
                    return 0.0
            
            debit = safe_float(row['debit'])
            credit = safe_float(row['credit'])
            balance = safe_float(row['balance'])
            
            # Calculate net amount
            amount = credit - debit if credit > 0 else -debit
            
            # Clean up text fields
            def safe_str(val):
                if pd.isna(val) or str(val).lower() == 'nan':
                    return ""
                return str(val).strip()
            
            transaction = {
                "date": date_str,
                "description": safe_str(row['description']),
                "reference": safe_str(row['reference']),
                "account_id": safe_str(row['account_id']),
                "account_description": safe_str(row['account_description']),
                "journal": safe_str(row['journal']),
                "debit": debit,
                "credit": credit,
                "balance": balance,
                "amount": amount
            }
            
            # Only add if it's a real transaction (has amount or description)
            if amount != 0 or transaction['description']:
                transactions.append(transaction)
            
        except Exception as e:
            print(f"Error processing row: {e}")
            continue
    
    print(f"Successfully extracted {len(transactions)} ledger transactions")
    return transactions

def main():
    """Main extraction function"""
    
    excel_path = "/Users/<USER>/Desktop/projects/acounting-app/example-docs/RFSA_July_2025_CBE_bank_statement.xlsx"
    
    # Extract ledger transactions
    print("=" * 60)
    ledger_transactions = extract_all_ledger_transactions(excel_path)
    
    # Save results
    output_dir = "/Users/<USER>/Desktop/projects/acounting-app/src/lib/e2b/extracted_data"
    
    # Save ledger transactions
    ledger_file = os.path.join(output_dir, "ledger_transactions_complete.json")
    with open(ledger_file, 'w') as f:
        json.dump(ledger_transactions, f, indent=2)
    print(f"Saved {len(ledger_transactions)} ledger transactions to {ledger_file}")
    
    # Show sample transactions
    print("\nSample transactions:")
    for i, txn in enumerate(ledger_transactions[:5]):
        print(f"{i+1}. {txn['date']} - {txn['description']} - {txn['amount']} ETB")
    
    print("=" * 60)
    print("LEDGER EXTRACTION SUMMARY:")
    print(f"Total transactions extracted: {len(ledger_transactions)}")
    print(f"Date range: {ledger_transactions[0]['date'] if ledger_transactions else 'N/A'} to {ledger_transactions[-1]['date'] if ledger_transactions else 'N/A'}")
    print("=" * 60)

if __name__ == "__main__":
    main()
