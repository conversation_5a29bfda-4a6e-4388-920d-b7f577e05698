#!/usr/bin/env python3
"""
Complete extraction using Mistral Document AI OCR for PDF and proper Excel processing
This will extract ALL 500+ transactions from both files
"""

import pandas as pd
import json
import os
import base64
import requests
from datetime import datetime
import re

def extract_all_ledger_transactions(excel_path):
    """Extract ALL transactions from the Excel ledger file"""
    print("Extracting ALL ledger transactions...")
    
    # Read the Excel file
    df = pd.read_excel(excel_path, sheet_name='Sheet1')
    print(f"Total rows in Excel: {len(df)}")
    
    # Clean up column names
    df.columns = ['account_id', 'account_description', 'date', 'reference', 'journal', 'description', 'debit', 'credit', 'balance']
    
    # Filter out header rows and summary rows
    # Keep only rows with valid dates and transaction data
    df = df.dropna(subset=['date'])
    df = df[df['date'] != 'Date']  # Remove header row
    df = df[pd.to_datetime(df['date'], errors='coerce').notna()]  # Keep only valid dates
    
    print(f"Rows with valid dates: {len(df)}")
    
    transactions = []
    
    for _, row in df.iterrows():
        try:
            # Parse date
            date_obj = pd.to_datetime(row['date'])
            date_str = date_obj.strftime('%Y-%m-%d')
            
            # Get amounts
            debit = float(row['debit']) if pd.notna(row['debit']) and str(row['debit']).replace('.', '').replace('-', '').isdigit() else 0
            credit = float(row['credit']) if pd.notna(row['credit']) and str(row['credit']).replace('.', '').replace('-', '').isdigit() else 0
            balance = float(row['balance']) if pd.notna(row['balance']) and str(row['balance']).replace('.', '').replace('-', '').isdigit() else 0
            
            # Calculate net amount
            amount = credit - debit if credit > 0 else -debit
            
            transaction = {
                "date": date_str,
                "description": str(row['description']) if pd.notna(row['description']) else "",
                "reference": str(row['reference']) if pd.notna(row['reference']) else "",
                "account_id": str(row['account_id']) if pd.notna(row['account_id']) else "",
                "journal": str(row['journal']) if pd.notna(row['journal']) else "",
                "debit": debit,
                "credit": credit,
                "balance": balance,
                "amount": amount
            }
            
            transactions.append(transaction)
            
        except Exception as e:
            print(f"Error processing row: {e}")
            continue
    
    print(f"Successfully extracted {len(transactions)} ledger transactions")
    return transactions

def extract_bank_transactions_mistral_ocr(pdf_path, api_key):
    """Extract transactions using Mistral Document AI OCR"""
    print("Extracting bank transactions using Mistral Document AI OCR...")
    
    # Encode PDF to base64
    with open(pdf_path, "rb") as pdf_file:
        pdf_base64 = base64.b64encode(pdf_file.read()).decode('utf-8')
    
    # Mistral OCR API call
    url = "https://api.mistral.ai/v1/ocr"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    
    payload = {
        "model": "mistral-ocr-latest",
        "document": {
            "type": "document_url",
            "document_url": f"data:application/pdf;base64,{pdf_base64}"
        },
        "include_image_base64": False
    }
    
    print("Calling Mistral Document AI OCR...")
    response = requests.post(url, headers=headers, json=payload)
    
    if response.status_code != 200:
        print(f"OCR API Error: {response.status_code} - {response.text}")
        return []
    
    ocr_result = response.json()
    
    # Extract text content
    if 'content' in ocr_result:
        text_content = ocr_result['content']
    elif 'text' in ocr_result:
        text_content = ocr_result['text']
    else:
        print("No text content found in OCR response")
        return []
    
    print(f"OCR extracted {len(text_content)} characters of text")
    
    # Parse transactions from OCR text
    transactions = parse_bank_transactions_from_text(text_content)
    
    print(f"Successfully extracted {len(transactions)} bank transactions")
    return transactions

def parse_bank_transactions_from_text(text):
    """Parse bank transactions from OCR text"""
    transactions = []
    
    # Split text into lines
    lines = text.split('\n')
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
            
        # Look for transaction patterns
        # Ethiopian bank statement pattern: Date, Description, Reference, Debit, Credit, Balance
        
        # Try to find date pattern (YYYY-MM-DD or DD/MM/YYYY)
        date_match = re.search(r'(\d{4}-\d{2}-\d{2}|\d{2}/\d{2}/\d{4})', line)
        if not date_match:
            continue
            
        date_str = date_match.group(1)
        
        # Convert date to standard format
        if '/' in date_str:
            try:
                date_obj = datetime.strptime(date_str, '%d/%m/%Y')
                date_str = date_obj.strftime('%Y-%m-%d')
            except:
                continue
        
        # Extract amounts (look for Ethiopian Birr amounts)
        amount_pattern = r'(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)'
        amounts = re.findall(amount_pattern, line)
        
        if len(amounts) >= 2:
            try:
                # Clean amounts
                clean_amounts = [float(amt.replace(',', '')) for amt in amounts]
                
                # Determine debit/credit based on context
                debit = 0
                credit = 0
                balance = clean_amounts[-1]  # Last amount is usually balance
                
                if len(clean_amounts) >= 3:
                    # Format: amount, amount, balance
                    if 'DR' in line or 'DEBIT' in line.upper():
                        debit = clean_amounts[0]
                    else:
                        credit = clean_amounts[0]
                
                # Extract description and reference
                desc_part = line.replace(date_match.group(1), '').strip()
                for amt in amounts:
                    desc_part = desc_part.replace(amt, '').strip()
                
                # Split description and reference
                parts = desc_part.split()
                if len(parts) > 0:
                    reference = parts[-1] if len(parts) > 1 else ""
                    description = ' '.join(parts[:-1]) if len(parts) > 1 else parts[0]
                else:
                    description = ""
                    reference = ""
                
                amount = credit - debit if credit > 0 else -debit
                
                transaction = {
                    "date": date_str,
                    "description": description,
                    "reference": reference,
                    "debit": debit,
                    "credit": credit,
                    "balance": balance,
                    "amount": amount
                }
                
                transactions.append(transaction)
                
            except Exception as e:
                continue
    
    return transactions

def main():
    """Main extraction function"""
    
    # File paths
    pdf_path = "/Users/<USER>/Desktop/projects/acounting-app/example-docs/RFSA bank statement July 2025_compressed.pdf"
    excel_path = "/Users/<USER>/Desktop/projects/acounting-app/example-docs/RFSA_July_2025_CBE_bank_statement.xlsx"
    
    # API key (you'll need to set this)
    api_key = os.environ.get('MISTRAL_API_KEY')
    if not api_key:
        print("ERROR: MISTRAL_API_KEY environment variable not set!")
        print("Please set your Mistral API key: export MISTRAL_API_KEY='your-key-here'")
        return
    
    # Extract ledger transactions (should get 500+)
    print("=" * 60)
    ledger_transactions = extract_all_ledger_transactions(excel_path)
    
    # Extract bank transactions using Mistral OCR (should get 500+)
    print("=" * 60)
    bank_transactions = extract_bank_transactions_mistral_ocr(pdf_path, api_key)
    
    # Save results
    output_dir = "/Users/<USER>/Desktop/projects/acounting-app/src/lib/e2b/extracted_data"
    
    # Save ledger transactions
    ledger_file = os.path.join(output_dir, "ledger_transactions_complete.json")
    with open(ledger_file, 'w') as f:
        json.dump(ledger_transactions, f, indent=2)
    print(f"Saved {len(ledger_transactions)} ledger transactions to {ledger_file}")
    
    # Save bank transactions
    bank_file = os.path.join(output_dir, "bank_transactions_complete.json")
    with open(bank_file, 'w') as f:
        json.dump(bank_transactions, f, indent=2)
    print(f"Saved {len(bank_transactions)} bank transactions to {bank_file}")
    
    # Create combined dataset
    combined_data = {
        "metadata": {
            "extraction_date": datetime.now().isoformat(),
            "method": "Mistral Document AI OCR + Complete Excel Processing",
            "bank_transactions_count": len(bank_transactions),
            "ledger_transactions_count": len(ledger_transactions),
            "total_transactions": len(bank_transactions) + len(ledger_transactions)
        },
        "bank_transactions": bank_transactions,
        "ledger_transactions": ledger_transactions
    }
    
    combined_file = os.path.join(output_dir, "combined_transactions_complete.json")
    with open(combined_file, 'w') as f:
        json.dump(combined_data, f, indent=2)
    print(f"Saved combined dataset to {combined_file}")
    
    print("=" * 60)
    print("EXTRACTION SUMMARY:")
    print(f"Bank transactions: {len(bank_transactions)}")
    print(f"Ledger transactions: {len(ledger_transactions)}")
    print(f"Total transactions: {len(bank_transactions) + len(ledger_transactions)}")
    print("=" * 60)

if __name__ == "__main__":
    main()
