-- Alternative fix for the reconciliation API error
-- Instead of changing the function return type, let's update the TypeScript types to match the database

-- First, let's create a simpler version that works with the existing schema
DROP FUNCTION IF EXISTS get_reconciliation_transactions(UUID, TEXT, INTEGER, INTEGER);

CREATE OR REPLACE FUNCTION get_reconciliation_transactions(
    p_company_id UUID,
    p_status TEXT DEFAULT NULL,
    p_limit INTEGER DEFAULT 50,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    date DATE,
    description TEXT,
    amount DECIMAL,
    reference VARCHAR(255),  -- Match the actual table column type
    source TEXT,
    matched BOOLEAN,
    matched_with UUID,
    confidence DECIMAL,
    reconciliation_id UUID,
    match_type VARCHAR(50)  -- Match the actual table column type
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        t.id,
        t.date,
        t.description,
        t.amount,
        t.reference,  -- No cast needed - use as-is
        CASE
            WHEN t.transaction_type = 'bank_statement' THEN 'bank'
            ELSE 'ledger'
        END as source,
        CASE
            WHEN r.id IS NOT NULL AND r.status IN ('matched', 'auto_matched', 'manual_matched') THEN true
            ELSE false
        END as matched,
        CASE
            WHEN t.transaction_type = 'bank_statement' THEN r.ledger_transaction_id
            ELSE r.bank_transaction_id
        END as matched_with,
        r.match_confidence as confidence,
        r.id as reconciliation_id,
        r.match_type
    FROM transactions t
    LEFT JOIN reconciliations r ON (
        (t.transaction_type = 'bank_statement' AND r.bank_transaction_id = t.id) OR
        (t.transaction_type = 'ledger_entry' AND r.ledger_transaction_id = t.id)
    )
    WHERE t.company_id = p_company_id
    AND (p_status IS NULL OR
         (p_status = 'matched' AND r.status IN ('matched', 'auto_matched', 'manual_matched')) OR
         (p_status = 'unmatched' AND (r.id IS NULL OR r.status NOT IN ('matched', 'auto_matched', 'manual_matched'))))
    ORDER BY t.date DESC, t.created_at DESC
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION get_reconciliation_transactions(UUID, TEXT, INTEGER, INTEGER) TO authenticated;