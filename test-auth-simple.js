#!/usr/bin/env node
/**
 * Simple Authentication Test for Debugging
 */

const { createClient } = require('@supabase/supabase-js')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function testAuth() {
  console.log('🔐 Testing Authentication...')

  try {
    // Sign in
    const { data, error } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'Aa@12345678'
    })

    if (error) {
      console.error('❌ Auth Error:', error.message)
      return
    }

    console.log('✅ Authentication successful')
    console.log('User ID:', data.user.id)
    console.log('Email:', data.user.email)

    // Check user profile
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', data.user.id)
      .single()

    if (profileError && profileError.code !== 'PGRST116') {
      console.error('❌ Profile Error:', profileError.message)
    } else {
      console.log('✅ User profile found:', profile?.email || 'No profile')
    }

    // Check company association
    const { data: companyUsers, error: companyError } = await supabase
      .from('company_users')
      .select('*')
      .eq('user_id', data.user.id)

    if (companyError) {
      console.error('❌ Company Error:', companyError.message)
    } else {
      console.log('✅ Company associations:', companyUsers.length)
      if (companyUsers.length > 0) {
        console.log('Company ID:', companyUsers[0].company_id)

        // Get company details
        const { data: company, error: companyDetailError } = await supabase
          .from('accounting_companies')
          .select('*')
          .eq('id', companyUsers[0].company_id)
          .single()

        if (companyDetailError) {
          console.error('❌ Company Detail Error:', companyDetailError.message)
        } else {
          console.log('✅ Company found:', company.name)
        }
      }
    }

    // Check files
    const { data: files, error: filesError } = await supabase
      .from('files')
      .select('*')
      .limit(5)

    if (filesError) {
      console.error('❌ Files Error:', filesError.message)
    } else {
      console.log('✅ Files in database:', files.length)
      files.forEach(f => {
        console.log(`  - ${f.original_filename} (${f.status})`)
      })
    }

  } catch (error) {
    console.error('💥 Critical Error:', error.message)
  }
}

testAuth()