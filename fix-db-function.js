const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

async function fixReconciliationFunction() {
  console.log('🔧 Fixing reconciliation function...')

  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY
  )

  try {
    // Read the fix SQL
    const { readFileSync } = require('fs')
    const fixSQL = readFileSync('./fix-reconciliation-function.sql', 'utf8')

    console.log('📝 Applying database fix...')

    // Execute the fix using raw SQL
    const { data, error } = await supabase.rpc('exec_sql', { sql: fixSQL })

    if (error) {
      console.error('❌ Error applying fix:', error)

      // Try alternative approach - split statements
      const statements = fixSQL.split(';').filter(stmt => stmt.trim())

      for (let i = 0; i < statements.length; i++) {
        const stmt = statements[i].trim()
        if (stmt) {
          console.log(`Executing statement ${i + 1}/${statements.length}...`)
          try {
            const { error: stmtError } = await supabase.rpc('exec_sql', { sql: stmt + ';' })
            if (stmtError) {
              console.error(`Statement ${i + 1} failed:`, stmtError)
            } else {
              console.log(`✅ Statement ${i + 1} executed successfully`)
            }
          } catch (err) {
            console.error(`Statement ${i + 1} exception:`, err)
          }
        }
      }
    } else {
      console.log('✅ Database fix applied successfully!')
    }

    // Test the function
    console.log('🧪 Testing the fixed function...')
    const { data: testData, error: testError } = await supabase
      .rpc('get_reconciliation_transactions', {
        p_company_id: 'test-company-id',
        p_limit: 1
      })

    if (testError) {
      console.error('❌ Function test failed:', testError)
    } else {
      console.log('✅ Function test successful!')
      console.log('Return structure:', testData?.[0] ? Object.keys(testData[0]) : 'No data')
    }

  } catch (error) {
    console.error('❌ Script error:', error)
  }
}

fixReconciliationFunction()