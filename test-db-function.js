console.log('🧪 Testing reconciliation function...')

// Simple test to see what the actual database function signature looks like
const { Pool } = require('pg')

// Create a connection using Supabase connection details
const connectionString = process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:54322/postgres'

async function testFunction() {
  const pool = new Pool({
    connectionString,
  })

  try {
    // Check current function signature
    const result = await pool.query(`
      SELECT
        p.proname as function_name,
        pg_get_function_result(p.oid) as return_type
      FROM pg_proc p
      WHERE p.proname = 'get_reconciliation_transactions'
    `)

    if (result.rows.length > 0) {
      console.log('✅ Current function signature:')
      console.log(result.rows[0].return_type)

      // Test if it works
      try {
        const testResult = await pool.query(`
          SELECT * FROM get_reconciliation_transactions(
            '5277bdf7-bbeb-4cab-bcbb-e55ee177b4dd'::UUID,
            NULL,
            1,
            0
          )
        `)
        console.log('✅ Function works! Sample result:')
        console.log(testResult.rows[0] || 'No data returned')
      } catch (err) {
        console.log('❌ Function test failed:', err.message)
        console.log('📝 Need to apply the fix in fix-reconciliation-function-v2.sql')
      }
    } else {
      console.log('❌ Function not found')
    }

  } catch (error) {
    console.error('❌ Database connection failed:', error.message)
    console.log('⚠️  Using alternative Supabase method...')

    // Fallback to Supabase client
    const { createClient } = require('@supabase/supabase-js')
    require('dotenv').config({ path: '.env.local' })

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    )

    const { data, error: supabaseError } = await supabase.rpc('get_reconciliation_transactions', {
      p_company_id: '5277bdf7-bbeb-4cab-bcbb-e55ee177b4dd',
      p_limit: 1
    })

    if (supabaseError) {
      console.log('❌ Supabase test failed:', supabaseError.message)
    } else {
      console.log('✅ Supabase test works!')
      console.log('📊 Sample data:', data?.[0] || 'No data')
    }
  } finally {
    await pool.end()
  }
}

testFunction()