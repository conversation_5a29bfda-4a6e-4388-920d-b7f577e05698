const { createClient } = require('@supabase/supabase-js')

// Test the API endpoints for the review page
async function testReviewAPIs() {
  console.log('🔍 Testing Review Page API Endpoints...\n')

  try {
    // Test the matches endpoint
    console.log('Testing /api/reconciliation/matches...')
    const matchesResponse = await fetch('http://localhost:3002/api/reconciliation/matches')

    if (matchesResponse.ok) {
      const matchesData = await matchesResponse.json()
      console.log('✅ Matches API successful')
      console.log(`📊 Found ${matchesData.length} matched transactions`)

      // Show sample match
      if (matchesData.length > 0) {
        const sample = matchesData[0]
        console.log('📝 Sample match:')
        console.log(`   - Confidence: ${sample.match_confidence}%`)
        console.log(`   - Bank: ${sample.bankTransaction?.description || 'N/A'}`)
        console.log(`   - Ledger: ${sample.ledgerTransaction?.description || 'N/A'}`)
      }
    } else {
      console.log('❌ Matches API failed:', matchesResponse.status, matchesResponse.statusText)
    }

    console.log('\nTesting /api/reconciliation/unmatched...')
    const unmatchedResponse = await fetch('http://localhost:3002/api/reconciliation/unmatched')

    if (unmatchedResponse.ok) {
      const unmatchedData = await unmatchedResponse.json()
      console.log('✅ Unmatched API successful')
      console.log(`📊 Unmatched bank: ${unmatchedData.bank?.length || 0}`)
      console.log(`📊 Unmatched ledger: ${unmatchedData.ledger?.length || 0}`)
    } else {
      console.log('❌ Unmatched API failed:', unmatchedResponse.status, unmatchedResponse.statusText)
    }

  } catch (error) {
    console.error('🚨 Test failed:', error.message)
  }
}

testReviewAPIs()