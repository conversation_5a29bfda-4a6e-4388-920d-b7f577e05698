#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js')

// Initialize Supabase client
const supabaseUrl = 'https://rrvdmpagsvhjdurfmqec.supabase.co'
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJydmRtcGFnc3ZoamR1cmZtcWVjIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1ODEzMDM1MiwiZXhwIjoyMDczNzA2MzUyfQ.SbZmq3nlQudzUN-xzJHj-NS23xnFVSlyx1RZWWZTy6U'

const supabase = createClient(supabaseUrl, supabaseServiceKey)

// Enhanced matching algorithms inspired by the example
function extractFinReference(text) {
  // Extract FIN/XXXX/YY or FIN/XXXX/YYYY patterns
  const finMatch = text.match(/FIN\/(\d+)\/(\d+)/i)
  if (finMatch) {
    const [, number, year] = finMatch
    // Normalize year to 4 digits for comparison
    const normalizedYear = year.length === 2 ? `20${year}` : year
    return {
      original: finMatch[0],
      number: number,
      year: normalizedYear,
      normalized: `FIN/${number}/${normalizedYear}`
    }
  }
  return null
}

function extractCheckNumber(text) {
  // Extract check patterns like "CHQ NO. 47996461" or "CD47996452"
  const checkMatch = text.match(/(?:CHQ\s+NO\.?\s*|CD)(\d+)/i)
  if (checkMatch) {
    return checkMatch[1]
  }
  return null
}

function extractPaymentVoucher(text) {
  // Extract PV patterns like "PV 352714" or "PV-353461"
  const pvMatch = text.match(/PV[-\s]*(\d+)/i)
  if (pvMatch) {
    return pvMatch[1]
  }
  return null
}

function calculateFinSimilarity(ref1, ref2) {
  // If both have FIN patterns, compare them
  const fin1 = extractFinReference(ref1)
  const fin2 = extractFinReference(ref2)

  if (fin1 && fin2) {
    // Perfect match: same FIN number and normalized year
    if (fin1.number === fin2.number && fin1.year === fin2.year) {
      return 100
    }
    // High match: same FIN number (handles 25 vs 2025 year difference)
    if (fin1.number === fin2.number) {
      return 95
    }
  }

  return 0
}

function calculateReferenceStrength(bankTxn, ledgerTxn) {
  let maxScore = 0
  let matchType = 'none'

  // 1. FIN reference matching (description vs reference)
  const finScore1 = calculateFinSimilarity(bankTxn.description, ledgerTxn.reference)
  if (finScore1 > maxScore) {
    maxScore = finScore1
    matchType = 'fin_description_to_reference'
  }

  // 2. FIN reference matching (reference vs reference)
  const finScore2 = calculateFinSimilarity(bankTxn.reference, ledgerTxn.reference)
  if (finScore2 > maxScore) {
    maxScore = finScore2
    matchType = 'fin_reference_to_reference'
  }

  // 3. Check number matching
  const bankCheck = extractCheckNumber(bankTxn.description) || extractCheckNumber(bankTxn.reference)
  const ledgerCheck = extractCheckNumber(ledgerTxn.reference) || extractCheckNumber(ledgerTxn.description)
  if (bankCheck && ledgerCheck && bankCheck === ledgerCheck) {
    if (90 > maxScore) {
      maxScore = 90
      matchType = 'check_number'
    }
  }

  // 4. PV number cross-matching
  const bankPV = extractPaymentVoucher(bankTxn.description) || extractPaymentVoucher(bankTxn.reference)
  const ledgerPV = extractPaymentVoucher(ledgerTxn.description) || extractPaymentVoucher(ledgerTxn.reference)
  if (bankPV && ledgerPV && bankPV === ledgerPV) {
    if (85 > maxScore) {
      maxScore = 85
      matchType = 'payment_voucher'
    }
  }

  return { score: maxScore, type: matchType }
}

function calculateDateDifference(date1, date2) {
  const d1 = new Date(date1)
  const d2 = new Date(date2)
  return Math.abs((d1 - d2) / (1000 * 60 * 60 * 24)) // Days difference
}

function enhancedTransactionMatching(bankTransactions, ledgerTransactions) {
  console.log(`🤖 Enhanced matching: ${bankTransactions.length} bank vs ${ledgerTransactions.length} ledger`)

  const matches = []
  const usedLedgerIds = new Set()
  const usedBankIds = new Set()

  // Strategy 1: High-confidence reference matching (FIN patterns, check numbers, etc.)
  console.log('🎯 Strategy 1: High-confidence reference matching...')
  for (const bankTxn of bankTransactions) {
    if (usedBankIds.has(bankTxn.id)) continue

    let bestMatch = null
    let bestScore = 0

    for (const ledgerTxn of ledgerTransactions) {
      if (usedLedgerIds.has(ledgerTxn.id)) continue

      const refStrength = calculateReferenceStrength(bankTxn, ledgerTxn)

      if (refStrength.score >= 85) {
        // Also check amount similarity (with some tolerance for rounding)
        const amountDiff = Math.abs(Math.abs(parseFloat(bankTxn.amount)) - Math.abs(parseFloat(ledgerTxn.amount)))
        const amountTolerance = Math.max(1, Math.abs(parseFloat(bankTxn.amount)) * 0.001) // 0.1% tolerance

        if (amountDiff <= amountTolerance) {
          const totalScore = refStrength.score

          if (totalScore > bestScore) {
            bestScore = totalScore
            bestMatch = {
              ledgerTxn,
              confidence: totalScore,
              matchType: refStrength.type,
              amountDiff
            }
          }
        }
      }
    }

    if (bestMatch && bestScore >= 85) {
      matches.push({
        bank_transaction_id: bankTxn.id,
        ledger_transaction_id: bestMatch.ledgerTxn.id,
        match_confidence: bestMatch.confidence,
        status: 'auto_matched',
        match_method: bestMatch.matchType,
        amount_difference: bestMatch.amountDiff,
        date_difference: calculateDateDifference(bankTxn.date, bestMatch.ledgerTxn.date)
      })

      usedBankIds.add(bankTxn.id)
      usedLedgerIds.add(bestMatch.ledgerTxn.id)
    }
  }

  console.log(`✅ High-confidence matching found ${matches.length} matches`)

  // Strategy 2: Amount + Date proximity matching (for remaining transactions)
  console.log('🎯 Strategy 2: Amount + Date proximity matching...')
  const strategy2Start = matches.length

  for (const bankTxn of bankTransactions) {
    if (usedBankIds.has(bankTxn.id)) continue

    let bestMatch = null
    let bestScore = 0

    for (const ledgerTxn of ledgerTransactions) {
      if (usedLedgerIds.has(ledgerTxn.id)) continue

      // Amount matching with small tolerance
      const amountDiff = Math.abs(Math.abs(parseFloat(bankTxn.amount)) - Math.abs(parseFloat(ledgerTxn.amount)))
      const amountTolerance = Math.max(1, Math.abs(parseFloat(bankTxn.amount)) * 0.005) // 0.5% tolerance

      if (amountDiff <= amountTolerance) {
        // Date proximity scoring
        const daysDiff = calculateDateDifference(bankTxn.date, ledgerTxn.date)
        let dateScore = 0

        if (daysDiff === 0) dateScore = 100        // Same day
        else if (daysDiff <= 1) dateScore = 90     // 1 day difference
        else if (daysDiff <= 3) dateScore = 80     // 3 day difference
        else if (daysDiff <= 7) dateScore = 70     // 1 week difference
        else continue // Skip if more than 1 week

        // Combined score (amount precision + date proximity)
        const amountScore = amountDiff === 0 ? 100 : Math.max(0, 100 - (amountDiff / Math.abs(parseFloat(bankTxn.amount))) * 100)
        const totalScore = (amountScore * 0.7) + (dateScore * 0.3) // Weight amount higher

        if (totalScore > bestScore && totalScore >= 75) {
          bestScore = totalScore
          bestMatch = {
            ledgerTxn,
            confidence: Math.round(totalScore),
            amountDiff,
            daysDiff
          }
        }
      }
    }

    if (bestMatch) {
      matches.push({
        bank_transaction_id: bankTxn.id,
        ledger_transaction_id: bestMatch.ledgerTxn.id,
        match_confidence: bestMatch.confidence,
        status: 'auto_matched',
        match_method: 'amount_date_proximity',
        amount_difference: bestMatch.amountDiff,
        date_difference: bestMatch.daysDiff
      })

      usedBankIds.add(bankTxn.id)
      usedLedgerIds.add(bestMatch.ledgerTxn.id)
    }
  }

  console.log(`✅ Amount+Date matching found ${matches.length - strategy2Start} additional matches`)

  // Strategy 3: Fuzzy description matching for high-value transactions
  console.log('🎯 Strategy 3: Fuzzy description matching for high-value transactions...')
  const strategy3Start = matches.length
  const highValueThreshold = 10000 // Transactions above this amount get fuzzy matching

  for (const bankTxn of bankTransactions) {
    if (usedBankIds.has(bankTxn.id)) continue
    if (Math.abs(parseFloat(bankTxn.amount)) < highValueThreshold) continue

    let bestMatch = null
    let bestScore = 0

    for (const ledgerTxn of ledgerTransactions) {
      if (usedLedgerIds.has(ledgerTxn.id)) continue
      if (Math.abs(parseFloat(ledgerTxn.amount)) < highValueThreshold) continue

      // Amount matching with larger tolerance for high-value transactions
      const amountDiff = Math.abs(Math.abs(parseFloat(bankTxn.amount)) - Math.abs(parseFloat(ledgerTxn.amount)))
      const amountTolerance = Math.abs(parseFloat(bankTxn.amount)) * 0.01 // 1% tolerance for high-value

      if (amountDiff <= amountTolerance) {
        const daysDiff = calculateDateDifference(bankTxn.date, ledgerTxn.date)

        if (daysDiff <= 7) { // Within a week
          const amountScore = Math.max(0, 100 - (amountDiff / Math.abs(parseFloat(bankTxn.amount))) * 100)
          const dateScore = Math.max(0, 100 - (daysDiff * 10)) // 10 points per day penalty
          const totalScore = (amountScore * 0.8) + (dateScore * 0.2) // Weight amount even higher

          if (totalScore > bestScore && totalScore >= 70) {
            bestScore = totalScore
            bestMatch = {
              ledgerTxn,
              confidence: Math.round(totalScore),
              amountDiff,
              daysDiff
            }
          }
        }
      }
    }

    if (bestMatch) {
      matches.push({
        bank_transaction_id: bankTxn.id,
        ledger_transaction_id: bestMatch.ledgerTxn.id,
        match_confidence: bestMatch.confidence,
        status: 'auto_matched',
        match_method: 'high_value_fuzzy',
        amount_difference: bestMatch.amountDiff,
        date_difference: bestMatch.daysDiff
      })

      usedBankIds.add(bankTxn.id)
      usedLedgerIds.add(bestMatch.ledgerTxn.id)
    }
  }

  console.log(`✅ High-value fuzzy matching found ${matches.length - strategy3Start} additional matches`)

  return matches
}

async function performAdvancedReconciliation() {
  try {
    console.log('🚀 Starting advanced reconciliation with enhanced matching...')

    const companyId = '5277bdf7-bbeb-4cab-bcbb-e55ee177b4dd'

    // Clear existing reconciliations
    await supabase.from('reconciliations').delete().eq('company_id', companyId)

    // Get all real transactions
    console.log('📊 Fetching real transaction data...')

    const { data: bankTransactions, error: bankError } = await supabase
      .from('transactions')
      .select('*')
      .eq('company_id', companyId)
      .eq('transaction_type', 'bank_statement')

    if (bankError) {
      console.error('❌ Error fetching bank transactions:', bankError)
      return
    }

    const { data: ledgerTransactions, error: ledgerError } = await supabase
      .from('transactions')
      .select('*')
      .eq('company_id', companyId)
      .eq('transaction_type', 'ledger_entry')

    if (ledgerError) {
      console.error('❌ Error fetching ledger transactions:', ledgerError)
      return
    }

    console.log(`🏦 Found ${bankTransactions.length} bank transactions`)
    console.log(`📚 Found ${ledgerTransactions.length} ledger transactions`)

    // Run enhanced matching algorithm
    const matches = enhancedTransactionMatching(bankTransactions, ledgerTransactions)

    console.log(`🎉 Total enhanced matches found: ${matches.length}`)

    // Calculate detailed statistics
    const matchesByType = {}
    matches.forEach(match => {
      matchesByType[match.match_method] = (matchesByType[match.match_method] || 0) + 1
    })

    console.log('📈 Matches by type:')
    Object.entries(matchesByType).forEach(([type, count]) => {
      console.log(`   ${type}: ${count}`)
    })

    // Insert matches into reconciliations table
    if (matches.length > 0) {
      console.log('💾 Saving enhanced matches to database...')

      const reconciliationsToInsert = matches.map(match => ({
        company_id: companyId,
        bank_transaction_id: match.bank_transaction_id,
        ledger_transaction_id: match.ledger_transaction_id,
        status: match.status,
        match_confidence: match.match_confidence,
        amount_difference: match.amount_difference,
        date_difference: Math.round(match.date_difference),
        notes: `Enhanced AI-matched using ${match.match_method}`,
        match_type: match.match_method,
        matched_fields: {
          method: match.match_method,
          confidence: match.match_confidence,
          amount_diff: match.amount_difference,
          date_diff: match.date_difference
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }))

      const { data: insertResult, error: insertError } = await supabase
        .from('reconciliations')
        .insert(reconciliationsToInsert)

      if (insertError) {
        console.error('❌ Error inserting reconciliations:', insertError)
        return
      } else {
        console.log('✅ Successfully saved enhanced matches to database')
      }
    }

    // Verify results with database function
    console.log('🧪 Verifying final results...')
    const { data: summaryData, error: summaryError } = await supabase
      .rpc('get_reconciliation_summary', { p_company_id: companyId })

    if (summaryError) {
      console.error('❌ Error getting summary:', summaryError)
    } else {
      const summary = summaryData[0]
      console.log('📈 Final enhanced reconciliation summary:')
      console.log(`   Bank transactions: ${summary.total_bank_transactions}`)
      console.log(`   Ledger transactions: ${summary.total_ledger_transactions}`)
      console.log(`   Matched transactions: ${summary.matched_transactions}`)
      console.log(`   Match rate: ${((summary.matched_transactions / summary.total_bank_transactions) * 100).toFixed(1)}%`)
      console.log(`   Unmatched bank: ${summary.unmatched_bank_transactions}`)
      console.log(`   Unmatched ledger: ${summary.unmatched_ledger_transactions}`)
      console.log(`   Reconciliation status: ${summary.reconciliation_status}`)
    }

    console.log('🎉 Advanced reconciliation completed!')

  } catch (error) {
    console.error('❌ Error in advanced reconciliation process:', error)
  }
}

// Run the advanced reconciliation
performAdvancedReconciliation()