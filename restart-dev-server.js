// <PERSON>ript to restart the development server
const { exec } = require('child_process');
const readline = require('readline');

console.log('🔄 Restarting development server...');

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

console.log('\n📋 INSTRUCTIONS:');
console.log('1. Press Ctrl+C to stop the current server if it\'s running');
console.log('2. Then press Enter to start a new server');
console.log('3. Once the server is running, visit http://localhost:3002/dashboard/seamless\n');

rl.question('Press Enter to start the server...', () => {
  console.log('\n🚀 Starting development server...');
  
  const server = exec('npm run dev', (error, stdout, stderr) => {
    if (error) {
      console.error(`❌ Error: ${error.message}`);
      return;
    }
    if (stderr) {
      console.error(`⚠️ stderr: ${stderr}`);
      return;
    }
    console.log(`📤 stdout: ${stdout}`);
  });
  
  // Forward stdout and stderr to console
  server.stdout.on('data', (data) => {
    console.log(data.toString());
  });
  
  server.stderr.on('data', (data) => {
    console.error(data.toString());
  });
  
  console.log('✅ Server started! Visit http://localhost:3002/dashboard/seamless');
  console.log('Press Ctrl+C to stop the server');
  
  rl.close();
});
