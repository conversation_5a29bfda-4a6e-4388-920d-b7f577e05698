const https = require('https');
const http = require('http');

async function testMatchingAPI() {
  try {
    console.log('🔄 Testing POST /api/match-transactions...');

    const postData = JSON.stringify({
      options: {
        exactMatchFields: ['reference', 'date', 'amount'],
        fuzzyMatchThreshold: 70,
        dateToleranceDays: 3,
        amountTolerancePercentage: 5,
        descriptionSimilarityThreshold: 60
      }
    });

    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/match-transactions',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const response = await new Promise((resolve, reject) => {
      const req = http.request(options, (res) => {
        let data = '';
        res.on('data', (chunk) => data += chunk);
        res.on('end', () => resolve({ statusCode: res.statusCode, data }));
      });
      req.on('error', reject);
      req.write(postData);
      req.end();
    });

    if (response.statusCode === 200) {
      const result = JSON.parse(response.data);
      console.log('✅ Transaction matching started successfully');
      console.log('Result:', JSON.stringify(result, null, 2));

      // Save results to test folder
      const fs = require('fs');
      const path = require('path');

      const resultsDir = path.join(__dirname, 'test-results', 'reconciliation');
      if (!fs.existsSync(resultsDir)) {
        fs.mkdirSync(resultsDir, { recursive: true });
      }

      fs.writeFileSync(
        path.join(resultsDir, 'matching-results.json'),
        JSON.stringify(result, null, 2)
      );

      console.log('📄 Results saved to test-results/reconciliation/matching-results.json');

    } else {
      console.log('❌ Transaction matching failed:', response.statusCode);
      console.log('Error:', response.data);
    }

  } catch (error) {
    console.error('❌ API test failed:', error.message);
  }
}

testMatchingAPI();