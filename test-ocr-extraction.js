/**
 * Test OCR-based PDF extraction
 * This script tests the improved PDF extraction using Mistral OCR
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔍 Testing OCR-based PDF Extraction');
console.log('===================================');

async function testOCRExtraction() {
  try {
    // Check if Mistral API key is set
    if (!process.env.MISTRAL_API_KEY) {
      console.log('❌ MISTRAL_API_KEY environment variable not set!');
      console.log('💡 Please set it with: export MISTRAL_API_KEY="your-api-key"');
      console.log('🔗 Get your API key from: https://console.mistral.ai/');
      return;
    }

    console.log('✅ MISTRAL_API_KEY is set');
    console.log('🚀 Starting OCR extraction...');

    // Check if required Python packages are installed
    console.log('\n📦 Checking Python dependencies...');
    
    const checkDeps = spawn('python3', ['-c', `
import sys
try:
    import pdf2image
    import PIL
    import requests
    import pandas
    print("✅ All dependencies available")
except ImportError as e:
    print(f"❌ Missing dependency: {e}")
    print("💡 Install with: pip3 install pdf2image Pillow requests pandas openpyxl")
    print("💡 Also install poppler: brew install poppler (on macOS)")
    sys.exit(1)
`]);

    checkDeps.stdout.on('data', (data) => {
      console.log(data.toString().trim());
    });

    checkDeps.stderr.on('data', (data) => {
      console.log(data.toString().trim());
    });

    checkDeps.on('close', (code) => {
      if (code === 0) {
        runOCRExtraction();
      } else {
        console.log('\n❌ Dependencies check failed. Please install required packages.');
        console.log('Run: pip3 install -r src/lib/e2b/requirements_ocr.txt');
        console.log('Also: brew install poppler (on macOS)');
      }
    });

  } catch (error) {
    console.error('❌ Error in test setup:', error.message);
  }
}

function runOCRExtraction() {
  console.log('\n🔄 Running OCR extraction...');
  
  const pythonScript = path.join(__dirname, 'src/lib/e2b/extract_transactions_ocr.py');
  const python = spawn('python3', [pythonScript], {
    env: { ...process.env, MISTRAL_API_KEY: process.env.MISTRAL_API_KEY }
  });

  python.stdout.on('data', (data) => {
    console.log(data.toString().trim());
  });

  python.stderr.on('data', (data) => {
    console.error(data.toString().trim());
  });

  python.on('close', (code) => {
    console.log(`\n🏁 OCR extraction completed with code: ${code}`);
    
    if (code === 0) {
      analyzeResults();
    } else {
      console.log('❌ OCR extraction failed. Check the error messages above.');
    }
  });
}

function analyzeResults() {
  console.log('\n📊 Analyzing extraction results...');
  
  const dataDir = path.join(__dirname, 'src/lib/e2b/extracted_data');
  const combinedFile = path.join(dataDir, 'combined_transactions.json');
  
  if (fs.existsSync(combinedFile)) {
    try {
      const data = JSON.parse(fs.readFileSync(combinedFile, 'utf8'));
      
      console.log('✅ Results Analysis:');
      console.log(`📄 Bank Transactions: ${data.bank_transactions?.length || 0}`);
      console.log(`📊 Ledger Transactions: ${data.ledger_transactions?.length || 0}`);
      console.log(`🔧 Extraction Method: ${data.metadata?.extraction_method || 'unknown'}`);
      console.log(`📅 Extraction Date: ${data.metadata?.extraction_date || 'unknown'}`);
      
      if (data.bank_transactions?.length > 10) {
        console.log('🎉 SUCCESS: Extracted more than 10 bank transactions!');
        console.log('💡 This is a significant improvement over the previous 5 transactions.');
        
        // Show sample transactions
        console.log('\n📋 Sample Bank Transactions:');
        data.bank_transactions.slice(0, 5).forEach((tx, i) => {
          console.log(`${i+1}. ${tx.date} - ${tx.description} - $${tx.amount}`);
        });
        
        if (data.bank_transactions.length > 100) {
          console.log('🚀 EXCELLENT: Extracted 100+ transactions! OCR is working well.');
        }
      } else {
        console.log('⚠️  Still only extracted a few transactions. OCR may need tuning.');
      }
      
      // Check for potential matching
      const bankTxWithRefs = data.bank_transactions?.filter(tx => tx.reference) || [];
      const ledgerTxWithRefs = data.ledger_transactions?.filter(tx => tx.reference) || [];
      
      console.log(`\n🔗 Potential Matching Analysis:`);
      console.log(`   Bank transactions with references: ${bankTxWithRefs.length}`);
      console.log(`   Ledger transactions with references: ${ledgerTxWithRefs.length}`);
      
      // Look for common references
      const bankRefs = new Set(bankTxWithRefs.map(tx => tx.reference));
      const ledgerRefs = new Set(ledgerTxWithRefs.map(tx => tx.reference));
      const commonRefs = [...bankRefs].filter(ref => ledgerRefs.has(ref));
      
      console.log(`   Common references found: ${commonRefs.length}`);
      if (commonRefs.length > 0) {
        console.log('✅ Good! Found common references for matching.');
        console.log('Sample common references:', commonRefs.slice(0, 3));
      }
      
    } catch (error) {
      console.error('❌ Error analyzing results:', error.message);
    }
  } else {
    console.log('❌ Combined transactions file not found. Extraction may have failed.');
  }
  
  console.log('\n🎯 Next Steps:');
  console.log('1. If extraction was successful, run: node seed-test-data.js');
  console.log('2. Then start the app: npm run dev');
  console.log('3. Test the reconciliation workflow with real data');
}

// Run the test
testOCRExtraction();
