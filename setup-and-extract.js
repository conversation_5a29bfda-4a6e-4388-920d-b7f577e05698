#!/usr/bin/env node

/**
 * Complete Setup and OCR Extraction Script
 * 
 * This script sets up the environment and runs Mistral OCR extraction
 * to replace inaccurate bank statement data with properly extracted transactions.
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

console.log('🚀 Accounting App - OCR Setup & Extraction');
console.log('==========================================');

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Helper function to ask questions
function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.trim());
    });
  });
}

// Helper function to run shell commands
function runCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    console.log(`🔄 Running: ${command} ${args.join(' ')}`);
    
    const process = spawn(command, args, { 
      stdio: options.silent ? 'pipe' : 'inherit',
      env: { ...process.env, ...options.env }
    });
    
    let stdout = '';
    let stderr = '';
    
    if (options.silent) {
      process.stdout.on('data', (data) => {
        stdout += data.toString();
      });
      
      process.stderr.on('data', (data) => {
        stderr += data.toString();
      });
    }
    
    process.on('close', (code) => {
      resolve({ code, stdout, stderr });
    });
    
    process.on('error', (error) => {
      reject(error);
    });
  });
}

// Check if Mistral API key is available
async function setupMistralApiKey() {
  console.log('\n🔑 Setting up Mistral API Key');
  console.log('=============================');
  
  if (process.env.MISTRAL_API_KEY) {
    console.log('✅ MISTRAL_API_KEY is already set');
    return process.env.MISTRAL_API_KEY;
  }
  
  console.log('❌ MISTRAL_API_KEY not found in environment variables');
  console.log('');
  console.log('🔗 To get your API key:');
  console.log('   1. Visit: https://console.mistral.ai/');
  console.log('   2. Sign up or log in');
  console.log('   3. Go to API Keys section');
  console.log('   4. Create a new API key');
  console.log('');
  
  const apiKey = await askQuestion('Please enter your Mistral API key: ');
  
  if (!apiKey) {
    console.log('❌ No API key provided. Exiting...');
    process.exit(1);
  }
  
  // Validate API key format (basic check)
  if (!apiKey.startsWith('mistral-') && apiKey.length < 20) {
    console.log('⚠️  Warning: API key format looks unusual');
    const confirm = await askQuestion('Continue anyway? (y/N): ');
    if (confirm.toLowerCase() !== 'y') {
      process.exit(1);
    }
  }
  
  console.log('✅ API key received');
  return apiKey;
}

// Install Python dependencies
async function installPythonDependencies() {
  console.log('\n📦 Installing Python Dependencies');
  console.log('=================================');
  
  const requiredPackages = [
    'pdf2image==1.17.0',
    'Pillow==10.0.1', 
    'requests==2.31.0',
    'pandas==2.1.1',
    'openpyxl==3.1.2'
  ];
  
  console.log('Installing required Python packages...');
  
  try {
    const result = await runCommand('pip3', ['install', ...requiredPackages]);
    
    if (result.code === 0) {
      console.log('✅ Python packages installed successfully');
    } else {
      console.log('❌ Failed to install Python packages');
      console.log('💡 Try running manually: pip3 install pdf2image Pillow requests pandas openpyxl');
      process.exit(1);
    }
  } catch (error) {
    console.log('❌ Error installing Python packages:', error.message);
    console.log('💡 Make sure Python 3 and pip are installed');
    process.exit(1);
  }
}

// Install poppler (required for pdf2image)
async function installPoppler() {
  console.log('\n🔧 Installing Poppler (PDF processing)');
  console.log('=====================================');
  
  // Check if poppler is already installed
  try {
    const result = await runCommand('pdftoppm', ['-h'], { silent: true });
    if (result.code === 0) {
      console.log('✅ Poppler is already installed');
      return;
    }
  } catch (error) {
    // Poppler not found, need to install
  }
  
  console.log('Installing poppler...');
  
  // Detect OS and install accordingly
  const platform = process.platform;
  
  try {
    if (platform === 'darwin') {
      // macOS
      console.log('Detected macOS - installing with Homebrew...');
      const result = await runCommand('brew', ['install', 'poppler']);
      
      if (result.code === 0) {
        console.log('✅ Poppler installed successfully');
      } else {
        console.log('❌ Failed to install poppler with Homebrew');
        console.log('💡 Please install manually: brew install poppler');
        process.exit(1);
      }
    } else if (platform === 'linux') {
      // Linux
      console.log('Detected Linux - installing with apt...');
      const result = await runCommand('sudo', ['apt-get', 'install', '-y', 'poppler-utils']);
      
      if (result.code === 0) {
        console.log('✅ Poppler installed successfully');
      } else {
        console.log('❌ Failed to install poppler with apt');
        console.log('💡 Please install manually: sudo apt-get install poppler-utils');
        process.exit(1);
      }
    } else {
      console.log('❌ Unsupported platform for automatic poppler installation');
      console.log('💡 Please install poppler manually:');
      console.log('   Windows: Download from https://poppler.freedesktop.org/');
      console.log('   macOS: brew install poppler');
      console.log('   Linux: sudo apt-get install poppler-utils');
      process.exit(1);
    }
  } catch (error) {
    console.log('❌ Error installing poppler:', error.message);
    process.exit(1);
  }
}

// Run the OCR extraction
async function runOCRExtraction(apiKey) {
  console.log('\n🔄 Running Mistral OCR Extraction');
  console.log('=================================');
  console.log('This may take 2-5 minutes depending on the PDF size...');
  console.log('');
  
  const scriptPath = path.join(__dirname, 'src/lib/e2b/extract_transactions_ocr.py');
  
  try {
    const result = await runCommand('python3', [scriptPath], {
      env: { MISTRAL_API_KEY: apiKey }
    });
    
    if (result.code === 0) {
      console.log('✅ OCR extraction completed successfully!');
    } else {
      console.log('❌ OCR extraction failed!');
      console.log('Check the output above for error details');
      process.exit(1);
    }
  } catch (error) {
    console.log('❌ Failed to run OCR extraction script!');
    console.log('Error:', error.message);
    process.exit(1);
  }
}

// Verify the extracted data
async function verifyExtractedData() {
  console.log('\n🔍 Verifying Extracted Data');
  console.log('===========================');
  
  const dataDir = path.join(__dirname, 'src/lib/e2b/extracted_data');
  const bankTransactionsFile = path.join(dataDir, 'bank_transactions.json');
  const ledgerTransactionsFile = path.join(dataDir, 'ledger_transactions.json');
  const combinedFile = path.join(dataDir, 'combined_transactions.json');
  
  try {
    // Check bank transactions
    if (fs.existsSync(bankTransactionsFile)) {
      const bankData = JSON.parse(fs.readFileSync(bankTransactionsFile, 'utf8'));
      console.log(`📊 Bank transactions: ${bankData.length} transactions extracted`);
      
      if (bankData.length > 5) {
        console.log('🎉 SUCCESS: Extracted significantly more transactions than the previous 5!');
        
        // Show sample transactions
        console.log('\n📋 Sample transactions:');
        bankData.slice(0, 3).forEach((tx, i) => {
          console.log(`   ${i + 1}. ${tx.date} - ${tx.description} - $${tx.amount || tx.credit || tx.debit}`);
        });
        if (bankData.length > 3) {
          console.log(`   ... and ${bankData.length - 3} more transactions`);
        }
      } else {
        console.log('⚠️  Warning: Still only extracted 5 or fewer transactions');
        console.log('   This might indicate OCR issues or API problems');
      }
    } else {
      console.log('❌ Bank transactions file not found');
    }
    
    // Check ledger transactions
    if (fs.existsSync(ledgerTransactionsFile)) {
      const ledgerData = JSON.parse(fs.readFileSync(ledgerTransactionsFile, 'utf8'));
      console.log(`📊 Ledger transactions: ${ledgerData.length} transactions extracted`);
    } else {
      console.log('❌ Ledger transactions file not found');
    }
    
    // Check combined data
    if (fs.existsSync(combinedFile)) {
      const combinedData = JSON.parse(fs.readFileSync(combinedFile, 'utf8'));
      const bankCount = combinedData.metadata?.bank_transaction_count || 0;
      const ledgerCount = combinedData.metadata?.ledger_transaction_count || 0;
      
      console.log(`📊 Combined data: ${bankCount} bank + ${ledgerCount} ledger transactions`);
      console.log(`🔧 Extraction method: ${combinedData.metadata?.extraction_method || 'unknown'}`);
      console.log(`🕐 Extraction date: ${combinedData.metadata?.extraction_date || 'unknown'}`);
      
      return { bankCount, ledgerCount, success: true };
    } else {
      console.log('❌ Combined transactions file not found');
      return { success: false };
    }
    
  } catch (error) {
    console.log('❌ Error verifying extracted data:', error.message);
    return { success: false };
  }
}

// Show next steps
function showNextSteps(extractionResults) {
  console.log('\n🎯 Next Steps');
  console.log('=============');
  
  if (extractionResults.success) {
    console.log('✅ OCR extraction completed successfully!');
    console.log('');
    console.log('🚀 To test the complete workflow:');
    console.log('   1. Start the development server: npm run dev');
    console.log('   2. Navigate to: http://localhost:3000/dashboard');
    console.log('   3. Check the Files page to see updated transaction counts');
    console.log('   4. Test the reconciliation workflow with the new data');
    console.log('   5. Run API tests: node test-api-endpoints.js');
    console.log('');
    console.log('📊 Your app now has:');
    console.log(`   • ${extractionResults.bankCount} bank transactions (OCR extracted)`);
    console.log(`   • ${extractionResults.ledgerCount} ledger transactions (Excel parsed)`);
    console.log('');
    console.log('🎉 The accounting app should now work end-to-end with accurate data!');
  } else {
    console.log('❌ Extraction had issues. Please check the logs above.');
    console.log('');
    console.log('🔧 Troubleshooting:');
    console.log('   1. Verify your Mistral API key is correct');
    console.log('   2. Check that all Python packages are installed');
    console.log('   3. Ensure the PDF file exists and is readable');
    console.log('   4. Try running the extraction script manually');
  }
}

// Main execution
async function main() {
  try {
    console.log('This script will:');
    console.log('1. Set up your Mistral API key');
    console.log('2. Install required Python dependencies');
    console.log('3. Install poppler for PDF processing');
    console.log('4. Run OCR extraction on the bank statement PDF');
    console.log('5. Replace the inaccurate 5-transaction data with real extracted data');
    console.log('');
    
    const proceed = await askQuestion('Continue? (Y/n): ');
    if (proceed.toLowerCase() === 'n') {
      console.log('Cancelled by user');
      process.exit(0);
    }
    
    const apiKey = await setupMistralApiKey();
    await installPythonDependencies();
    await installPoppler();
    await runOCRExtraction(apiKey);
    const results = await verifyExtractedData();
    showNextSteps(results);
    
  } catch (error) {
    console.log('❌ Setup and extraction failed:', error.message);
    process.exit(1);
  } finally {
    rl.close();
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { main };
