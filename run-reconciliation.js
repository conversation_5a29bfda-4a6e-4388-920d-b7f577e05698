#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js')

// Initialize Supabase client
const supabaseUrl = 'https://rrvdmpagsvhjdurfmqec.supabase.co'
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.SbZmq3nlQudzUN-xzJHj-NS23xnFVSlyx1RZWWZTy6U'

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function performReconciliation() {
  try {
    console.log('🔄 Starting reconciliation process...')

    const companyId = '5277bdf7-bbeb-4cab-bcbb-e55ee177b4dd'

    // 1. Get all bank and ledger transactions
    console.log('📊 Fetching transactions...')

    const { data: bankTransactions, error: bankError } = await supabase
      .from('transactions')
      .select('*')
      .eq('company_id', companyId)
      .eq('transaction_type', 'bank_statement')

    if (bankError) {
      console.error('❌ Error fetching bank transactions:', bankError)
      return
    }

    const { data: ledgerTransactions, error: ledgerError } = await supabase
      .from('transactions')
      .select('*')
      .eq('company_id', companyId)
      .eq('transaction_type', 'ledger_entry')

    if (ledgerError) {
      console.error('❌ Error fetching ledger transactions:', ledgerError)
      return
    }

    console.log(`🏦 Found ${bankTransactions.length} bank transactions`)
    console.log(`📚 Found ${ledgerTransactions.length} ledger transactions`)

    // 2. Match transactions using multiple strategies
    const matches = []
    const usedLedgerIds = new Set()

    // Strategy 1: Exact amount and date match
    console.log('🎯 Strategy 1: Exact amount and date matching...')
    for (const bankTxn of bankTransactions) {
      const matchingLedger = ledgerTransactions.find(ledgerTxn =>
        !usedLedgerIds.has(ledgerTxn.id) &&
        Math.abs(parseFloat(bankTxn.amount) - parseFloat(ledgerTxn.amount)) < 0.01 &&
        bankTxn.date === ledgerTxn.date
      )

      if (matchingLedger) {
        matches.push({
          bank_transaction_id: bankTxn.id,
          ledger_transaction_id: matchingLedger.id,
          match_confidence: 100,
          status: 'auto_matched',
          match_method: 'exact_amount_date'
        })
        usedLedgerIds.add(matchingLedger.id)
      }
    }

    // Strategy 2: Amount match within 3 days
    console.log('🎯 Strategy 2: Amount match within 3 days...')
    for (const bankTxn of bankTransactions) {
      // Skip if already matched
      if (matches.some(m => m.bank_transaction_id === bankTxn.id)) continue

      const bankDate = new Date(bankTxn.date)
      const matchingLedger = ledgerTransactions.find(ledgerTxn => {
        if (usedLedgerIds.has(ledgerTxn.id)) return false

        const ledgerDate = new Date(ledgerTxn.date)
        const daysDiff = Math.abs((bankDate - ledgerDate) / (1000 * 60 * 60 * 24))

        return Math.abs(parseFloat(bankTxn.amount) - parseFloat(ledgerTxn.amount)) < 0.01 &&
               daysDiff <= 3
      })

      if (matchingLedger) {
        matches.push({
          bank_transaction_id: bankTxn.id,
          ledger_transaction_id: matchingLedger.id,
          match_confidence: 85,
          status: 'auto_matched',
          match_method: 'amount_date_tolerance'
        })
        usedLedgerIds.add(matchingLedger.id)
      }
    }

    // Strategy 3: Reference number matching
    console.log('🎯 Strategy 3: Reference number matching...')
    for (const bankTxn of bankTransactions) {
      // Skip if already matched or no reference
      if (matches.some(m => m.bank_transaction_id === bankTxn.id) || !bankTxn.reference) continue

      const matchingLedger = ledgerTransactions.find(ledgerTxn =>
        !usedLedgerIds.has(ledgerTxn.id) &&
        ledgerTxn.reference &&
        ledgerTxn.reference === bankTxn.reference
      )

      if (matchingLedger) {
        matches.push({
          bank_transaction_id: bankTxn.id,
          ledger_transaction_id: matchingLedger.id,
          match_confidence: 90,
          status: 'auto_matched',
          match_method: 'reference_match'
        })
        usedLedgerIds.add(matchingLedger.id)
      }
    }

    console.log(`✅ Found ${matches.length} matches`)

    // 3. Insert matches into reconciliations table
    if (matches.length > 0) {
      console.log('💾 Saving matches to database...')

      const reconciliationsToInsert = matches.map(match => ({
        company_id: companyId,
        bank_transaction_id: match.bank_transaction_id,
        ledger_transaction_id: match.ledger_transaction_id,
        status: match.status,
        match_confidence: match.match_confidence,
        amount_difference: 0, // These are exact matches
        date_difference: 0,
        notes: `Auto-matched using ${match.match_method}`,
        match_type: match.match_method,
        matched_fields: { method: match.match_method },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }))

      const { data: insertResult, error: insertError } = await supabase
        .from('reconciliations')
        .insert(reconciliationsToInsert)

      if (insertError) {
        console.error('❌ Error inserting reconciliations:', insertError)
        return
      } else {
        console.log('✅ Successfully saved matches to database')
      }
    }

    // 4. Verify results
    console.log('🧪 Verifying results...')
    const { data: summaryData, error: summaryError } = await supabase
      .rpc('get_reconciliation_summary', { p_company_id: companyId })

    if (summaryError) {
      console.error('❌ Error getting summary:', summaryError)
    } else {
      console.log('📈 Final reconciliation summary:', summaryData[0])
    }

    console.log('🎉 Reconciliation process completed!')

  } catch (error) {
    console.error('❌ Error in reconciliation process:', error)
  }
}

// Run the reconciliation
performReconciliation()