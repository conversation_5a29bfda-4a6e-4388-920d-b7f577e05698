// Force show the Start Reconciliation button
const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

async function forceShowButton() {
  try {
    console.log('🔧 Forcing the Start Reconciliation button to appear...')
    
    // First, get the latest files to see their IDs
    const { data: latestFiles, error: fetchError } = await supabaseAdmin
      .from('files')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(5)
    
    if (fetchError) {
      console.error('❌ Error fetching files:', fetchError)
      return
    }
    
    console.log('📄 Latest files:', latestFiles.map(f => ({ 
      id: f.id, 
      name: f.original_filename, 
      status: f.status 
    })))
    
    // Update all files with "uploaded" status to ensure they're detected
    const filesToUpdate = latestFiles
      .filter(f => f.status === 'uploaded')
      .map(f => f.id)
    
    if (filesToUpdate.length === 0) {
      console.log('⚠️ No files with "uploaded" status found')
      return
    }
    
    // Add file_type metadata to help detection
    const updates = []
    
    for (const file of latestFiles) {
      const isPdf = file.original_filename.toLowerCase().includes('.pdf')
      const isExcel = file.original_filename.toLowerCase().includes('.xls')
      
      const metadata = {
        ...file.metadata,
        file_type: isPdf ? 'bank_statement' : (isExcel ? 'ledger' : file.file_type)
      }
      
      updates.push({
        id: file.id,
        metadata
      })
    }
    
    console.log('🔄 Updating file metadata to ensure proper detection...')
    
    for (const update of updates) {
      const { error } = await supabaseAdmin
        .from('files')
        .update({ metadata: update.metadata })
        .eq('id', update.id)
        
      if (error) {
        console.error(`❌ Error updating file ${update.id}:`, error)
      }
    }
    
    console.log('✅ Files updated successfully!')
    console.log('🚀 Now go back to http://localhost:3002/dashboard/seamless')
    console.log('💡 Refresh the page and the Start Reconciliation button should appear!')
    console.log('\n📋 If the button still doesn\'t appear, run this in your browser console:')
    console.log(`
    // Force show the Start Reconciliation button
    document.querySelectorAll('.mt-6.text-center').forEach(el => {
      if (el.innerHTML.includes('Start Reconciliation')) {
        el.style.display = 'block';
      }
    });
    
    // Force hasRequiredFiles to return true
    window.forceButtonShow = function() {
      const buttons = document.querySelectorAll('button');
      for (const button of buttons) {
        if (button.innerText.includes('Start Reconciliation')) {
          button.style.display = 'block';
          button.disabled = false;
          console.log('✅ Start Reconciliation button is now visible!');
          return;
        }
      }
    };
    
    forceButtonShow();
    `)
    
  } catch (error) {
    console.error('💥 Fix failed:', error)
  }
}

forceShowButton()
