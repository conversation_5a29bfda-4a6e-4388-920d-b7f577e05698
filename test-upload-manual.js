#!/usr/bin/env node

/**
 * Manual test to verify upload functionality
 */

const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const FormData = require('form-data')
const fetch = require('node-fetch')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function testUpload() {
  console.log('🧪 Testing manual upload...\n')
  
  try {
    // Create a test CSV file
    const testCsvContent = `Date,Description,Amount,Account
2025-01-15,Test Transaction 1,100.50,Checking
2025-01-16,Test Transaction 2,-50.25,Checking
2025-01-17,Test Transaction 3,200.00,Savings`

    const testFilePath = './test-ledger.csv'
    fs.writeFileSync(testFilePath, testCsvContent)
    console.log('✅ Created test CSV file')

    // Get user session (you'll need to be logged in)
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError || !session) {
      console.log('❌ No active session. Please log in to the app first.')
      console.log('   1. Visit http://localhost:3000/auth/login')
      console.log('   2. Log in with your account')
      console.log('   3. Run this test again')
      return
    }

    console.log('✅ Found active session for:', session.user.email)

    // Test ensure-company endpoint first
    console.log('\n📋 Testing ensure-company endpoint...')
    const companyResponse = await fetch('http://localhost:3000/api/ensure-company', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${session.access_token}`,
        'Content-Type': 'application/json',
      },
    })

    if (!companyResponse.ok) {
      const error = await companyResponse.text()
      console.log('❌ Ensure company failed:', error)
      return
    }

    const { companyId } = await companyResponse.json()
    console.log('✅ Company ID:', companyId)

    // Test file upload
    console.log('\n📤 Testing file upload...')
    const formData = new FormData()
    formData.append('file', fs.createReadStream(testFilePath))
    formData.append('documentType', 'ledger')
    formData.append('companyId', companyId)

    const uploadResponse = await fetch('http://localhost:3000/api/upload-file', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${session.access_token}`,
      },
      body: formData
    })

    if (!uploadResponse.ok) {
      const error = await uploadResponse.text()
      console.log('❌ Upload failed:', error)
      return
    }

    const uploadResult = await uploadResponse.json()
    console.log('✅ Upload successful!')
    console.log('   File ID:', uploadResult.fileId)
    console.log('   Storage Path:', uploadResult.storagePath)

    // Clean up test file
    fs.unlinkSync(testFilePath)
    console.log('✅ Cleaned up test file')

    console.log('\n🎉 Upload test completed successfully!')
    console.log('   Now check http://localhost:3000/dashboard/files to see your uploaded file')

  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
}

testUpload().catch(console.error)
