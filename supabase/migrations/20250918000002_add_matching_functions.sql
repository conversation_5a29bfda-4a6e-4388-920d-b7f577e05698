-- Migration: Add functions for transaction matching
-- Created: 2025-09-18
-- Description: Adds database functions to support transaction matching workflow

-- Function to save matching results
CREATE OR REPLACE FUNCTION save_matching_results(
    p_company_id UUID,
    p_exact_matches JSONB,
    p_fuzzy_matches JSONB,
    p_heuristic_matches JSON<PERSON>,
    p_created_by UUID
)
RETURNS VOID AS $$
DECLARE
    match_record JSONB;
    reconciliation_id UUID;
BEGIN
    -- Process exact matches
    FOR match_record IN SELECT * FROM jsonb_array_elements(p_exact_matches)
    LOOP
        INSERT INTO reconciliations (
            company_id,
            bank_transaction_id,
            ledger_transaction_id,
            status,
            match_confidence,
            amount_difference,
            date_difference,
            match_type,
            matched_fields,
            created_by
        ) VALUES (
            p_company_id,
            ((match_record->>'bankTransaction')::JSONB->>'id')::UUID,
            ((match_record->>'ledgerTransaction')::JSONB->>'id')::UUID,
            'auto_matched',
            (match_record->>'confidenceScore')::DECIMAL,
            (match_record->>'amountDifference')::DECIMAL,
            (match_record->>'dateDifference')::INTEGER,
            'exact',
            match_record->'matchedFields',
            p_created_by
        );
    END LOOP;

    -- Process fuzzy matches
    FOR match_record IN SELECT * FROM jsonb_array_elements(p_fuzzy_matches)
    LOOP
        INSERT INTO reconciliations (
            company_id,
            bank_transaction_id,
            ledger_transaction_id,
            status,
            match_confidence,
            amount_difference,
            date_difference,
            match_type,
            matched_fields,
            created_by
        ) VALUES (
            p_company_id,
            ((match_record->>'bankTransaction')::JSONB->>'id')::UUID,
            ((match_record->>'ledgerTransaction')::JSONB->>'id')::UUID,
            'auto_matched',
            (match_record->>'confidenceScore')::DECIMAL,
            (match_record->>'amountDifference')::DECIMAL,
            (match_record->>'dateDifference')::INTEGER,
            'fuzzy',
            match_record->'matchedFields',
            p_created_by
        );
    END LOOP;

    -- Process heuristic matches
    FOR match_record IN SELECT * FROM jsonb_array_elements(p_heuristic_matches)
    LOOP
        INSERT INTO reconciliations (
            company_id,
            bank_transaction_id,
            ledger_transaction_id,
            status,
            match_confidence,
            amount_difference,
            date_difference,
            match_type,
            matched_fields,
            created_by
        ) VALUES (
            p_company_id,
            ((match_record->>'bankTransaction')::JSONB->>'id')::UUID,
            ((match_record->>'ledgerTransaction')::JSONB->>'id')::UUID,
            'potential_match',
            (match_record->>'confidenceScore')::DECIMAL,
            (match_record->>'amountDifference')::DECIMAL,
            (match_record->>'dateDifference')::INTEGER,
            'heuristic',
            match_record->'matchedFields',
            p_created_by
        );
    END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get reconciliation summary
CREATE OR REPLACE FUNCTION get_reconciliation_summary(p_company_id UUID)
RETURNS TABLE (
    total_bank_transactions BIGINT,
    total_ledger_transactions BIGINT,
    matched_transactions BIGINT,
    unmatched_bank_transactions BIGINT,
    unmatched_ledger_transactions BIGINT,
    total_discrepancy DECIMAL,
    reconciliation_status TEXT
) AS $$
BEGIN
    -- Get transaction counts
    SELECT COUNT(*) INTO total_bank_transactions
    FROM transactions
    WHERE company_id = p_company_id AND transaction_type = 'bank_statement';

    SELECT COUNT(*) INTO total_ledger_transactions
    FROM transactions
    WHERE company_id = p_company_id AND transaction_type = 'ledger_entry';

    -- Get matched transactions count
    SELECT COUNT(*) INTO matched_transactions
    FROM reconciliations
    WHERE company_id = p_company_id 
    AND status IN ('matched', 'auto_matched', 'manual_matched');

    -- Calculate unmatched transactions
    SELECT 
        total_bank_transactions - COALESCE((
            SELECT COUNT(DISTINCT bank_transaction_id)
            FROM reconciliations
            WHERE company_id = p_company_id 
            AND bank_transaction_id IS NOT NULL
            AND status IN ('matched', 'auto_matched', 'manual_matched')
        ), 0) INTO unmatched_bank_transactions;

    SELECT 
        total_ledger_transactions - COALESCE((
            SELECT COUNT(DISTINCT ledger_transaction_id)
            FROM reconciliations
            WHERE company_id = p_company_id 
            AND ledger_transaction_id IS NOT NULL
            AND status IN ('matched', 'auto_matched', 'manual_matched')
        ), 0) INTO unmatched_ledger_transactions;

    -- Calculate total discrepancy
    SELECT COALESCE(SUM(ABS(amount_difference)), 0) INTO total_discrepancy
    FROM reconciliations
    WHERE company_id = p_company_id;

    -- Determine reconciliation status
    IF unmatched_bank_transactions = 0 AND unmatched_ledger_transactions = 0 AND total_discrepancy = 0 THEN
        reconciliation_status := 'balanced';
    ELSIF unmatched_bank_transactions > 0 OR unmatched_ledger_transactions > 0 OR total_discrepancy > 0 THEN
        reconciliation_status := 'discrepancies';
    ELSE
        reconciliation_status := 'pending';
    END IF;

    RETURN NEXT;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get transactions for reconciliation display
CREATE OR REPLACE FUNCTION get_reconciliation_transactions(
    p_company_id UUID,
    p_status TEXT DEFAULT NULL,
    p_limit INTEGER DEFAULT 50,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    date DATE,
    description TEXT,
    amount DECIMAL,
    reference VARCHAR,
    source TEXT,
    matched BOOLEAN,
    matched_with UUID,
    confidence DECIMAL,
    reconciliation_id UUID,
    match_type TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t.id,
        t.date,
        t.description,
        t.amount,
        t.reference,
        CASE 
            WHEN t.transaction_type = 'bank_statement' THEN 'bank'
            ELSE 'ledger'
        END as source,
        CASE 
            WHEN r.id IS NOT NULL AND r.status IN ('matched', 'auto_matched', 'manual_matched') THEN true
            ELSE false
        END as matched,
        CASE 
            WHEN t.transaction_type = 'bank_statement' THEN r.ledger_transaction_id
            ELSE r.bank_transaction_id
        END as matched_with,
        r.match_confidence as confidence,
        r.id as reconciliation_id,
        r.match_type
    FROM transactions t
    LEFT JOIN reconciliations r ON (
        (t.transaction_type = 'bank_statement' AND r.bank_transaction_id = t.id) OR
        (t.transaction_type = 'ledger_entry' AND r.ledger_transaction_id = t.id)
    )
    WHERE t.company_id = p_company_id
    AND (p_status IS NULL OR 
         (p_status = 'matched' AND r.status IN ('matched', 'auto_matched', 'manual_matched')) OR
         (p_status = 'unmatched' AND (r.id IS NULL OR r.status NOT IN ('matched', 'auto_matched', 'manual_matched'))))
    ORDER BY t.date DESC, t.created_at DESC
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to manually match transactions
CREATE OR REPLACE FUNCTION manual_match_transactions(
    p_company_id UUID,
    p_bank_transaction_id UUID,
    p_ledger_transaction_id UUID,
    p_user_id UUID,
    p_notes TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    reconciliation_id UUID;
    bank_tx RECORD;
    ledger_tx RECORD;
BEGIN
    -- Verify transactions belong to the company
    SELECT * INTO bank_tx FROM transactions 
    WHERE id = p_bank_transaction_id AND company_id = p_company_id AND transaction_type = 'bank_statement';
    
    SELECT * INTO ledger_tx FROM transactions 
    WHERE id = p_ledger_transaction_id AND company_id = p_company_id AND transaction_type = 'ledger_entry';
    
    IF bank_tx.id IS NULL OR ledger_tx.id IS NULL THEN
        RAISE EXCEPTION 'Invalid transaction IDs or transactions do not belong to company';
    END IF;

    -- Create or update reconciliation record
    INSERT INTO reconciliations (
        company_id,
        bank_transaction_id,
        ledger_transaction_id,
        status,
        match_confidence,
        amount_difference,
        date_difference,
        match_type,
        matched_fields,
        notes,
        created_by,
        reviewed_by,
        reviewed_at
    ) VALUES (
        p_company_id,
        p_bank_transaction_id,
        p_ledger_transaction_id,
        'manual_matched',
        100.00,
        ABS(bank_tx.amount - ledger_tx.amount),
        ABS(EXTRACT(DAY FROM bank_tx.date - ledger_tx.date)),
        'manual',
        '["manual"]'::JSONB,
        p_notes,
        p_user_id,
        p_user_id,
        NOW()
    ) RETURNING id INTO reconciliation_id;

    RETURN reconciliation_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to unmatch transactions
CREATE OR REPLACE FUNCTION unmatch_transactions(
    p_reconciliation_id UUID,
    p_company_id UUID,
    p_user_id UUID
)
RETURNS VOID AS $$
BEGIN
    -- Verify reconciliation belongs to company
    IF NOT EXISTS (
        SELECT 1 FROM reconciliations 
        WHERE id = p_reconciliation_id AND company_id = p_company_id
    ) THEN
        RAISE EXCEPTION 'Reconciliation not found or does not belong to company';
    END IF;

    -- Delete the reconciliation record
    DELETE FROM reconciliations 
    WHERE id = p_reconciliation_id AND company_id = p_company_id;

    -- Log the action
    PERFORM log_audit_event(
        p_company_id,
        'unmatch_transactions',
        'reconciliation',
        p_reconciliation_id,
        NULL,
        jsonb_build_object('unmatched_by', p_user_id)
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
