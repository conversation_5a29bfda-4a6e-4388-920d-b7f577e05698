-- Migration: Fix RLS Policies for Company Creation
-- Created: 2025-09-17
-- Description: Add missing INSERT policies for companies and company_users tables

-- Drop existing policies if they exist (to avoid conflicts)
DROP POLICY IF EXISTS "Users can create companies" ON companies;
DROP POLICY IF EXISTS "Users can join companies" ON company_users;
DROP POLICY IF EXISTS "Users can create own profile" ON user_profiles;
DROP POLICY IF EXISTS "Company admins can update companies" ON companies;
DROP POLICY IF EXISTS "Company admins can update company users" ON company_users;

-- Companies table: Allow users to create companies
CREATE POLICY "Users can create companies" ON companies
    FOR INSERT WITH CHECK (true);

-- Company users: Allow users to insert themselves into company_users when they create a company
CREATE POLICY "Users can join companies" ON company_users
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Also add missing INSERT policy for user_profiles (though it should be handled by trigger)
CREATE POLICY "Users can create own profile" ON user_profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Add missing UPDATE policies for companies (owners should be able to update their companies)
CREATE POLICY "Company admins can update companies" ON companies
    FOR UPDATE USING (
        id IN (
            SELECT company_id FROM company_users
            WHERE user_id = auth.uid() AND role = 'admin'
        )
    );

-- Add missing UPDATE policy for company_users
CREATE POLICY "Company admins can update company users" ON company_users
    FOR UPDATE USING (
        company_id IN (
            SELECT company_id FROM company_users cu
            WHERE cu.user_id = auth.uid() AND cu.role = 'admin'
        )
    );