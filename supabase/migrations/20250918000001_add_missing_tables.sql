-- Migration: Add missing tables for complete reconciliation workflow
-- Created: 2025-09-18
-- Description: Adds reports, journal_vouchers, and discrepancies tables

-- Create additional custom types
CREATE TYPE report_type AS ENUM ('reconciliation', 'discrepancy', 'journal_voucher', 'summary');
CREATE TYPE report_status AS ENUM ('generating', 'completed', 'failed');
CREATE TYPE discrepancy_type AS ENUM ('bank_only', 'ledger_only', 'amount_mismatch', 'date_discrepancy', 'reference_mismatch');
CREATE TYPE discrepancy_status AS ENUM ('pending', 'reviewed', 'resolved');

-- Reports table (for generated reports)
CREATE TABLE reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    type report_type NOT NULL,
    status report_status NOT NULL DEFAULT 'generating',
    date_from DATE,
    date_to DATE,
    bank_statement_file_id UUID REFERENCES files(id) ON DELETE SET NULL,
    ledger_file_id UUID REFERENCES files(id) ON DELETE SET NULL,
    report_data JSONB DEFAULT '{}',
    file_path TEXT, -- Path to generated report file
    file_size BIGINT,
    generated_by UUID NOT NULL REFERENCES user_profiles(id),
    generated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Discrepancies table (for tracking specific discrepancies)
CREATE TABLE discrepancies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    type discrepancy_type NOT NULL,
    status discrepancy_status NOT NULL DEFAULT 'pending',
    bank_transaction_id UUID REFERENCES transactions(id) ON DELETE SET NULL,
    ledger_transaction_id UUID REFERENCES transactions(id) ON DELETE SET NULL,
    amount_difference DECIMAL(15,2) DEFAULT 0.00,
    date_variance INTEGER DEFAULT 0, -- days difference
    description TEXT,
    category VARCHAR(255),
    notes TEXT,
    recommendation TEXT,
    reviewed_by UUID REFERENCES user_profiles(id),
    reviewed_at TIMESTAMP WITH TIME ZONE,
    resolved_by UUID REFERENCES user_profiles(id),
    resolved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Journal vouchers table (for journal voucher recommendations)
CREATE TABLE journal_vouchers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    report_id UUID REFERENCES reports(id) ON DELETE CASCADE,
    discrepancy_id UUID REFERENCES discrepancies(id) ON DELETE SET NULL,
    voucher_number VARCHAR(100),
    date DATE NOT NULL,
    description TEXT NOT NULL,
    entries JSONB NOT NULL, -- Array of debit/credit entries
    total_amount DECIMAL(15,2) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'draft', -- draft, approved, posted
    created_by UUID NOT NULL REFERENCES user_profiles(id),
    approved_by UUID REFERENCES user_profiles(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for reports
CREATE INDEX idx_reports_company_id ON reports(company_id);
CREATE INDEX idx_reports_type ON reports(type);
CREATE INDEX idx_reports_status ON reports(status);
CREATE INDEX idx_reports_generated_by ON reports(generated_by);
CREATE INDEX idx_reports_generated_at ON reports(generated_at);

-- Create indexes for discrepancies
CREATE INDEX idx_discrepancies_company_id ON discrepancies(company_id);
CREATE INDEX idx_discrepancies_type ON discrepancies(type);
CREATE INDEX idx_discrepancies_status ON discrepancies(status);
CREATE INDEX idx_discrepancies_bank_transaction ON discrepancies(bank_transaction_id);
CREATE INDEX idx_discrepancies_ledger_transaction ON discrepancies(ledger_transaction_id);

-- Create indexes for journal vouchers
CREATE INDEX idx_journal_vouchers_company_id ON journal_vouchers(company_id);
CREATE INDEX idx_journal_vouchers_report_id ON journal_vouchers(report_id);
CREATE INDEX idx_journal_vouchers_discrepancy_id ON journal_vouchers(discrepancy_id);
CREATE INDEX idx_journal_vouchers_date ON journal_vouchers(date);
CREATE INDEX idx_journal_vouchers_status ON journal_vouchers(status);

-- Enable RLS on new tables
ALTER TABLE reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE discrepancies ENABLE ROW LEVEL SECURITY;
ALTER TABLE journal_vouchers ENABLE ROW LEVEL SECURITY;

-- RLS policies for reports
CREATE POLICY "Users can view company reports" ON reports
    FOR SELECT USING (
        company_id IN (
            SELECT company_id FROM company_users
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create reports for their companies" ON reports
    FOR INSERT WITH CHECK (
        company_id IN (
            SELECT company_id FROM company_users
            WHERE user_id = auth.uid()
        )
        AND generated_by = auth.uid()
    );

CREATE POLICY "Users can update company reports" ON reports
    FOR UPDATE USING (
        company_id IN (
            SELECT company_id FROM company_users
            WHERE user_id = auth.uid()
        )
    );

-- RLS policies for discrepancies
CREATE POLICY "Users can view company discrepancies" ON discrepancies
    FOR SELECT USING (
        company_id IN (
            SELECT company_id FROM company_users
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can manage company discrepancies" ON discrepancies
    FOR ALL USING (
        company_id IN (
            SELECT company_id FROM company_users
            WHERE user_id = auth.uid()
        )
    );

-- RLS policies for journal vouchers
CREATE POLICY "Users can view company journal vouchers" ON journal_vouchers
    FOR SELECT USING (
        company_id IN (
            SELECT company_id FROM company_users
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can manage company journal vouchers" ON journal_vouchers
    FOR ALL USING (
        company_id IN (
            SELECT company_id FROM company_users
            WHERE user_id = auth.uid()
        )
    );

-- Add triggers for updated_at
CREATE TRIGGER update_reports_updated_at BEFORE UPDATE ON reports
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_discrepancies_updated_at BEFORE UPDATE ON discrepancies
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_journal_vouchers_updated_at BEFORE UPDATE ON journal_vouchers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add file_status enum values for the complete workflow
ALTER TYPE file_status ADD VALUE 'uploaded' AFTER 'uploading';
ALTER TYPE file_status ADD VALUE 'extracted' AFTER 'completed';

-- Update reconciliation_status enum to include more states
ALTER TYPE reconciliation_status ADD VALUE 'auto_matched' AFTER 'matched';
ALTER TYPE reconciliation_status ADD VALUE 'manual_matched' AFTER 'auto_matched';
ALTER TYPE reconciliation_status ADD VALUE 'potential_match' AFTER 'manual_matched';

-- Add match_type to reconciliations table
ALTER TABLE reconciliations ADD COLUMN match_type VARCHAR(50) DEFAULT 'auto';
ALTER TABLE reconciliations ADD COLUMN matched_fields JSONB DEFAULT '[]';
ALTER TABLE reconciliations ADD COLUMN created_by UUID REFERENCES user_profiles(id);

-- Create function to automatically create discrepancies from unmatched transactions
CREATE OR REPLACE FUNCTION create_discrepancies_from_unmatched()
RETURNS TRIGGER AS $$
BEGIN
    -- Create discrepancy for bank-only transactions
    IF NEW.bank_transaction_id IS NOT NULL AND NEW.ledger_transaction_id IS NULL THEN
        INSERT INTO discrepancies (
            company_id,
            type,
            bank_transaction_id,
            description,
            recommendation
        ) VALUES (
            NEW.company_id,
            'bank_only',
            NEW.bank_transaction_id,
            'Bank transaction without matching ledger entry',
            'Create corresponding ledger entry or investigate if transaction is valid'
        );
    END IF;
    
    -- Create discrepancy for ledger-only transactions
    IF NEW.ledger_transaction_id IS NOT NULL AND NEW.bank_transaction_id IS NULL THEN
        INSERT INTO discrepancies (
            company_id,
            type,
            ledger_transaction_id,
            description,
            recommendation
        ) VALUES (
            NEW.company_id,
            'ledger_only',
            NEW.ledger_transaction_id,
            'Ledger transaction without matching bank entry',
            'Verify if bank transaction exists or if ledger entry is correct'
        );
    END IF;
    
    -- Create discrepancy for amount mismatches
    IF NEW.amount_difference != 0 AND NEW.amount_difference IS NOT NULL THEN
        INSERT INTO discrepancies (
            company_id,
            type,
            bank_transaction_id,
            ledger_transaction_id,
            amount_difference,
            description,
            recommendation
        ) VALUES (
            NEW.company_id,
            'amount_mismatch',
            NEW.bank_transaction_id,
            NEW.ledger_transaction_id,
            NEW.amount_difference,
            'Amount difference between bank and ledger transactions',
            'Review transaction amounts and create adjustment entry if needed'
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically create discrepancies
CREATE TRIGGER create_discrepancies_trigger
    AFTER INSERT ON reconciliations
    FOR EACH ROW
    WHEN (NEW.status = 'discrepancy')
    EXECUTE FUNCTION create_discrepancies_from_unmatched();
