#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js')

// Initialize Supabase client
const supabaseUrl = 'https://rrvdmpagsvhjdurfmqec.supabase.co'
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJydmRtcGFnc3ZoamR1cmZtcWVjIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1ODEzMDM1MiwiZXhwIjoyMDczNzA2MzUyfQ.SbZmq3nlQudzUN-xzJHj-NS23xnFVSlyx1RZWWZTy6U'

const supabase = createClient(supabaseUrl, supabaseServiceKey)

// AI-powered fuzzy matching functions
function extractFinReference(text) {
  // Extract FIN/XXXX/YY or FIN/XXXX/YYYY patterns
  const finMatch = text.match(/FIN\/(\d+)\/(\d+)/i)
  if (finMatch) {
    const [, number, year] = finMatch
    // Normalize year to 4 digits
    const normalizedYear = year.length === 2 ? `20${year}` : year
    return {
      original: finMatch[0],
      number: number,
      year: normalizedYear,
      normalized: `FIN/${number}/${normalizedYear}`
    }
  }
  return null
}

function calculateFinSimilarity(ref1, ref2) {
  // If both have FIN patterns, compare them
  const fin1 = extractFinReference(ref1)
  const fin2 = extractFinReference(ref2)

  if (fin1 && fin2) {
    // If same FIN number and year, perfect match
    if (fin1.number === fin2.number && fin1.year === fin2.year) {
      return 100
    }
    // If same FIN number but different year format (25 vs 2025), very high match
    if (fin1.number === fin2.number) {
      return 95
    }
  }

  // Fallback to simple string similarity
  return calculateStringSimilarity(ref1, ref2)
}

function calculateStringSimilarity(str1, str2) {
  if (!str1 || !str2) return 0
  if (str1 === str2) return 100

  // Simple Levenshtein-based similarity
  const longer = str1.length > str2.length ? str1 : str2
  const shorter = str1.length > str2.length ? str2 : str1

  if (longer.length === 0) return 100

  const editDistance = levenshteinDistance(longer, shorter)
  return Math.round((longer.length - editDistance) / longer.length * 100)
}

function levenshteinDistance(str1, str2) {
  const matrix = []

  for (let i = 0; i <= str2.length; i++) {
    matrix[i] = [i]
  }

  for (let j = 0; j <= str1.length; j++) {
    matrix[0][j] = j
  }

  for (let i = 1; i <= str2.length; i++) {
    for (let j = 1; j <= str1.length; j++) {
      if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
        matrix[i][j] = matrix[i - 1][j - 1]
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j - 1] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j] + 1
        )
      }
    }
  }

  return matrix[str2.length][str1.length]
}

function calculateDateDifference(date1, date2) {
  const d1 = new Date(date1)
  const d2 = new Date(date2)
  return Math.abs((d1 - d2) / (1000 * 60 * 60 * 24)) // Days difference
}

async function performAIPoweredReconciliation() {
  try {
    console.log('🤖 Starting AI-powered reconciliation with real data...')

    const companyId = '5277bdf7-bbeb-4cab-bcbb-e55ee177b4dd'

    // Clear existing reconciliations
    await supabase.from('reconciliations').delete().eq('company_id', companyId)

    // 1. Get all real transactions
    console.log('📊 Fetching real transaction data...')

    const { data: bankTransactions, error: bankError } = await supabase
      .from('transactions')
      .select('*')
      .eq('company_id', companyId)
      .eq('transaction_type', 'bank_statement')

    if (bankError) {
      console.error('❌ Error fetching bank transactions:', bankError)
      return
    }

    const { data: ledgerTransactions, error: ledgerError } = await supabase
      .from('transactions')
      .select('*')
      .eq('company_id', companyId)
      .eq('transaction_type', 'ledger_entry')

    if (ledgerError) {
      console.error('❌ Error fetching ledger transactions:', ledgerError)
      return
    }

    console.log(`🏦 Found ${bankTransactions.length} bank transactions`)
    console.log(`📚 Found ${ledgerTransactions.length} ledger transactions`)

    // 2. AI-powered matching with multiple strategies
    const matches = []
    const usedLedgerIds = new Set()

    console.log('🎯 Strategy 1: FIN reference pattern matching...')
    for (const bankTxn of bankTransactions) {
      const matchingLedger = ledgerTransactions.find(ledgerTxn => {
        if (usedLedgerIds.has(ledgerTxn.id)) return false

        // Check FIN pattern similarity in description vs reference
        const bankFinSimilarity = calculateFinSimilarity(bankTxn.description, ledgerTxn.reference)
        const refFinSimilarity = calculateFinSimilarity(bankTxn.reference, ledgerTxn.reference)

        // Also check if amounts match (with signs flipped potentially)
        const amountMatch = Math.abs(Math.abs(parseFloat(bankTxn.amount)) - Math.abs(parseFloat(ledgerTxn.amount))) < 0.01

        return (bankFinSimilarity >= 90 || refFinSimilarity >= 90) && amountMatch
      })

      if (matchingLedger) {
        const confidence = Math.max(
          calculateFinSimilarity(bankTxn.description, matchingLedger.reference),
          calculateFinSimilarity(bankTxn.reference, matchingLedger.reference)
        )

        matches.push({
          bank_transaction_id: bankTxn.id,
          ledger_transaction_id: matchingLedger.id,
          match_confidence: confidence,
          status: 'auto_matched',
          match_method: 'fin_pattern_match',
          amount_difference: Math.abs(parseFloat(bankTxn.amount) - parseFloat(matchingLedger.amount)),
          date_difference: calculateDateDifference(bankTxn.date, matchingLedger.date)
        })
        usedLedgerIds.add(matchingLedger.id)

        if (matches.length % 50 === 0) {
          console.log(`   Found ${matches.length} FIN pattern matches so far...`)
        }
      }
    }

    console.log(`✅ FIN pattern matching found ${matches.length} matches`)

    // 3. Strategy 2: Exact amount and close date matching for remaining transactions
    console.log('🎯 Strategy 2: Amount and date matching...')
    const initialMatchCount = matches.length

    for (const bankTxn of bankTransactions) {
      // Skip if already matched
      if (matches.some(m => m.bank_transaction_id === bankTxn.id)) continue

      const bankDate = new Date(bankTxn.date)
      const matchingLedger = ledgerTransactions.find(ledgerTxn => {
        if (usedLedgerIds.has(ledgerTxn.id)) return false

        const ledgerDate = new Date(ledgerTxn.date)
        const daysDiff = Math.abs((bankDate - ledgerDate) / (1000 * 60 * 60 * 24))

        return Math.abs(parseFloat(bankTxn.amount) - parseFloat(ledgerTxn.amount)) < 0.01 &&
               daysDiff <= 3
      })

      if (matchingLedger) {
        matches.push({
          bank_transaction_id: bankTxn.id,
          ledger_transaction_id: matchingLedger.id,
          match_confidence: 85,
          status: 'auto_matched',
          match_method: 'amount_date_match',
          amount_difference: Math.abs(parseFloat(bankTxn.amount) - parseFloat(matchingLedger.amount)),
          date_difference: calculateDateDifference(bankTxn.date, matchingLedger.date)
        })
        usedLedgerIds.add(matchingLedger.id)
      }
    }

    console.log(`✅ Amount/date matching found ${matches.length - initialMatchCount} additional matches`)

    console.log(`🎉 Total matches found: ${matches.length}`)

    // 4. Insert matches into reconciliations table
    if (matches.length > 0) {
      console.log('💾 Saving matches to database...')

      const reconciliationsToInsert = matches.map(match => ({
        company_id: companyId,
        bank_transaction_id: match.bank_transaction_id,
        ledger_transaction_id: match.ledger_transaction_id,
        status: match.status,
        match_confidence: match.match_confidence,
        amount_difference: match.amount_difference,
        date_difference: Math.round(match.date_difference),
        notes: `AI-matched using ${match.match_method}`,
        match_type: match.match_method,
        matched_fields: { method: match.match_method },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }))

      const { data: insertResult, error: insertError } = await supabase
        .from('reconciliations')
        .insert(reconciliationsToInsert)

      if (insertError) {
        console.error('❌ Error inserting reconciliations:', insertError)
        return
      } else {
        console.log('✅ Successfully saved matches to database')
      }
    }

    // 5. Verify results with database function
    console.log('🧪 Verifying final results...')
    const { data: summaryData, error: summaryError } = await supabase
      .rpc('get_reconciliation_summary', { p_company_id: companyId })

    if (summaryError) {
      console.error('❌ Error getting summary:', summaryError)
    } else {
      console.log('📈 Final reconciliation summary:', summaryData[0])
    }

    console.log('🎉 AI-powered reconciliation completed!')

  } catch (error) {
    console.error('❌ Error in AI reconciliation process:', error)
  }
}

// Run the AI-powered reconciliation
performAIPoweredReconciliation()