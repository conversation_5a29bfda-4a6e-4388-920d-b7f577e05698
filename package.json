{"name": "accounting-app", "version": "1.0.0", "description": "Accounting Discrepancy Finder - Bank Reconciliation MVP", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate-types": "supabase gen types typescript --project-id YOUR_PROJECT_ID > src/types/database.ts"}, "dependencies": {"@e2b/code-interpreter": "^2.0.0", "@google/generative-ai": "^0.24.1", "@hookform/resolvers": "^3.3.2", "@playwright/test": "^1.55.0", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.14", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-tooltip": "^1.2.8", "@supabase/ssr": "^0.7.0", "@supabase/supabase-js": "^2.39.0", "@tanstack/react-query": "^5.17.0", "@tanstack/react-query-devtools": "^5.89.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^17.2.2", "e2b": "^1.0.0", "form-data": "^4.0.4", "lucide-react": "^0.323.0", "next": "^14.0.0", "node-fetch": "^3.3.2", "react": "^18.0.0", "react-day-picker": "^9.10.0", "react-dom": "^18.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.48.2", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "^14.0.0", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}