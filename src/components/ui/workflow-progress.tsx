'use client'

import { cn } from '@/lib/utils'
import { CheckCircle, Circle, Clock, AlertTriangle } from 'lucide-react'

interface WorkflowStep {
  id: string
  title: string
  description: string
  status: 'pending' | 'current' | 'completed' | 'error'
}

interface WorkflowProgressProps {
  steps: WorkflowStep[]
  className?: string
}

export function WorkflowProgress({ steps, className }: WorkflowProgressProps) {
  return (
    <div className={cn("space-y-4", className)}>
      {steps.map((step, index) => {
        const isLast = index === steps.length - 1
        
        return (
          <div key={step.id} className="relative">
            {/* Connector line */}
            {!isLast && (
              <div 
                className={cn(
                  "absolute left-4 top-8 w-0.5 h-8 -ml-px",
                  step.status === 'completed' ? 'bg-green-500' : 'bg-gray-300'
                )}
              />
            )}
            
            {/* Step content */}
            <div className="flex items-start space-x-4">
              {/* Step icon */}
              <div className={cn(
                "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center border-2",
                step.status === 'completed' && 'bg-green-500 border-green-500 text-white',
                step.status === 'current' && 'bg-blue-500 border-blue-500 text-white',
                step.status === 'error' && 'bg-red-500 border-red-500 text-white',
                step.status === 'pending' && 'bg-white border-gray-300 text-gray-400'
              )}>
                {step.status === 'completed' && <CheckCircle className="w-5 h-5" />}
                {step.status === 'current' && <Clock className="w-4 h-4" />}
                {step.status === 'error' && <AlertTriangle className="w-4 h-4" />}
                {step.status === 'pending' && <Circle className="w-4 h-4" />}
              </div>
              
              {/* Step details */}
              <div className="flex-1 min-w-0">
                <h3 className={cn(
                  "text-sm font-medium",
                  step.status === 'completed' && 'text-green-900',
                  step.status === 'current' && 'text-blue-900',
                  step.status === 'error' && 'text-red-900',
                  step.status === 'pending' && 'text-gray-500'
                )}>
                  {step.title}
                </h3>
                <p className={cn(
                  "text-sm mt-1",
                  step.status === 'completed' && 'text-green-700',
                  step.status === 'current' && 'text-blue-700',
                  step.status === 'error' && 'text-red-700',
                  step.status === 'pending' && 'text-gray-500'
                )}>
                  {step.description}
                </p>
              </div>
            </div>
          </div>
        )
      })}
    </div>
  )
}

// Predefined workflow steps for bank reconciliation
export const RECONCILIATION_STEPS: WorkflowStep[] = [
  {
    id: 'upload',
    title: 'Upload Documents',
    description: 'Upload bank statements and ledger files',
    status: 'pending'
  },
  {
    id: 'processing',
    title: 'Extract Transactions',
    description: 'AI processes documents to extract transaction data',
    status: 'pending'
  },
  {
    id: 'matching',
    title: 'Match Transactions',
    description: 'Automatically match bank and ledger transactions',
    status: 'pending'
  },
  {
    id: 'review',
    title: 'Review Results',
    description: 'Review matches and resolve discrepancies',
    status: 'pending'
  },
  {
    id: 'complete',
    title: 'Generate Reports',
    description: 'Export reconciliation reports and journal vouchers',
    status: 'pending'
  }
]
