'use client'

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ArrowRight, CheckCircle, Clock, AlertTriangle, Upload, FileSearch, BarChart3 } from 'lucide-react'
import Link from 'next/link'
import { UIStatus } from '@/lib/utils/status-mapping'

interface NextStep {
  id: string
  title: string
  description: string
  action: string
  href: string
  priority: 'high' | 'medium' | 'low'
  icon: React.ComponentType<any>
  estimated?: string
}

interface NextStepsCardProps {
  title: string
  description: string
  steps: NextStep[]
  currentStatus?: UIStatus
}

export function NextStepsCard({ title, description, steps, currentStatus }: NextStepsCardProps) {
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200'
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low': return 'bg-green-100 text-green-800 border-green-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'files_uploaded': return <Upload className="h-5 w-5 text-blue-600" />
      case 'processing': return <Clock className="h-5 w-5 text-yellow-600 animate-pulse" />
      case 'ready_for_reconciliation': return <FileSearch className="h-5 w-5 text-green-600" />
      case 'completed': return <CheckCircle className="h-5 w-5 text-green-600" />
      case 'extracted': return <FileSearch className="h-5 w-5 text-green-600" />
      case 'failed': return <AlertTriangle className="h-5 w-5 text-red-600" />
      case 'no_files': return <Upload className="h-5 w-5 text-gray-600" />
      default: return <AlertTriangle className="h-5 w-5 text-gray-600" />
    }
  }

  const getStatusMessage = (status?: string) => {
    switch (status) {
      case 'files_uploaded':
        return {
          message: 'Files uploaded successfully! Ready for processing.',
          color: 'text-blue-700 bg-blue-50 border-blue-200'
        }
      case 'processing':
        return {
          message: 'Processing your documents... This may take a few minutes.',
          color: 'text-yellow-700 bg-yellow-50 border-yellow-200'
        }
      case 'ready_for_reconciliation':
        return {
          message: 'Documents processed! Ready to start reconciliation.',
          color: 'text-green-700 bg-green-50 border-green-200'
        }
      case 'completed':
        return {
          message: 'Reconciliation completed successfully!',
          color: 'text-green-700 bg-green-50 border-green-200'
        }
      case 'extracted':
        return {
          message: 'Data extracted successfully! Ready for reconciliation.',
          color: 'text-green-700 bg-green-50 border-green-200'
        }
      case 'failed':
        return {
          message: 'Processing failed. Please try again.',
          color: 'text-red-700 bg-red-50 border-red-200'
        }
      case 'no_files':
        return {
          message: 'Upload your documents to get started.',
          color: 'text-gray-700 bg-gray-50 border-gray-200'
        }
      default:
        return {
          message: 'Upload your documents to get started.',
          color: 'text-gray-700 bg-gray-50 border-gray-200'
        }
    }
  }

  const statusInfo = getStatusMessage(currentStatus)

  return (
    <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-indigo-50">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            {getStatusIcon(currentStatus)}
            <div>
              <CardTitle className="text-blue-900">{title}</CardTitle>
              <CardDescription className="text-blue-700 mt-1">
                {description}
              </CardDescription>
            </div>
          </div>
        </div>
        
        {/* Status message */}
        <div className={`mt-4 p-3 rounded-lg border ${statusInfo.color}`}>
          <p className="text-sm font-medium">{statusInfo.message}</p>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-4">
          {steps.map((step, index) => {
            const Icon = step.icon
            return (
              <div key={step.id} className="flex items-center justify-between p-4 bg-white rounded-lg border border-blue-100 hover:border-blue-200 transition-colors">
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-sm font-semibold text-blue-600">{index + 1}</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Icon className="h-5 w-5 text-blue-600" />
                    <div>
                      <h4 className="font-medium text-gray-900">{step.title}</h4>
                      <p className="text-sm text-gray-600">{step.description}</p>
                      {step.estimated && (
                        <p className="text-xs text-gray-500 mt-1">Est. {step.estimated}</p>
                      )}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <Badge className={getPriorityColor(step.priority)}>
                    {step.priority}
                  </Badge>
                  <Link href={step.href}>
                    <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                      {step.action}
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </Link>
                </div>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}

// Predefined next steps for different scenarios
export const getNextSteps = (scenario: UIStatus): NextStep[] => {
  switch (scenario) {
    case 'no_files':
      return [
        {
          id: 'upload_bank',
          title: 'Upload Bank Statement',
          description: 'Upload your PDF bank statement for the period you want to reconcile',
          action: 'Upload Now',
          href: '/dashboard/upload?type=bank',
          priority: 'high',
          icon: Upload,
          estimated: '2 min'
        },
        {
          id: 'upload_ledger',
          title: 'Upload Ledger File',
          description: 'Upload your Excel or CSV ledger file for the same period',
          action: 'Upload Now',
          href: '/dashboard/upload?type=ledger',
          priority: 'high',
          icon: Upload,
          estimated: '2 min'
        }
      ]
    
    case 'files_uploaded':
      return [
        {
          id: 'start_processing',
          title: 'Start Document Processing',
          description: 'Begin AI-powered extraction of transaction data from your files',
          action: 'Start Processing',
          href: '/dashboard/process',
          priority: 'high',
          icon: FileSearch,
          estimated: '3-5 min'
        },
        {
          id: 'view_files',
          title: 'Review Uploaded Files',
          description: 'Check your uploaded files and their status',
          action: 'View Files',
          href: '/dashboard/files',
          priority: 'medium',
          icon: FileSearch,
          estimated: '1 min'
        }
      ]
    
    case 'processing':
      return [
        {
          id: 'monitor_progress',
          title: 'Monitor Processing',
          description: 'Track the progress of document processing and transaction extraction',
          action: 'View Status',
          href: '/dashboard/process',
          priority: 'high',
          icon: Clock,
          estimated: 'In progress'
        }
      ]
    
    case 'ready_for_reconciliation':
    case 'extracted':
      return [
        {
          id: 'start_reconciliation',
          title: 'Start Reconciliation',
          description: 'Review automatic transaction matching and resolve discrepancies',
          action: 'Start Review',
          href: '/dashboard/reconciliation',
          priority: 'high',
          icon: FileSearch,
          estimated: '10-15 min'
        },
        {
          id: 'view_transactions',
          title: 'View Extracted Transactions',
          description: 'Review the transactions extracted from your documents',
          action: 'View Details',
          href: '/dashboard/transactions',
          priority: 'medium',
          icon: BarChart3,
          estimated: '5 min'
        }
      ]
    
    case 'failed':
      return [
        {
          id: 'retry_processing',
          title: 'Retry Processing',
          description: 'Try processing your documents again',
          action: 'Retry',
          href: '/dashboard/process',
          priority: 'high',
          icon: FileSearch,
          estimated: '3-5 min'
        },
        {
          id: 'upload_new',
          title: 'Upload New Documents',
          description: 'Upload different documents if the current ones are problematic',
          action: 'Upload New',
          href: '/dashboard/upload',
          priority: 'medium',
          icon: Upload,
          estimated: '2 min'
        }
      ]
    
    default:
      return []
  }
}
