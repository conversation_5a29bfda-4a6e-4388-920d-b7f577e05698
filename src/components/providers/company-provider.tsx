'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { useAuth } from './auth-provider'
import { createClient } from '@/lib/supabase'

interface Company {
  id: string
  name: string
  created_at: string
  updated_at: string
}

interface CompanyContextType {
  company: Company | null
  loading: boolean
  refetch: () => Promise<void>
}

const CompanyContext = createContext<CompanyContextType | undefined>(undefined)

export function CompanyProvider({ children }: { children: React.ReactNode }) {
  const { user } = useAuth()
  const [company, setCompany] = useState<Company | null>(null)
  const [loading, setLoading] = useState(true)
  const supabase = createClient()

  const fetchCompany = async () => {
    if (!user) {
      setCompany(null)
      setLoading(false)
      return
    }

    try {
      setLoading(true)

      // Get company for the user
      const { data: companyUser, error } = await supabase
        .from('company_users')
        .select(`
          company_id,
          companies (
            id,
            name,
            created_at,
            updated_at
          )
        `)
        .eq('user_id', user.id)
        .single()

      if (error) {
        console.error('Error fetching company:', error)
        setCompany(null)
      } else if (companyUser && (companyUser as any).companies) {
        setCompany((companyUser as any).companies as Company)
      } else {
        setCompany(null)
      }
    } catch (error) {
      console.error('Error in fetchCompany:', error)
      setCompany(null)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchCompany()
  }, [user])

  const refetch = async () => {
    await fetchCompany()
  }

  const value = {
    company,
    loading,
    refetch
  }

  return (
    <CompanyContext.Provider value={value}>
      {children}
    </CompanyContext.Provider>
  )
}

export function useCompany() {
  const context = useContext(CompanyContext)
  if (context === undefined) {
    throw new Error('useCompany must be used within a CompanyProvider')
  }
  return context
}
