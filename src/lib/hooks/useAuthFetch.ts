import { useCallback } from 'react'
import { createClient } from '@/lib/supabase'

/**
 * Custom hook for making authenticated API requests
 * This ensures that authentication tokens are properly included
 * Using the latest Supabase client implementation
 */
export function useAuthFetch() {
  const supabase = createClient()
  
  const authFetch = useCallback(async (url: string, options: RequestInit = {}) => {
    try {
      // Get the current session
      const { data: { session } } = await supabase.auth.getSession()
      
      if (!session) {
        throw new Error('No active session')
      }
      
      // Set up headers with authentication
      const headers = {
        ...options.headers,
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`
      }
      
      // Make the request with credentials and auth headers
      const response = await fetch(url, {
        ...options,
        credentials: 'include',
        headers
      })
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `Request failed with status ${response.status}`)
      }
      
      return await response.json()
    } catch (error) {
      console.error('Auth fetch error:', error)
      throw error
    }
  }, [supabase])
  
  return { authFetch }
}
