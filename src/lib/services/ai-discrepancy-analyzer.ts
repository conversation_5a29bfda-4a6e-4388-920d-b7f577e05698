import { createClient } from '@/lib/supabase';

export interface DiscrepancyAnalysis {
  id: string;
  companyId: string;
  bankTransactionId?: string;
  ledgerTransactionId?: string;
  discrepancyType: 'unmatched_bank' | 'unmatched_ledger' | 'amount_mismatch' | 'date_mismatch' | 'reference_mismatch';
  severity: 'low' | 'medium' | 'high' | 'critical';
  amount: number;
  description: string;
  aiExplanation?: string;
  aiRecommendation?: string;
  suggestedAction: 'manual_review' | 'auto_correct' | 'ignore' | 'investigate';
  confidence: number;
  metadata: {
    originalAmount?: number;
    differenceAmount?: number;
    dateDifference?: number;
    referenceSimilarity?: number;
    patternMatches?: string[];
    businessContext?: string;
  };
  status: 'pending' | 'analyzed' | 'resolved' | 'ignored';
  createdAt: Date;
  updatedAt: Date;
}

export interface AIAnalysisResult {
  explanation: string;
  recommendation: string;
  confidence: number;
  suggestedAction: 'manual_review' | 'auto_correct' | 'ignore' | 'investigate';
  reasoning: string[];
  riskFactors: string[];
}

export interface JournalVoucherSuggestion {
  id: string;
  description: string;
  debitAccount: string;
  creditAccount: string;
  amount: number;
  reference: string;
  explanation: string;
  confidence: number;
}

export class AIDiscrepancyAnalyzer {
  private supabase = createClient();
  private geminiApiKey: string;

  constructor() {
    this.geminiApiKey = process.env.GEMINI_API_KEY || '';
    if (!this.geminiApiKey) {
      console.warn('GEMINI_API_KEY not found. AI analysis will be disabled.');
    }
  }

  /**
   * Analyze discrepancies using AI
   */
  async analyzeDiscrepancies(companyId: string): Promise<DiscrepancyAnalysis[]> {
    console.log(`Starting AI discrepancy analysis for company: ${companyId}`);

    // Get unmatched transactions
    const { unmatchedBank, unmatchedLedger } = await this.getUnmatchedTransactions(companyId);

    const analyses: DiscrepancyAnalysis[] = [];

    // Analyze unmatched bank transactions
    for (const bankTx of unmatchedBank) {
      const analysis = await this.analyzeUnmatchedBankTransaction(bankTx, companyId);
      if (analysis) {
        analyses.push(analysis);
      }
    }

    // Analyze unmatched ledger transactions
    for (const ledgerTx of unmatchedLedger) {
      const analysis = await this.analyzeUnmatchedLedgerTransaction(ledgerTx, companyId);
      if (analysis) {
        analyses.push(analysis);
      }
    }

    // Analyze amount mismatches in matched transactions
    const amountMismatches = await this.analyzeAmountMismatches(companyId);
    analyses.push(...amountMismatches);

    // Save analyses to database
    await this.saveDiscrepancyAnalyses(analyses);

    console.log(`AI analysis completed: ${analyses.length} discrepancies analyzed`);

    return analyses;
  }

  /**
   * Analyze unmatched bank transaction
   */
  private async analyzeUnmatchedBankTransaction(bankTx: any, companyId: string): Promise<DiscrepancyAnalysis | null> {
    const aiResult = await this.getAIAnalysis(bankTx, null, 'unmatched_bank');

    return {
      id: `bank_${bankTx.id}`,
      companyId,
      bankTransactionId: bankTx.id,
      discrepancyType: 'unmatched_bank',
      severity: this.determineSeverity(bankTx.amount, aiResult.confidence),
      amount: bankTx.amount,
      description: bankTx.description || 'No description',
      aiExplanation: aiResult.explanation,
      aiRecommendation: aiResult.recommendation,
      suggestedAction: aiResult.suggestedAction,
      confidence: aiResult.confidence,
      metadata: {
        originalAmount: bankTx.amount,
        businessContext: aiResult.reasoning.join('; ')
      },
      status: 'pending',
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }

  /**
   * Analyze unmatched ledger transaction
   */
  private   async analyzeUnmatchedLedgerTransaction(ledgerTx: any, companyId: string): Promise<DiscrepancyAnalysis | null> {
    const aiResult = await this.getAIAnalysis(null, ledgerTx, 'unmatched_ledger');

    return {
      id: `ledger_${ledgerTx.id}`,
      companyId,
      bankTransactionId: undefined,
      ledgerTransactionId: ledgerTx.id,
      discrepancyType: 'unmatched_ledger',
      severity: this.determineSeverity(ledgerTx.amount, aiResult.confidence),
      amount: ledgerTx.amount,
      description: ledgerTx.description || 'No description',
      aiExplanation: aiResult.explanation,
      aiRecommendation: aiResult.recommendation,
      suggestedAction: aiResult.suggestedAction,
      confidence: aiResult.confidence,
      metadata: {
        originalAmount: ledgerTx.amount,
        businessContext: aiResult.reasoning.join('; ')
      },
      status: 'pending',
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }

  /**
   * Analyze amount mismatches in matched transactions
   */
  private async analyzeAmountMismatches(companyId: string): Promise<DiscrepancyAnalysis[]> {
    const { data: reconciliations } = await this.supabase
      .from('reconciliations')
      .select(`
        *,
        bank_transaction:bank_transaction_id(*),
        ledger_transaction:ledger_transaction_id(*)
      `)
      .eq('company_id', companyId)
      .not('amount_difference', 'is', null)
      .gt('amount_difference', 1); // Only significant differences

    if (!reconciliations) return [];

    const analyses: DiscrepancyAnalysis[] = [];

    for (const recon of reconciliations) {
      if ((recon as any).bank_transaction && (recon as any).ledger_transaction) {
        const aiResult = await this.getAIAnalysis(
          (recon as any).bank_transaction,
          (recon as any).ledger_transaction,
          'amount_mismatch'
        );

        analyses.push({
          id: `mismatch_${(recon as any).id}`,
          companyId,
          bankTransactionId: (recon as any).bank_transaction.id,
          ledgerTransactionId: (recon as any).ledger_transaction.id,
          discrepancyType: 'amount_mismatch',
          severity: this.determineSeverity((recon as any).amount_difference, aiResult.confidence),
          amount: (recon as any).amount_difference,
          description: `Amount mismatch: Bank ${(recon as any).bank_transaction.amount} vs Ledger ${(recon as any).ledger_transaction.amount}`,
          aiExplanation: aiResult.explanation,
          aiRecommendation: aiResult.recommendation,
          suggestedAction: aiResult.suggestedAction,
          confidence: aiResult.confidence,
          metadata: {
            originalAmount: (recon as any).bank_transaction.amount,
            differenceAmount: (recon as any).amount_difference,
            businessContext: aiResult.reasoning.join('; ')
          },
          status: 'pending',
          createdAt: new Date(),
          updatedAt: new Date()
        });
      }
    }

    return analyses;
  }

  /**
   * Get AI analysis using Gemini
   */
  private async getAIAnalysis(
    bankTx: any,
    ledgerTx: any,
    discrepancyType: string
  ): Promise<AIAnalysisResult> {
    if (!this.geminiApiKey) {
      return this.getFallbackAnalysis(bankTx, ledgerTx, discrepancyType);
    }

    try {
      const prompt = this.buildAnalysisPrompt(bankTx, ledgerTx, discrepancyType);

      const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent?key=${this.geminiApiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: prompt
            }]
          }],
          generationConfig: {
            temperature: 0.1,
            maxOutputTokens: 1000,
          }
        })
      });

      if (!response.ok) {
        throw new Error(`Gemini API error: ${response.statusText}`);
      }

      const data = await response.json();
      const analysisText = data.candidates?.[0]?.content?.parts?.[0]?.text || '';

      return this.parseAIAnalysis(analysisText);
    } catch (error) {
      console.error('Gemini API error:', error);
      return this.getFallbackAnalysis(bankTx, ledgerTx, discrepancyType);
    }
  }

  /**
   * Build analysis prompt for Gemini
   */
  private buildAnalysisPrompt(bankTx: any, ledgerTx: any, discrepancyType: string): string {
    const bankInfo = bankTx ? `
Bank Transaction:
- Date: ${bankTx.date}
- Amount: ${bankTx.amount}
- Description: ${bankTx.description || 'N/A'}
- Reference: ${bankTx.reference || 'N/A'}
- Check Number: ${bankTx.check_number || 'N/A'}
- FIN Reference: ${bankTx.fin_reference || 'N/A'}
` : '';

    const ledgerInfo = ledgerTx ? `
Ledger Transaction:
- Date: ${ledgerTx.date}
- Amount: ${ledgerTx.amount}
- Description: ${ledgerTx.description || 'N/A'}
- Reference: ${ledgerTx.reference || 'N/A'}
- Voucher Number: ${ledgerTx.voucher_number || 'N/A'}
- FIN Reference: ${ledgerTx.fin_reference || 'N/A'}
` : '';

    return `
You are an expert Ethiopian accounting and banking reconciliation specialist. Analyze this ${discrepancyType} and provide insights.

${bankInfo}
${ledgerInfo}

Discrepancy Type: ${discrepancyType}

Please provide analysis in this exact JSON format:
{
  "explanation": "Clear explanation of what might have caused this discrepancy",
  "recommendation": "Specific recommendation for resolution",
  "confidence": 0.85,
  "suggestedAction": "manual_review",
  "reasoning": ["Reason 1", "Reason 2", "Reason 3"],
  "riskFactors": ["Risk 1", "Risk 2"]
}

Consider:
1. Ethiopian banking patterns (FIN references, check numbers, RFSA)
2. Common reconciliation issues
3. Business context and timing
4. Risk assessment
5. Practical resolution steps

Suggested actions: manual_review, auto_correct, ignore, investigate
Confidence: 0.0 to 1.0
`;
  }

  /**
   * Parse AI analysis response
   */
  private parseAIAnalysis(analysisText: string): AIAnalysisResult {
    try {
      // Try to extract JSON from the response
      const jsonMatch = analysisText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return {
          explanation: parsed.explanation || 'AI analysis completed',
          recommendation: parsed.recommendation || 'Manual review recommended',
          confidence: parsed.confidence || 0.5,
          suggestedAction: parsed.suggestedAction || 'manual_review',
          reasoning: parsed.reasoning || [],
          riskFactors: parsed.riskFactors || []
        };
      }
    } catch (error) {
      console.error('Failed to parse AI analysis:', error);
    }

    return {
      explanation: analysisText.substring(0, 500) + '...',
      recommendation: 'Manual review recommended',
      confidence: 0.5,
      suggestedAction: 'manual_review',
      reasoning: ['AI analysis provided'],
      riskFactors: ['Requires human review']
    };
  }

  /**
   * Fallback analysis when AI is unavailable
   */
  private getFallbackAnalysis(bankTx: any, ledgerTx: any, discrepancyType: string): AIAnalysisResult {
    const amount = bankTx?.amount || ledgerTx?.amount || 0;
    const isHighValue = Math.abs(amount) > 10000;

    return {
      explanation: `Automatic analysis of ${discrepancyType}. Amount: ${amount}. ${isHighValue ? 'High-value transaction requiring attention.' : 'Standard transaction.'}`,
      recommendation: isHighValue ? 'Immediate manual review recommended' : 'Review when convenient',
      confidence: 0.3,
      suggestedAction: isHighValue ? 'investigate' : 'manual_review',
      reasoning: [
        `Discrepancy type: ${discrepancyType}`,
        `Amount: ${amount}`,
        isHighValue ? 'High-value transaction' : 'Standard transaction'
      ],
      riskFactors: isHighValue ? ['High monetary value', 'Potential financial impact'] : ['Standard reconciliation item']
    };
  }

  /**
   * Determine severity based on amount and confidence
   */
  private determineSeverity(amount: number, confidence: number): 'low' | 'medium' | 'high' | 'critical' {
    const absAmount = Math.abs(amount);

    if (absAmount > 100000) return 'critical';
    if (absAmount > 50000) return 'high';
    if (absAmount > 10000) return 'medium';
    return 'low';
  }

  /**
   * Generate journal voucher suggestions
   */
  async generateJournalVoucherSuggestions(companyId: string): Promise<JournalVoucherSuggestion[]> {
    const { data: discrepancies } = await this.supabase
      .from('discrepancies')
      .select('*')
      .eq('company_id', companyId)
      .eq('status', 'pending')
      .in('discrepancy_type', ['amount_mismatch', 'unmatched_bank', 'unmatched_ledger']);

    if (!discrepancies) return [];

    const suggestions: JournalVoucherSuggestion[] = [];

    for (const disc of discrepancies) {
      const suggestion = await this.generateJournalVoucherForDiscrepancy(disc);
      if (suggestion) {
        suggestions.push(suggestion);
      }
    }

    return suggestions;
  }

  /**
   * Generate journal voucher for specific discrepancy
   */
  private async generateJournalVoucherForDiscrepancy(disc: any): Promise<JournalVoucherSuggestion | null> {
    if (!this.geminiApiKey) {
      return this.getFallbackJournalVoucher(disc);
    }

    try {
      const prompt = `
Generate a journal voucher entry for this reconciliation discrepancy:

Discrepancy Type: ${disc.discrepancy_type}
Amount: ${disc.amount}
Description: ${disc.description}
AI Explanation: ${disc.ai_explanation || 'N/A'}

Provide journal voucher in this exact JSON format:
{
  "description": "Journal voucher description",
  "debitAccount": "Account to debit",
  "creditAccount": "Account to credit",
  "amount": ${Math.abs(disc.amount)},
  "reference": "Reference number",
  "explanation": "Explanation of the entry",
  "confidence": 0.85
}

Consider Ethiopian accounting standards and common reconciliation entries.
`;

      const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent?key=${this.geminiApiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: prompt
            }]
          }],
          generationConfig: {
            temperature: 0.1,
            maxOutputTokens: 500,
          }
        })
      });

      if (!response.ok) {
        throw new Error(`Gemini API error: ${response.statusText}`);
      }

      const data = await response.json();
      const voucherText = data.candidates?.[0]?.content?.parts?.[0]?.text || '';

      const jsonMatch = voucherText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return {
          id: `jv_${disc.id}`,
          description: parsed.description || 'Reconciliation adjustment',
          debitAccount: parsed.debitAccount || 'Suspense Account',
          creditAccount: parsed.creditAccount || 'Bank Account',
          amount: parsed.amount || Math.abs(disc.amount),
          reference: parsed.reference || `ADJ-${Date.now()}`,
          explanation: parsed.explanation || 'Reconciliation discrepancy adjustment',
          confidence: parsed.confidence || 0.5
        };
      }
    } catch (error) {
      console.error('Failed to generate journal voucher:', error);
    }

    return this.getFallbackJournalVoucher(disc);
  }

  /**
   * Fallback journal voucher
   */
  private getFallbackJournalVoucher(disc: any): JournalVoucherSuggestion {
    return {
      id: `jv_${disc.id}`,
      description: 'Reconciliation discrepancy adjustment',
      debitAccount: 'Suspense Account',
      creditAccount: 'Bank Account',
      amount: Math.abs(disc.amount),
      reference: `ADJ-${Date.now()}`,
      explanation: `Adjustment for ${disc.discrepancy_type}: ${disc.description}`,
      confidence: 0.3
    };
  }

  /**
   * Get unmatched transactions
   */
  private async getUnmatchedTransactions(companyId: string) {
    // Get all transactions
    const { data: bankTransactions } = await this.supabase
      .from('transactions')
      .select('*')
      .eq('company_id', companyId)
      .eq('transaction_type', 'bank_statement');

    const { data: ledgerTransactions } = await this.supabase
      .from('transactions')
      .select('*')
      .eq('company_id', companyId)
      .eq('transaction_type', 'ledger_entry');

    // Get matched transaction IDs
    const { data: reconciliations } = await this.supabase
      .from('reconciliations')
      .select('bank_transaction_id, ledger_transaction_id')
      .eq('company_id', companyId);

    const matchedBankIds = new Set(reconciliations?.map((r: any) => r.bank_transaction_id) || []);
    const matchedLedgerIds = new Set(reconciliations?.map((r: any) => r.ledger_transaction_id) || []);

    return {
      unmatchedBank: bankTransactions?.filter((t: any) => !matchedBankIds.has(t.id)) || [],
      unmatchedLedger: ledgerTransactions?.filter((t: any) => !matchedLedgerIds.has(t.id)) || []
    };
  }

  /**
   * Save discrepancy analyses to database
   */
  private async saveDiscrepancyAnalyses(analyses: DiscrepancyAnalysis[]): Promise<void> {
    const discrepancies = analyses.map(analysis => ({
      company_id: analysis.companyId,
      bank_transaction_id: analysis.bankTransactionId,
      ledger_transaction_id: analysis.ledgerTransactionId,
      discrepancy_type: analysis.discrepancyType,
      severity: analysis.severity,
      amount: analysis.amount,
      description: analysis.description,
      ai_explanation: analysis.aiExplanation,
      ai_recommendation: analysis.aiRecommendation,
      suggested_action: analysis.suggestedAction,
      confidence: analysis.confidence,
      status: analysis.status,
      metadata: analysis.metadata
    }));

    const { error } = await this.supabase
      .from('discrepancies')
      .upsert(discrepancies as any, { onConflict: 'company_id,bank_transaction_id,ledger_transaction_id' });

    if (error) {
      console.error('Error saving discrepancy analyses:', error);
      throw new Error(`Failed to save discrepancy analyses: ${error.message}`);
    }

    console.log(`Saved ${discrepancies.length} discrepancy analyses to database`);
  }
}

export default AIDiscrepancyAnalyzer;
