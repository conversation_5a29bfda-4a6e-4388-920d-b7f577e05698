import { createClient } from '@/lib/supabase'
import { Transaction, MatchingResult } from './transaction-matcher'

export type DiscrepancyType = 'bank_only' | 'ledger_only' | 'amount_mismatch' | 'date_discrepancy' | 'reference_mismatch'
export type DiscrepancyStatus = 'pending' | 'reviewed' | 'resolved'

export interface Discrepancy {
  id?: string
  type: DiscrepancyType
  status: DiscrepancyStatus
  bankTransaction?: Transaction
  ledgerTransaction?: Transaction
  amountDifference: number
  dateVariance: number
  description: string
  category?: string
  notes?: string
  recommendation: string
  reviewedBy?: string
  reviewedAt?: string
  resolvedBy?: string
  resolvedAt?: string
  createdAt?: string
}

export interface DiscrepancyAnalysisResult {
  discrepancies: Discrepancy[]
  statistics: {
    totalDiscrepancies: number
    bankOnlyCount: number
    ledgerOnlyCount: number
    amountMismatchCount: number
    dateMismatchCount: number
    referenceMismatchCount: number
    totalAmountDifference: number
    averageAmountDifference: number
    criticalDiscrepancies: number
    minorDiscrepancies: number
  }
  recommendations: {
    priority: 'high' | 'medium' | 'low'
    action: string
    description: string
    affectedTransactions: number
    estimatedImpact: number
  }[]
}

export interface JournalVoucherEntry {
  account: string
  debit?: number
  credit?: number
  description: string
}

export interface JournalVoucher {
  id?: string
  voucherNumber?: string
  date: string
  description: string
  entries: JournalVoucherEntry[]
  totalAmount: number
  discrepancyId?: string
  status: 'draft' | 'approved' | 'posted'
}

export class DiscrepancyAnalyzer {
  private supabase = createClient()

  /**
   * Analyze discrepancies from matching results
   */
  async analyzeDiscrepancies(
    companyId: string,
    matchingResult: MatchingResult
  ): Promise<DiscrepancyAnalysisResult> {
    const discrepancies: Discrepancy[] = []

    // Analyze bank-only transactions
    for (const bankTx of matchingResult.unmatchedBankTransactions) {
      discrepancies.push({
        type: 'bank_only',
        status: 'pending',
        bankTransaction: bankTx,
        amountDifference: Math.abs(bankTx.amount),
        dateVariance: 0,
        description: `Bank transaction without matching ledger entry: ${bankTx.description}`,
        category: this.categorizeTransaction(bankTx),
        recommendation: this.generateRecommendation('bank_only', bankTx)
      })
    }

    // Analyze ledger-only transactions
    for (const ledgerTx of matchingResult.unmatchedLedgerTransactions) {
      discrepancies.push({
        type: 'ledger_only',
        status: 'pending',
        ledgerTransaction: ledgerTx,
        amountDifference: Math.abs(ledgerTx.amount),
        dateVariance: 0,
        description: `Ledger transaction without matching bank entry: ${ledgerTx.description}`,
        category: this.categorizeTransaction(ledgerTx),
        recommendation: this.generateRecommendation('ledger_only', ledgerTx)
      })
    }

    // Analyze amount mismatches in matched transactions
    const allMatches = [
      ...matchingResult.exactMatches,
      ...matchingResult.fuzzyMatches,
      ...matchingResult.heuristicMatches
    ]

    for (const match of allMatches) {
      if (match.amountDifference > 0.01) {
        discrepancies.push({
          type: 'amount_mismatch',
          status: 'pending',
          bankTransaction: match.bankTransaction,
          ledgerTransaction: match.ledgerTransaction,
          amountDifference: match.amountDifference,
          dateVariance: match.dateDifference,
          description: `Amount mismatch between bank (${match.bankTransaction.amount}) and ledger (${match.ledgerTransaction.amount})`,
          category: 'amount_discrepancy',
          recommendation: this.generateAmountMismatchRecommendation(match)
        })
      }

      if (match.dateDifference > 0) {
        discrepancies.push({
          type: 'date_discrepancy',
          status: 'pending',
          bankTransaction: match.bankTransaction,
          ledgerTransaction: match.ledgerTransaction,
          amountDifference: match.amountDifference,
          dateVariance: match.dateDifference,
          description: `Date discrepancy: ${match.dateDifference} day(s) difference between bank and ledger dates`,
          category: 'timing_difference',
          recommendation: this.generateDateDiscrepancyRecommendation(match)
        })
      }
    }

    // Calculate statistics
    const statistics = this.calculateDiscrepancyStatistics(discrepancies)

    // Generate recommendations
    const recommendations = this.generateDiscrepancyRecommendations(discrepancies, statistics)

    return {
      discrepancies,
      statistics,
      recommendations
    }
  }

  /**
   * Save discrepancies to database
   */
  async saveDiscrepancies(
    companyId: string,
    discrepancies: Discrepancy[],
    userId: string
  ): Promise<void> {
    const discrepancyRecords = discrepancies.map(discrepancy => ({
      company_id: companyId,
      type: discrepancy.type,
      status: discrepancy.status,
      bank_transaction_id: discrepancy.bankTransaction?.id || null,
      ledger_transaction_id: discrepancy.ledgerTransaction?.id || null,
      amount_difference: discrepancy.amountDifference,
      date_variance: discrepancy.dateVariance,
      description: discrepancy.description,
      category: discrepancy.category,
      recommendation: discrepancy.recommendation
    }))

    const { error } = await (this.supabase as any)
      .from('discrepancies')
      .insert(discrepancyRecords)

    if (error) {
      console.error('Error saving discrepancies:', error)
      throw new Error(`Failed to save discrepancies: ${error.message}`)
    }
  }

  /**
   * Get discrepancies from database
   */
  async getDiscrepancies(
    companyId: string,
    filters?: {
      type?: DiscrepancyType
      status?: DiscrepancyStatus
      limit?: number
      offset?: number
    }
  ): Promise<{ discrepancies: Discrepancy[]; total: number }> {
    let query = (this.supabase as any)
      .from('discrepancies')
      .select(`
        *,
        bank_transaction:bank_transaction_id(*),
        ledger_transaction:ledger_transaction_id(*)
      `)
      .eq('company_id', companyId)
      .order('created_at', { ascending: false })

    if (filters?.type) {
      query = query.eq('type', filters.type)
    }

    if (filters?.status) {
      query = query.eq('status', filters.status)
    }

    if (filters?.limit) {
      query = query.range(filters.offset || 0, (filters.offset || 0) + filters.limit - 1)
    }

    const { data, error, count } = await query

    if (error) {
      console.error('Error fetching discrepancies:', error)
      throw new Error(`Failed to fetch discrepancies: ${error.message}`)
    }

    // Map database records to Discrepancy interface
    const discrepancies: Discrepancy[] = (data || []).map((record: any) => ({
      id: record.id,
      type: record.type as DiscrepancyType,
      status: record.status as DiscrepancyStatus,
      bankTransaction: record.bank_transaction ? {
        id: record.bank_transaction.id,
        date: record.bank_transaction.date,
        amount: record.bank_transaction.amount,
        description: record.bank_transaction.description || '',
        reference: record.bank_transaction.reference,
        account: record.bank_transaction.account,
        category: record.bank_transaction.category,
        balance: record.bank_transaction.balance,
        transaction_type: record.bank_transaction.transaction_type,
        file_id: record.bank_transaction.file_id,
        company_id: record.bank_transaction.company_id
      } : undefined,
      ledgerTransaction: record.ledger_transaction ? {
        id: record.ledger_transaction.id,
        date: record.ledger_transaction.date,
        amount: record.ledger_transaction.amount,
        description: record.ledger_transaction.description || '',
        reference: record.ledger_transaction.reference,
        account: record.ledger_transaction.account,
        category: record.ledger_transaction.category,
        balance: record.ledger_transaction.balance,
        transaction_type: record.ledger_transaction.transaction_type,
        file_id: record.ledger_transaction.file_id,
        company_id: record.ledger_transaction.company_id
      } : undefined,
      amountDifference: record.amount_difference || 0,
      dateVariance: record.date_variance || 0,
      description: record.description || '',
      category: record.category,
      notes: record.notes,
      recommendation: record.recommendation || '',
      reviewedBy: record.reviewed_by,
      reviewedAt: record.reviewed_at,
      resolvedBy: record.resolved_by,
      resolvedAt: record.resolved_at,
      createdAt: record.created_at
    }))

    return {
      discrepancies,
      total: count || 0
    }
  }

  /**
   * Generate journal vouchers for discrepancies
   */
  async generateJournalVouchers(
    companyId: string,
    discrepancies: Discrepancy[]
  ): Promise<JournalVoucher[]> {
    const journalVouchers: JournalVoucher[] = []

    for (const discrepancy of discrepancies) {
      const voucher = this.createJournalVoucherForDiscrepancy(discrepancy)
      if (voucher) {
        journalVouchers.push(voucher)
      }
    }

    return journalVouchers
  }

  /**
   * Save journal vouchers to database
   */
  async saveJournalVouchers(
    companyId: string,
    journalVouchers: JournalVoucher[],
    userId: string,
    reportId?: string
  ): Promise<void> {
    const voucherRecords = journalVouchers.map(voucher => ({
      company_id: companyId,
      report_id: reportId || null,
      discrepancy_id: voucher.discrepancyId || null,
      voucher_number: voucher.voucherNumber || this.generateVoucherNumber(),
      date: voucher.date,
      description: voucher.description,
      entries: voucher.entries as any, // Cast to any to avoid type issues with JSONB
      total_amount: voucher.totalAmount,
      status: voucher.status,
      created_by: userId
    }))

    const { error } = await (this.supabase as any)
      .from('journal_vouchers')
      .insert(voucherRecords)

    if (error) {
      console.error('Error saving journal vouchers:', error)
      throw new Error(`Failed to save journal vouchers: ${error.message}`)
    }
  }

  /**
   * Categorize transaction based on description and amount
   */
  private categorizeTransaction(transaction: Transaction): string {
    const description = transaction.description.toLowerCase()
    const amount = Math.abs(transaction.amount)

    // Bank fees and charges
    if (description.includes('fee') || description.includes('charge') || description.includes('commission')) {
      return 'bank_fees'
    }

    // Interest
    if (description.includes('interest')) {
      return 'interest'
    }

    // Transfers
    if (description.includes('transfer') || description.includes('wire')) {
      return 'transfers'
    }

    // Payments
    if (description.includes('payment') || description.includes('pay')) {
      return 'payments'
    }

    // Deposits
    if (description.includes('deposit') || amount > 0) {
      return 'deposits'
    }

    // Small amounts (likely fees or adjustments)
    if (amount < 100) {
      return 'adjustments'
    }

    return 'other'
  }

  /**
   * Generate recommendation for discrepancy
   */
  private generateRecommendation(type: DiscrepancyType, transaction: Transaction): string {
    switch (type) {
      case 'bank_only':
        return `Create corresponding ledger entry for this bank transaction. Verify if this is a valid transaction that should be recorded in the books.`

      case 'ledger_only':
        return `Verify if corresponding bank transaction exists. This may be a timing difference or the transaction may not have cleared the bank yet.`

      default:
        return 'Review transaction details and determine appropriate action.'
    }
  }

  /**
   * Generate recommendation for amount mismatch
   */
  private generateAmountMismatchRecommendation(match: any): string {
    const difference = Math.abs(match.amountDifference)

    if (difference < 1) {
      return 'Minor amount difference, likely due to rounding. Consider creating adjustment entry.'
    } else if (difference < 100) {
      return 'Moderate amount difference. Review transaction details and create correcting entry if needed.'
    } else {
      return 'Significant amount difference. Investigate thoroughly and create appropriate journal entries.'
    }
  }

  /**
   * Generate recommendation for date discrepancy
   */
  private generateDateDiscrepancyRecommendation(match: any): string {
    const daysDifference = match.dateDifference

    if (daysDifference <= 1) {
      return 'Minor timing difference, likely due to processing delays. No action required unless material.'
    } else if (daysDifference <= 5) {
      return 'Moderate timing difference. Review if this affects period-end reporting.'
    } else {
      return 'Significant timing difference. Investigate and consider period adjustments if material.'
    }
  }

  /**
   * Calculate discrepancy statistics
   */
  private calculateDiscrepancyStatistics(discrepancies: Discrepancy[]) {
    const stats = {
      totalDiscrepancies: discrepancies.length,
      bankOnlyCount: discrepancies.filter(d => d.type === 'bank_only').length,
      ledgerOnlyCount: discrepancies.filter(d => d.type === 'ledger_only').length,
      amountMismatchCount: discrepancies.filter(d => d.type === 'amount_mismatch').length,
      dateMismatchCount: discrepancies.filter(d => d.type === 'date_discrepancy').length,
      referenceMismatchCount: discrepancies.filter(d => d.type === 'reference_mismatch').length,
      totalAmountDifference: discrepancies.reduce((sum, d) => sum + d.amountDifference, 0),
      averageAmountDifference: 0,
      criticalDiscrepancies: discrepancies.filter(d => d.amountDifference > 1000).length,
      minorDiscrepancies: discrepancies.filter(d => d.amountDifference <= 100).length
    }

    stats.averageAmountDifference = stats.totalDiscrepancies > 0
      ? stats.totalAmountDifference / stats.totalDiscrepancies
      : 0

    return stats
  }

  /**
   * Generate high-level recommendations
   */
  private generateDiscrepancyRecommendations(
    discrepancies: Discrepancy[],
    statistics: any
  ) {
    const recommendations = []

    // Critical amount discrepancies
    if (statistics.criticalDiscrepancies > 0) {
      recommendations.push({
        priority: 'high' as const,
        action: 'Review Critical Discrepancies',
        description: `${statistics.criticalDiscrepancies} discrepancies with amounts over $1,000 require immediate attention`,
        affectedTransactions: statistics.criticalDiscrepancies,
        estimatedImpact: discrepancies
          .filter(d => d.amountDifference > 1000)
          .reduce((sum, d) => sum + d.amountDifference, 0)
      })
    }

    // Bank-only transactions
    if (statistics.bankOnlyCount > 0) {
      recommendations.push({
        priority: 'medium' as const,
        action: 'Record Missing Ledger Entries',
        description: `${statistics.bankOnlyCount} bank transactions need corresponding ledger entries`,
        affectedTransactions: statistics.bankOnlyCount,
        estimatedImpact: discrepancies
          .filter(d => d.type === 'bank_only')
          .reduce((sum, d) => sum + d.amountDifference, 0)
      })
    }

    // Ledger-only transactions
    if (statistics.ledgerOnlyCount > 0) {
      recommendations.push({
        priority: 'medium' as const,
        action: 'Verify Outstanding Transactions',
        description: `${statistics.ledgerOnlyCount} ledger entries may not have cleared the bank`,
        affectedTransactions: statistics.ledgerOnlyCount,
        estimatedImpact: discrepancies
          .filter(d => d.type === 'ledger_only')
          .reduce((sum, d) => sum + d.amountDifference, 0)
      })
    }

    // Minor discrepancies
    if (statistics.minorDiscrepancies > 0) {
      recommendations.push({
        priority: 'low' as const,
        action: 'Process Minor Adjustments',
        description: `${statistics.minorDiscrepancies} minor discrepancies can be processed as adjusting entries`,
        affectedTransactions: statistics.minorDiscrepancies,
        estimatedImpact: discrepancies
          .filter(d => d.amountDifference <= 100)
          .reduce((sum, d) => sum + d.amountDifference, 0)
      })
    }

    return recommendations
  }

  /**
   * Create journal voucher for a discrepancy
   */
  private createJournalVoucherForDiscrepancy(discrepancy: Discrepancy): JournalVoucher | null {
    const entries: JournalVoucherEntry[] = []

    switch (discrepancy.type) {
      case 'bank_only':
        if (discrepancy.bankTransaction) {
          const tx = discrepancy.bankTransaction
          if (tx.amount > 0) {
            // Bank deposit not in ledger
            entries.push(
              { account: 'Cash in Bank', debit: tx.amount, description: tx.description },
              { account: 'Unidentified Deposits', credit: tx.amount, description: 'To record bank deposit' }
            )
          } else {
            // Bank withdrawal not in ledger
            entries.push(
              { account: 'Unidentified Expenses', debit: Math.abs(tx.amount), description: tx.description },
              { account: 'Cash in Bank', credit: Math.abs(tx.amount), description: 'To record bank withdrawal' }
            )
          }
        }
        break

      case 'ledger_only':
        if (discrepancy.ledgerTransaction) {
          const tx = discrepancy.ledgerTransaction
          // Reverse the ledger entry if bank transaction doesn't exist
          entries.push(
            { account: 'Outstanding Checks/Deposits', debit: Math.abs(tx.amount), description: 'Outstanding transaction' },
            { account: 'Cash in Bank', credit: Math.abs(tx.amount), description: 'To record outstanding transaction' }
          )
        }
        break

      case 'amount_mismatch':
        if (discrepancy.bankTransaction && discrepancy.ledgerTransaction) {
          const difference = discrepancy.amountDifference
          entries.push(
            { account: 'Cash in Bank', debit: difference, description: 'Amount difference adjustment' },
            { account: 'Reconciliation Adjustments', credit: difference, description: 'To correct amount difference' }
          )
        }
        break

      default:
        return null
    }

    if (entries.length === 0) return null

    const totalAmount = entries.reduce((sum, entry) => sum + (entry.debit || 0), 0)

    return {
      date: new Date().toISOString().split('T')[0],
      description: `Reconciliation adjustment: ${discrepancy.description}`,
      entries,
      totalAmount,
      discrepancyId: discrepancy.id,
      status: 'draft'
    }
  }

  /**
   * Generate voucher number
   */
  private generateVoucherNumber(): string {
    const date = new Date()
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')

    return `JV${year}${month}${day}${random}`
  }
}

export default DiscrepancyAnalyzer
