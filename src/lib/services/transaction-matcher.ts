import { createClient } from '@/lib/supabase'

// Types for transaction matching
export interface Transaction {
  id: string
  date: string
  amount: number
  description: string
  reference?: string
  account?: string
  category?: string
  balance?: number
  transaction_type: 'bank_statement' | 'ledger_entry'
  file_id: string
  company_id: string
}

export interface MatchingOptions {
  exactMatchFields: ('reference' | 'date' | 'amount')[]
  fuzzyMatchThreshold: number
  dateToleranceDays: number
  amountTolerancePercentage: number
  descriptionSimilarityThreshold: number
}

export interface MatchedTransaction {
  bankTransaction: Transaction
  ledgerTransaction: Transaction
  matchType: 'exact' | 'fuzzy' | 'manual' | 'heuristic'
  confidenceScore: number
  matchedFields: string[]
  amountDifference: number
  dateDifference: number
}

export interface PotentialMatch {
  bankTransaction: Transaction
  ledgerTransactions: {
    transaction: Transaction
    confidenceScore: number
    matchedFields: string[]
    reasons: string[]
  }[]
}

export interface MatchingResult {
  exactMatches: MatchedTransaction[]
  fuzzyMatches: MatchedTransaction[]
  heuristicMatches: MatchedTransaction[]
  unmatchedBankTransactions: Transaction[]
  unmatchedLedgerTransactions: Transaction[]
  potentialMatches: PotentialMatch[]
  statistics: {
    totalBankTransactions: number
    totalLedgerTransactions: number
    exactMatchCount: number
    fuzzyMatchCount: number
    heuristicMatchCount: number
    unmatchedBankCount: number
    unmatchedLedgerCount: number
    potentialMatchCount: number
    matchPercentage: number
  }
}

export class TransactionMatcher {
  private supabase = createClient()

  /**
   * Main matching function that orchestrates all matching strategies
   */
  async matchTransactions(
    bankTransactions: Transaction[],
    ledgerTransactions: Transaction[],
    options: MatchingOptions = this.getDefaultOptions()
  ): Promise<MatchingResult> {
    console.log(`Starting transaction matching: ${bankTransactions.length} bank, ${ledgerTransactions.length} ledger`)

    // Step 1: Find exact matches
    const exactMatches = this.findExactMatches(bankTransactions, ledgerTransactions, options)
    console.log(`Found ${exactMatches.length} exact matches`)

    // Remove matched transactions from further processing
    const remainingBankTransactions = bankTransactions.filter(
      bt => !exactMatches.some(em => em.bankTransaction.id === bt.id)
    )
    const remainingLedgerTransactions = ledgerTransactions.filter(
      lt => !exactMatches.some(em => em.ledgerTransaction.id === lt.id)
    )

    // Step 2: Find fuzzy matches
    const fuzzyMatches = this.findFuzzyMatches(
      remainingBankTransactions,
      remainingLedgerTransactions,
      options
    )
    console.log(`Found ${fuzzyMatches.length} fuzzy matches`)

    // Remove fuzzy matched transactions
    const afterFuzzyBankTransactions = remainingBankTransactions.filter(
      bt => !fuzzyMatches.some(fm => fm.bankTransaction.id === bt.id)
    )
    const afterFuzzyLedgerTransactions = remainingLedgerTransactions.filter(
      lt => !fuzzyMatches.some(fm => fm.ledgerTransaction.id === lt.id)
    )

    // Step 3: Find heuristic matches
    const heuristicMatches = this.findHeuristicMatches(
      afterFuzzyBankTransactions,
      afterFuzzyLedgerTransactions,
      options
    )
    console.log(`Found ${heuristicMatches.length} heuristic matches`)

    // Remove heuristic matched transactions
    const finalUnmatchedBankTransactions = afterFuzzyBankTransactions.filter(
      bt => !heuristicMatches.some(hm => hm.bankTransaction.id === bt.id)
    )
    const finalUnmatchedLedgerTransactions = afterFuzzyLedgerTransactions.filter(
      lt => !heuristicMatches.some(hm => hm.ledgerTransaction.id === lt.id)
    )

    // Step 4: Find potential matches for manual review
    const potentialMatches = this.findPotentialMatches(
      finalUnmatchedBankTransactions,
      finalUnmatchedLedgerTransactions,
      options
    )

    // Calculate statistics
    const totalMatches = exactMatches.length + fuzzyMatches.length + heuristicMatches.length
    const statistics = {
      totalBankTransactions: bankTransactions.length,
      totalLedgerTransactions: ledgerTransactions.length,
      exactMatchCount: exactMatches.length,
      fuzzyMatchCount: fuzzyMatches.length,
      heuristicMatchCount: heuristicMatches.length,
      unmatchedBankCount: finalUnmatchedBankTransactions.length,
      unmatchedLedgerCount: finalUnmatchedLedgerTransactions.length,
      potentialMatchCount: potentialMatches.length,
      matchPercentage: Math.round((totalMatches / Math.max(bankTransactions.length, ledgerTransactions.length)) * 100)
    }

    return {
      exactMatches,
      fuzzyMatches,
      heuristicMatches,
      unmatchedBankTransactions: finalUnmatchedBankTransactions,
      unmatchedLedgerTransactions: finalUnmatchedLedgerTransactions,
      potentialMatches,
      statistics
    }
  }

  /**
   * Find exact matches based on reference numbers, date+amount combinations
   */
  private findExactMatches(
    bankTransactions: Transaction[],
    ledgerTransactions: Transaction[],
    options: MatchingOptions
  ): MatchedTransaction[] {
    const matches: MatchedTransaction[] = []

    for (const bankTx of bankTransactions) {
      for (const ledgerTx of ledgerTransactions) {
        const matchedFields: string[] = []
        let isExactMatch = false

        // Check reference number match
        if (bankTx.reference && ledgerTx.reference &&
            this.normalizeReference(bankTx.reference) === this.normalizeReference(ledgerTx.reference)) {
          matchedFields.push('reference')
          isExactMatch = true
        }

        // Check date + amount combination
        if (options.exactMatchFields.includes('date') && options.exactMatchFields.includes('amount')) {
          if (bankTx.date === ledgerTx.date && Math.abs(bankTx.amount - ledgerTx.amount) < 0.01) {
            matchedFields.push('date', 'amount')
            isExactMatch = true
          }
        }

        if (isExactMatch) {
          matches.push({
            bankTransaction: bankTx,
            ledgerTransaction: ledgerTx,
            matchType: 'exact',
            confidenceScore: 100,
            matchedFields,
            amountDifference: Math.abs(bankTx.amount - ledgerTx.amount),
            dateDifference: this.calculateDateDifference(bankTx.date, ledgerTx.date)
          })
          break // Move to next bank transaction
        }
      }
    }

    return matches
  }

  /**
   * Find fuzzy matches based on description similarity and date/amount proximity
   */
  private findFuzzyMatches(
    bankTransactions: Transaction[],
    ledgerTransactions: Transaction[],
    options: MatchingOptions
  ): MatchedTransaction[] {
    const matches: MatchedTransaction[] = []

    for (const bankTx of bankTransactions) {
      let bestMatch: { ledgerTx: Transaction; score: number; fields: string[] } | null = null

      for (const ledgerTx of ledgerTransactions) {
        const score = this.calculateFuzzyScore(bankTx, ledgerTx, options)

        if (score >= options.fuzzyMatchThreshold && (!bestMatch || score > bestMatch.score)) {
          const matchedFields = this.getMatchedFields(bankTx, ledgerTx, options)
          bestMatch = { ledgerTx, score, fields: matchedFields }
        }
      }

      if (bestMatch) {
        matches.push({
          bankTransaction: bankTx,
          ledgerTransaction: bestMatch.ledgerTx,
          matchType: 'fuzzy',
          confidenceScore: Math.round(bestMatch.score),
          matchedFields: bestMatch.fields,
          amountDifference: Math.abs(bankTx.amount - bestMatch.ledgerTx.amount),
          dateDifference: this.calculateDateDifference(bankTx.date, bestMatch.ledgerTx.date)
        })
      }
    }

    return matches
  }

  /**
   * Find heuristic matches based on business logic and patterns
   */
  private findHeuristicMatches(
    bankTransactions: Transaction[],
    ledgerTransactions: Transaction[],
    options: MatchingOptions
  ): MatchedTransaction[] {
    const matches: MatchedTransaction[] = []

    for (const bankTx of bankTransactions) {
      for (const ledgerTx of ledgerTransactions) {
        const heuristicScore = this.calculateHeuristicScore(bankTx, ledgerTx, options)

        if (heuristicScore >= 70) { // 70% threshold for heuristic matches
          const matchedFields = this.getHeuristicMatchedFields(bankTx, ledgerTx, options)

          matches.push({
            bankTransaction: bankTx,
            ledgerTransaction: ledgerTx,
            matchType: 'heuristic',
            confidenceScore: Math.round(heuristicScore),
            matchedFields,
            amountDifference: Math.abs(bankTx.amount - ledgerTx.amount),
            dateDifference: this.calculateDateDifference(bankTx.date, ledgerTx.date)
          })
          break // Move to next bank transaction
        }
      }
    }

    return matches
  }

  /**
   * Find potential matches for manual review
   */
  private findPotentialMatches(
    bankTransactions: Transaction[],
    ledgerTransactions: Transaction[],
    options: MatchingOptions
  ): PotentialMatch[] {
    const potentialMatches: PotentialMatch[] = []

    for (const bankTx of bankTransactions) {
      const candidates: PotentialMatch['ledgerTransactions'] = []

      for (const ledgerTx of ledgerTransactions) {
        const score = this.calculatePotentialMatchScore(bankTx, ledgerTx, options)

        if (score >= 30) { // Lower threshold for potential matches
          const matchedFields = this.getMatchedFields(bankTx, ledgerTx, options)
          const reasons = this.getPotentialMatchReasons(bankTx, ledgerTx, options)

          candidates.push({
            transaction: ledgerTx,
            confidenceScore: Math.round(score),
            matchedFields,
            reasons
          })
        }
      }

      if (candidates.length > 0) {
        // Sort by confidence score
        candidates.sort((a, b) => b.confidenceScore - a.confidenceScore)

        potentialMatches.push({
          bankTransaction: bankTx,
          ledgerTransactions: candidates.slice(0, 5) // Top 5 candidates
        })
      }
    }

    return potentialMatches
  }

  /**
   * Calculate fuzzy score based on description similarity and other factors
   */
  private calculateFuzzyScore(bankTx: Transaction, ledgerTx: Transaction, options: MatchingOptions): number {
    let score = 0
    let factors = 0

    // Description similarity (using simple string matching for now)
    const descriptionSimilarity = this.calculateStringSimilarity(bankTx.description, ledgerTx.description)
    if (descriptionSimilarity >= options.descriptionSimilarityThreshold) {
      score += descriptionSimilarity * 0.4 // 40% weight
      factors++
    }

    // Date proximity
    const dateDiff = this.calculateDateDifference(bankTx.date, ledgerTx.date)
    if (dateDiff <= options.dateToleranceDays) {
      const dateScore = Math.max(0, 100 - (dateDiff * 10)) // Decrease by 10% per day
      score += dateScore * 0.3 // 30% weight
      factors++
    }

    // Amount similarity
    const amountDiff = Math.abs(bankTx.amount - ledgerTx.amount)
    const amountPercentDiff = Math.abs(bankTx.amount) > 0
      ? (amountDiff / Math.abs(bankTx.amount)) * 100
      : (amountDiff > 0 ? 100 : 0)
      ? (amountDiff / Math.abs(bankTx.amount)) * 100
      : (amountDiff > 0 ? 100 : 0)
    if (amountPercentDiff <= options.amountTolerancePercentage) {
      const amountScore = Math.max(0, 100 - amountPercentDiff)
      score += amountScore * 0.3 // 30% weight
      factors++
    }

    return factors > 0 ? score / factors : 0
  }

  /**
   * Calculate heuristic score based on business patterns
   */
  private calculateHeuristicScore(bankTx: Transaction, ledgerTx: Transaction, options: MatchingOptions): number {
    let score = 0
    let factors = 0

    // Same amount (exact)
    if (Math.abs(bankTx.amount - ledgerTx.amount) < 0.01) {
      score += 100
      factors++
    }

    // Similar date (within tolerance)
    const dateDiff = this.calculateDateDifference(bankTx.date, ledgerTx.date)
    if (dateDiff <= options.dateToleranceDays) {
      score += Math.max(0, 100 - (dateDiff * 20))
      factors++
    }

    // Reference number patterns (partial matches)
    if (bankTx.reference && ledgerTx.reference) {
      const refSimilarity = this.calculateStringSimilarity(bankTx.reference, ledgerTx.reference)
      if (refSimilarity >= 50) {
        score += refSimilarity
        factors++
      }
    }

    return factors > 0 ? score / factors : 0
  }

  /**
   * Calculate potential match score (more lenient)
   */
  private calculatePotentialMatchScore(bankTx: Transaction, ledgerTx: Transaction, options: MatchingOptions): number {
    let score = 0

    // Amount similarity (more lenient)
    const amountDiff = Math.abs(bankTx.amount - ledgerTx.amount)
    const amountPercentDiff = (amountDiff / Math.abs(bankTx.amount)) * 100
    if (amountPercentDiff <= options.amountTolerancePercentage * 2) { // Double tolerance
      score += Math.max(0, 50 - amountPercentDiff)
    }

    // Date proximity (more lenient)
    const dateDiff = this.calculateDateDifference(bankTx.date, ledgerTx.date)
    if (dateDiff <= options.dateToleranceDays * 2) { // Double tolerance
      score += Math.max(0, 50 - (dateDiff * 5))
    }

    // Description similarity (more lenient)
    const descriptionSimilarity = this.calculateStringSimilarity(bankTx.description, ledgerTx.description)
    score += descriptionSimilarity * 0.5

    return score
  }

  /**
   * Simple string similarity calculation (Levenshtein-like)
   */
  private calculateStringSimilarity(str1: string, str2: string): number {
    if (!str1 || !str2) return 0

    const s1 = str1.toLowerCase().trim()
    const s2 = str2.toLowerCase().trim()

    if (s1 === s2) return 100

    // Simple substring matching
    if (s1.includes(s2) || s2.includes(s1)) {
      const shorter = s1.length < s2.length ? s1 : s2
      const longer = s1.length >= s2.length ? s1 : s2
      return (shorter.length / longer.length) * 80
    }

    // Word overlap
    const words1 = s1.split(/\s+/)
    const words2 = s2.split(/\s+/)
    const commonWords = words1.filter(word => words2.includes(word))

    if (commonWords.length > 0) {
      const overlap = commonWords.length / Math.max(words1.length, words2.length)
      return overlap * 60
    }

    return 0
  }

  /**
   * Calculate date difference in days
   */
  private calculateDateDifference(date1: string, date2: string): number {
    const d1 = new Date(date1)
    const d2 = new Date(date2)
    return Math.abs(Math.floor((d1.getTime() - d2.getTime()) / (1000 * 60 * 60 * 24)))
  }

  /**
   * Normalize reference numbers for comparison
   */
  private normalizeReference(ref: string): string {
    return ref.toLowerCase().replace(/[^a-z0-9]/g, '')
  }

  /**
   * Get matched fields for display
   */
  private getMatchedFields(bankTx: Transaction, ledgerTx: Transaction, options: MatchingOptions): string[] {
    const fields: string[] = []

    if (Math.abs(bankTx.amount - ledgerTx.amount) < 0.01) {
      fields.push('amount')
    }

    if (this.calculateDateDifference(bankTx.date, ledgerTx.date) <= options.dateToleranceDays) {
      fields.push('date')
    }

    if (bankTx.reference && ledgerTx.reference &&
        this.calculateStringSimilarity(bankTx.reference, ledgerTx.reference) >= 70) {
      fields.push('reference')
    }

    if (this.calculateStringSimilarity(bankTx.description, ledgerTx.description) >= options.descriptionSimilarityThreshold) {
      fields.push('description')
    }

    return fields
  }

  /**
   * Get heuristic matched fields
   */
  private getHeuristicMatchedFields(bankTx: Transaction, ledgerTx: Transaction, options: MatchingOptions): string[] {
    const fields: string[] = []

    if (Math.abs(bankTx.amount - ledgerTx.amount) < 0.01) {
      fields.push('amount')
    }

    const dateDiff = this.calculateDateDifference(bankTx.date, ledgerTx.date)
    if (dateDiff <= options.dateToleranceDays) {
      fields.push('date')
    }

    if (bankTx.reference && ledgerTx.reference) {
      const refSimilarity = this.calculateStringSimilarity(bankTx.reference, ledgerTx.reference)
      if (refSimilarity >= 50) {
        fields.push('reference')
      }
    }

    return fields
  }

  /**
   * Get reasons for potential matches
   */
  private getPotentialMatchReasons(bankTx: Transaction, ledgerTx: Transaction, options: MatchingOptions): string[] {
    const reasons: string[] = []

    const amountDiff = Math.abs(bankTx.amount - ledgerTx.amount)
    if (amountDiff < 0.01) {
      reasons.push('Exact amount match')
    } else if (amountDiff < Math.abs(bankTx.amount) * 0.1) {
      reasons.push('Similar amount')
    }

    const dateDiff = this.calculateDateDifference(bankTx.date, ledgerTx.date)
    if (dateDiff === 0) {
      reasons.push('Same date')
    } else if (dateDiff <= 3) {
      reasons.push(`${dateDiff} day(s) apart`)
    }

    const descSimilarity = this.calculateStringSimilarity(bankTx.description, ledgerTx.description)
    if (descSimilarity >= 70) {
      reasons.push('Similar description')
    }

    if (bankTx.reference && ledgerTx.reference) {
      const refSimilarity = this.calculateStringSimilarity(bankTx.reference, ledgerTx.reference)
      if (refSimilarity >= 50) {
        reasons.push('Similar reference')
      }
    }

    return reasons
  }

  /**
   * Get default matching options
   */
  private getDefaultOptions(): MatchingOptions {
    return {
      exactMatchFields: ['reference', 'date', 'amount'],
      fuzzyMatchThreshold: 70,
      dateToleranceDays: 3,
      amountTolerancePercentage: 5,
      descriptionSimilarityThreshold: 60
    }
  }

  /**
   * Save matching results to database
   */
  async saveMatchingResults(
    companyId: string,
    matchingResult: MatchingResult,
    userId: string
  ): Promise<void> {
    const { error } = await (this.supabase as any).rpc('save_matching_results', {
      p_company_id: companyId,
      p_exact_matches: matchingResult.exactMatches,
      p_fuzzy_matches: matchingResult.fuzzyMatches,
      p_heuristic_matches: matchingResult.heuristicMatches,
      p_created_by: userId
    })

    if (error) {
      console.error('Error saving matching results:', error)
      throw new Error(`Failed to save matching results: ${error.message}`)
    }
  }

  /**
   * Get transactions for matching from database
   */
  async getTransactionsForMatching(
    companyId: string,
    bankFileId?: string,
    ledgerFileId?: string
  ): Promise<{ bankTransactions: Transaction[]; ledgerTransactions: Transaction[] }> {
    let bankQuery = this.supabase
      .from('transactions')
      .select('*')
      .eq('company_id', companyId)
      .eq('transaction_type', 'bank_statement')

    let ledgerQuery = this.supabase
      .from('transactions')
      .select('*')
      .eq('company_id', companyId)
      .eq('transaction_type', 'ledger_entry')

    if (bankFileId) {
      bankQuery = bankQuery.eq('file_id', bankFileId)
    }

    if (ledgerFileId) {
      ledgerQuery = ledgerQuery.eq('file_id', ledgerFileId)
    }

    const [bankResult, ledgerResult] = await Promise.all([
      bankQuery,
      ledgerQuery
    ])

    if (bankResult.error) {
      throw new Error(`Failed to fetch bank transactions: ${bankResult.error.message}`)
    }

    if (ledgerResult.error) {
      throw new Error(`Failed to fetch ledger transactions: ${ledgerResult.error.message}`)
    }

    return {
      bankTransactions: bankResult.data || [],
      ledgerTransactions: ledgerResult.data || []
    }
  }
}

export default TransactionMatcher
