import { createClient } from '@/lib/supabase';

export interface DiscrepancyDetectionResult {
  bankOnlyDiscrepancies: number;
  ledgerOnlyDiscrepancies: number;
  amountMismatchDiscrepancies: number;
  totalDiscrepancies: number;
  message: string;
}

interface Transaction {
  id: string;
  company_id: string;
  file_id: string;
  transaction_type: 'bank_statement' | 'ledger_entry';
  date: string;
  amount: number;
  description: string;
  reference?: string;
  account?: string;
  category?: string;
  balance?: number;
  raw_data?: any;
  created_at?: string;
  updated_at?: string;
  check_number?: string;
  voucher_number?: string;
  fin_reference?: string;
  transaction_code?: string;
  branch_code?: string;
  serial_number?: string;
  processed_at?: string;
}

interface DiscrepancyInsert {
  company_id: string;
  type: 'bank_only' | 'ledger_only' | 'amount_mismatch';
  amount_difference: number;
  description: string;
  bank_transaction_id?: string;
  ledger_transaction_id?: string;
  status: 'pending' | 'reviewed' | 'resolved';
  category?: string;
  notes?: string;
  recommendation?: string;
}

interface Discrepancy {
  id: string;
  company_id: string;
  type: 'bank_only' | 'ledger_only' | 'amount_mismatch';
  amount_difference: number;
  description: string;
  bank_transaction_id?: string;
  ledger_transaction_id?: string;
  status: 'pending' | 'reviewed' | 'resolved';
  category?: string;
  notes?: string;
  recommendation?: string;
  reviewed_by?: string;
  reviewed_at?: string;
  resolved_by?: string;
  resolved_at?: string;
  created_at?: string;
  updated_at?: string;
}

export class DiscrepancyDetector {
  private supabase = createClient();

  /**
   * Detect and populate discrepancies after transaction matching
   */
  async detectAndPopulateDiscrepancies(companyId: string): Promise<DiscrepancyDetectionResult> {
    console.log('Starting discrepancy detection for company:', companyId);

    // Clear existing discrepancies for this company to avoid duplicates
    await (this.supabase as any)
      .from('discrepancies')
      .delete()
      .eq('company_id', companyId);

    let bankOnlyCount = 0;
    let ledgerOnlyCount = 0;
    let amountMismatchCount = 0;

    // 1. Find unmatched bank transactions (bank-only discrepancies)
    const { data: unmatchedBankTxs } = await this.supabase
      .from('transactions')
      .select('*')
      .eq('company_id', companyId)
      .eq('transaction_type', 'bank_statement')
      .not('id', 'in', `(
        SELECT bank_transaction_id
        FROM reconciliations
        WHERE company_id = '${companyId}'
          AND bank_transaction_id IS NOT NULL
      )`);

    if (unmatchedBankTxs && unmatchedBankTxs.length > 0) {
      console.log(`Found ${unmatchedBankTxs.length} unmatched bank transactions`);

      for (const tx of unmatchedBankTxs as Transaction[]) {
        const discrepancy: DiscrepancyInsert = {
          company_id: companyId,
          type: 'bank_only',
          amount_difference: Math.abs(tx.amount),
          description: `Bank transaction not found in ledger: ${tx.description || 'No description'}`,
          bank_transaction_id: tx.id,
          status: 'pending',
          category: 'bank_only_transaction',
          notes: `Bank Date: ${tx.date}, Amount: ${tx.amount}, Reference: ${tx.reference || 'N/A'}`,
          recommendation: Math.abs(tx.amount) > 10000 ? 'Manual review required for large amount' : 'Investigate missing ledger entry'
        };

        const { error: insertError } = await this.supabase
          .from('discrepancies')
          .insert(discrepancy as any);

        if (insertError) {
          console.error('Error inserting bank discrepancy:', insertError);
        }
      }
      bankOnlyCount = unmatchedBankTxs.length;
    }

    // 2. Find unmatched ledger transactions (ledger-only discrepancies)
    const { data: unmatchedLedgerTxs } = await this.supabase
      .from('transactions')
      .select('*')
      .eq('company_id', companyId)
      .eq('transaction_type', 'ledger_entry')
      .not('id', 'in', `(
        SELECT ledger_transaction_id
        FROM reconciliations
        WHERE company_id = '${companyId}'
          AND ledger_transaction_id IS NOT NULL
      )`);

    if (unmatchedLedgerTxs && unmatchedLedgerTxs.length > 0) {
      console.log(`Found ${unmatchedLedgerTxs.length} unmatched ledger transactions`);

      for (const tx of unmatchedLedgerTxs as Transaction[]) {
        const discrepancy: DiscrepancyInsert = {
          company_id: companyId,
          type: 'ledger_only',
          amount_difference: Math.abs(tx.amount),
          description: `Ledger entry not found in bank statement: ${tx.description || 'No description'}`,
          ledger_transaction_id: tx.id,
          status: 'pending',
          category: 'ledger_only_transaction',
          notes: `Ledger Date: ${tx.date}, Amount: ${tx.amount}, Reference: ${tx.reference || 'N/A'}`,
          recommendation: Math.abs(tx.amount) > 10000 ? 'Manual review required for large amount' : 'Investigate missing bank entry'
        };

        const { error: insertError } = await this.supabase
          .from('discrepancies')
          .insert(discrepancy as any);

        if (insertError) {
          console.error('Error inserting ledger discrepancy:', insertError);
        }
      }
      ledgerOnlyCount = unmatchedLedgerTxs.length;
    }

    // 3. Find amount mismatches in existing reconciliations
    const { data: reconciliations } = await this.supabase
      .from('reconciliations')
      .select(`
        *,
        bank_transaction:bank_transaction_id(id, amount, date, description, reference),
        ledger_transaction:ledger_transaction_id(id, amount, date, description, reference)
      `)
      .eq('company_id', companyId)
      .not('bank_transaction_id', 'is', null)
      .not('ledger_transaction_id', 'is', null);

    if (reconciliations && reconciliations.length > 0) {
      for (const rec of reconciliations as any[]) {
        const bankTx = rec.bank_transaction;
        const ledgerTx = rec.ledger_transaction;

        if (bankTx && ledgerTx) {
          const amountDiff = Math.abs(bankTx.amount - ledgerTx.amount);

          // Consider amount differences > 1 as mismatches
          if (amountDiff > 1) {
            const discrepancy: DiscrepancyInsert = {
              company_id: companyId,
              type: 'amount_mismatch',
              amount_difference: amountDiff,
              description: `Amount mismatch: Bank ${bankTx.amount}, Ledger ${ledgerTx.amount}`,
              bank_transaction_id: bankTx.id,
              ledger_transaction_id: ledgerTx.id,
              status: 'pending',
              category: 'amount_discrepancy',
              notes: `Bank Amount: ${bankTx.amount}, Ledger Amount: ${ledgerTx.amount}, Difference: ${amountDiff}`,
              recommendation: amountDiff > 1000 ? 'Manual review required for large discrepancy' : 'Auto-correction possible'
            };

            const { error: insertError } = await this.supabase
              .from('discrepancies')
              .insert(discrepancy as any);

            if (insertError) {
              console.error('Error inserting amount mismatch discrepancy:', insertError);
            } else {
              amountMismatchCount++;
            }
          }
        }
      }
    }

    const totalDiscrepancies = bankOnlyCount + ledgerOnlyCount + amountMismatchCount;

    console.log('Discrepancy detection completed:', {
      bankOnly: bankOnlyCount,
      ledgerOnly: ledgerOnlyCount,
      amountMismatch: amountMismatchCount,
      total: totalDiscrepancies
    });

    return {
      bankOnlyDiscrepancies: bankOnlyCount,
      ledgerOnlyDiscrepancies: ledgerOnlyCount,
      amountMismatchDiscrepancies: amountMismatchCount,
      totalDiscrepancies,
      message: `Found ${totalDiscrepancies} discrepancies: ${bankOnlyCount} bank-only, ${ledgerOnlyCount} ledger-only, ${amountMismatchCount} amount mismatches`
    };
  }

  /**
   * Get discrepancy statistics for a company
   */
  async getDiscrepancyStatistics(companyId: string) {
    const { data: discrepancies } = await (this.supabase as any)
      .from('discrepancies')
      .select('type, status, amount_difference')
      .eq('company_id', companyId);

    if (!discrepancies || discrepancies.length === 0) {
      return {
        total: 0,
        pending: 0,
        resolved: 0,
        byType: { bank_only: 0, ledger_only: 0, amount_mismatch: 0 },
        totalAmount: 0
      };
    }

    const typedDiscrepancies = discrepancies as Discrepancy[];

    return {
      total: typedDiscrepancies.length,
      pending: typedDiscrepancies.filter(d => d.status === 'pending').length,
      resolved: typedDiscrepancies.filter(d => d.status === 'resolved').length,
      byType: {
        bank_only: typedDiscrepancies.filter(d => d.type === 'bank_only').length,
        ledger_only: typedDiscrepancies.filter(d => d.type === 'ledger_only').length,
        amount_mismatch: typedDiscrepancies.filter(d => d.type === 'amount_mismatch').length
      },
      totalAmount: typedDiscrepancies.reduce((sum, d) => sum + Math.abs(d.amount_difference || 0), 0)
    };
  }
}