import { createClient } from '@/lib/supabase'

export interface DocumentProcessingRequest {
  fileId: string
  fileName: string
  fileType: 'pdf' | 'xlsx' | 'xls' | 'csv'
  documentType: 'bank_statement' | 'ledger'
  companyId: string
}

export interface Transaction {
  date: string
  amount: number
  description: string
  reference?: string
  account?: string
  category?: string
  balance?: number
}

export interface ProcessingResponse {
  success: boolean
  fileId: string
  transactions?: Transaction[]
  error?: string
  processingTime: number
  aiModel: 'mistral' | 'gemini'
}

export class SimpleDocumentProcessor {
  private supabase: ReturnType<typeof createClient>

  constructor() {
    this.supabase = createClient()
  }

  /**
   * Process a document - simplified version for immediate implementation
   */
  async processDocument(request: DocumentProcessingRequest): Promise<ProcessingResponse> {
    const startTime = Date.now()

    try {
      // Update file status to processing
      await this.updateFileStatus(request.fileId, 'processing')

      // For now, create mock transaction data based on file type
      // This will be replaced with actual AI processing later
      const mockTransactions = this.generateMockTransactions(request.documentType)

      // Store transactions in database
      await this.storeTransactions(
        request.fileId,
        request.companyId,
        mockTransactions,
        request.documentType
      )

      // Update file status to completed
      await this.updateFileStatus(request.fileId, 'completed', {
        metadata: {
          transaction_count: mockTransactions.length,
          processing_time: Date.now() - startTime,
          ai_model: request.fileType === 'pdf' ? 'mistral' : 'gemini'
        }
      })

      return {
        success: true,
        fileId: request.fileId,
        transactions: mockTransactions,
        processingTime: Date.now() - startTime,
        aiModel: request.fileType === 'pdf' ? 'mistral' : 'gemini'
      }

    } catch (error) {
      console.error('Document processing error:', error)

      // Update file status to failed
      await this.updateFileStatus(request.fileId, 'failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        processing_time: Date.now() - startTime
      })

      return {
        success: false,
        fileId: request.fileId,
        error: error instanceof Error ? error.message : 'Unknown error',
        processingTime: Date.now() - startTime,
        aiModel: request.fileType === 'pdf' ? 'mistral' : 'gemini'
      }
    }
  }

  /**
   * Generate mock transaction data for testing
   */
  private generateMockTransactions(documentType: 'bank_statement' | 'ledger'): Transaction[] {
    const baseTransactions: Transaction[] = [
      {
        date: '2025-01-15',
        amount: -245.50,
        description: 'Office Supplies Purchase',
        reference: 'REF001',
        balance: 5432.10
      },
      {
        date: '2025-01-14',
        amount: 5000.00,
        description: 'Client Payment - ABC Corp',
        reference: 'INV-2025-001',
        balance: 5677.60
      },
      {
        date: '2025-01-13',
        amount: -25.00,
        description: 'Bank Service Fee',
        reference: 'FEE003',
        balance: 677.60
      },
      {
        date: '2025-01-12',
        amount: -150.00,
        description: 'Utility Payment',
        reference: 'UTIL-001',
        balance: 702.60
      },
      {
        date: '2025-01-11',
        amount: 2500.00,
        description: 'Deposit - Cash Sales',
        reference: 'DEP-001',
        balance: 852.60
      }
    ]

    if (documentType === 'ledger') {
      return baseTransactions.map(t => ({
        ...t,
        account: t.amount > 0 ? 'Revenue Account' : 'Expense Account',
        category: t.amount > 0 ? 'Income' : 'Operating Expense'
      }))
    }

    return baseTransactions
  }

  /**
   * Update file processing status in database
   */
  private async updateFileStatus(
    fileId: string,
    status: 'processing' | 'completed' | 'failed',
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      const updateData: any = {
        status,
        updated_at: new Date().toISOString()
      }

      if (status === 'processing') {
        updateData.processing_started_at = new Date().toISOString()
      } else if (status === 'completed') {
        updateData.processing_completed_at = new Date().toISOString()
      } else if (status === 'failed') {
        updateData.processing_error = metadata?.error
      }

      if (metadata) {
        if (metadata.metadata) {
          updateData.metadata = metadata.metadata
        } else {
          updateData.metadata = metadata
        }
      }

      const { error } = await (this.supabase as any)
        .from('files')
        .update(updateData)
        .eq('id', fileId)

      if (error) {
        console.error('Failed to update file status:', error)
      }
    } catch (error) {
      console.error('Error updating file status:', error)
    }
  }

  /**
   * Store extracted transactions in database
   */
  private async storeTransactions(
    fileId: string,
    companyId: string,
    transactions: Transaction[],
    transactionType: 'bank_statement' | 'ledger'
  ): Promise<void> {
    try {
      const transactionRecords = transactions.map(transaction => ({
        id: crypto.randomUUID(),
        company_id: companyId,
        file_id: fileId,
        transaction_type: (transactionType === 'ledger' ? 'ledger_entry' : 'bank_statement') as 'ledger_entry' | 'bank_statement',
        date: transaction.date,
        amount: transaction.amount,
        description: transaction.description,
        reference: transaction.reference,
        account: transaction.account,
        category: transaction.category,
        balance: transaction.balance,
        raw_data: JSON.parse(JSON.stringify({
          original_transaction: transaction,
          processing_metadata: {
            processor: 'simple-mock',
            timestamp: new Date().toISOString()
          }
        })),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }))

      const { error } = await (this.supabase as any)
        .from('transactions')
        .insert(transactionRecords)

      if (error) {
        console.error('Failed to store transactions:', error)
        throw new Error(`Failed to store transactions: ${error.message}`)
      }

      console.log(`Successfully stored ${transactions.length} transactions for file ${fileId}`)
    } catch (error) {
      console.error('Error storing transactions:', error)
      throw error
    }
  }

  /**
   * Get processing status for a file
   */
  async getProcessingStatus(fileId: string): Promise<{
    status: string
    progress?: number
    error?: string
    transactionCount?: number
  }> {
    try {
      const { data, error } = await (this.supabase as any)
        .from('files')
        .select('status, processing_error, metadata')
        .eq('id', fileId)
        .single()

      if (error) {
        throw new Error(`Failed to get file status: ${error.message}`)
      }

      const metadata = data.metadata as any;
      return {
        status: data.status,
        error: data.processing_error || undefined,
        transactionCount: metadata?.transaction_count
      }
    } catch (error) {
      console.error('Error getting processing status:', error)
      return {
        status: 'unknown',
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Get extracted transactions for a file
   */
  async getExtractedTransactions(fileId: string): Promise<Transaction[]> {
    try {
      const { data, error } = await (this.supabase as any)
        .from('transactions')
        .select('*')
        .eq('file_id', fileId)
        .order('date', { ascending: true })

      if (error) {
        throw new Error(`Failed to get transactions: ${error.message}`)
      }

      return data.map((record: any) => ({
        date: record.date,
        amount: record.amount,
        description: record.description || '',
        reference: record.reference || undefined,
        account: record.account || undefined,
        category: record.category || undefined,
        balance: record.balance || undefined
      }))
    } catch (error) {
      console.error('Error getting transactions:', error)
      return []
    }
  }
}

export default SimpleDocumentProcessor
