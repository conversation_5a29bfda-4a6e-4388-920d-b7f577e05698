import { createClient } from '@/lib/supabase';

// Enhanced types with RFSA-specific fields
export interface EnhancedTransaction {
  id: string;
  date: string;
  amount: number;
  description: string;
  reference?: string;
  account?: string;
  category?: string;
  balance?: number;
  transaction_type: 'bank_statement' | 'ledger_entry';
  file_id: string;
  company_id: string;

  // RFSA-specific fields
  check_number?: string;
  voucher_number?: string;
  fin_reference?: string;
  transaction_code?: string;
  branch_code?: string;
  serial_number?: string;
}

export interface EnhancedMatchResult {
  bankTransaction: EnhancedTransaction;
  ledgerTransaction: EnhancedTransaction;
  confidence: number;
  matchType: 'exact' | 'fin_reference' | 'check_number' | 'amount_date' | 'fuzzy' | 'manual';
  reasons: string[];
  matchedFields: string[];
  amountDifference: number;
  dateDifference: number;
}

export interface EnhancedMatchingResult {
  exactMatches: EnhancedMatchResult[];
  finReferenceMatches: EnhancedMatchResult[];
  checkNumberMatches: EnhancedMatchResult[];
  fuzzyMatches: EnhancedMatchResult[];
  unmatchedBankTransactions: EnhancedTransaction[];
  unmatchedLedgerTransactions: EnhancedTransaction[];
  statistics: {
    totalBankTransactions: number;
    totalLedgerTransactions: number;
    exactMatchCount: number;
    finReferenceMatchCount: number;
    checkNumberMatchCount: number;
    fuzzyMatchCount: number;
    unmatchedBankCount: number;
    unmatchedLedgerCount: number;
    matchPercentage: number;
    averageConfidence: number;
  };
  validation: {
    highValueUnmatchedBank: number;
    highValueUnmatchedLedger: number;
    potentialMissedMatches: number;
    matchingCompleteness: number;
  };
}

export class EnhancedTransactionMatcher {
  private supabase = createClient();

  /**
   * Main enhanced matching function with RFSA-specific patterns
   */
  async matchTransactions(
    companyId: string,
    bankFileId?: string,
    ledgerFileId?: string
  ): Promise<EnhancedMatchingResult> {
    console.log('Starting enhanced RFSA transaction matching...');

    // Get transactions from database
    const { bankTransactions, ledgerTransactions } = await this.getTransactionsForMatching(
      companyId,
      bankFileId,
      ledgerFileId
    );

    console.log(`Processing: ${bankTransactions.length} bank, ${ledgerTransactions.length} ledger transactions`);

    // Multi-pass matching with decreasing confidence thresholds
    const matches: EnhancedMatchResult[] = [];
    const usedLedgerIds = new Set<string>();
    const usedBankIds = new Set<string>();

    const confidenceThresholds = [0.95, 0.9, 0.8, 0.7, 0.6, 0.5, 0.4, 0.3];

    for (const threshold of confidenceThresholds) {
      console.log(`Pass ${confidenceThresholds.indexOf(threshold) + 1}: threshold ${threshold}`);

      for (const bankTxn of bankTransactions) {
        if (usedBankIds.has(bankTxn.id)) continue;

        let bestMatch: EnhancedMatchResult | null = null;

        for (const ledgerTxn of ledgerTransactions) {
          if (usedLedgerIds.has(ledgerTxn.id)) continue;

          const match = this.calculateEnhancedMatchConfidence(bankTxn, ledgerTxn);

          if (match.confidence >= threshold &&
              (!bestMatch || match.confidence > bestMatch.confidence)) {
            bestMatch = match;
          }
        }

        if (bestMatch && bestMatch.confidence >= threshold) {
          matches.push(bestMatch);
          usedLedgerIds.add(bestMatch.ledgerTransaction.id);
          usedBankIds.add(bankTxn.id);

          console.log(`Match found: ${bestMatch.matchType} (${bestMatch.confidence.toFixed(2)}) - Bank: ${bankTxn.amount} | Ledger: ${bestMatch.ledgerTransaction.amount}`);
        }
      }
    }

    // Categorize matches by type
    const exactMatches = matches.filter(m => m.matchType === 'exact');
    const finReferenceMatches = matches.filter(m => m.matchType === 'fin_reference');
    const checkNumberMatches = matches.filter(m => m.matchType === 'check_number');
    const fuzzyMatches = matches.filter(m => m.matchType === 'fuzzy');

    // Get unmatched transactions
    const unmatchedBankTransactions = bankTransactions.filter(
      bt => !usedBankIds.has(bt.id)
    );
    const unmatchedLedgerTransactions = ledgerTransactions.filter(
      lt => !usedLedgerIds.has(lt.id)
    );

    // Calculate statistics
    const totalMatches = matches.length;
    const averageConfidence = totalMatches > 0
      ? matches.reduce((sum, m) => sum + m.confidence, 0) / totalMatches
      : 0;

    const statistics = {
      totalBankTransactions: bankTransactions.length,
      totalLedgerTransactions: ledgerTransactions.length,
      exactMatchCount: exactMatches.length,
      finReferenceMatchCount: finReferenceMatches.length,
      checkNumberMatchCount: checkNumberMatches.length,
      fuzzyMatchCount: fuzzyMatches.length,
      unmatchedBankCount: unmatchedBankTransactions.length,
      unmatchedLedgerCount: unmatchedLedgerTransactions.length,
      matchPercentage: Math.round((totalMatches / Math.max(bankTransactions.length, ledgerTransactions.length)) * 100),
      averageConfidence: Math.round(averageConfidence * 100) / 100
    };

    // Validation analysis
    const validation = this.validateMatchingCompleteness(
      bankTransactions,
      ledgerTransactions,
      matches
    );

    console.log(`Enhanced matching complete: ${totalMatches} matches found`);
    console.log('Statistics:', statistics);
    console.log('Validation:', validation);

    return {
      exactMatches,
      finReferenceMatches,
      checkNumberMatches,
      fuzzyMatches,
      unmatchedBankTransactions,
      unmatchedLedgerTransactions,
      statistics,
      validation
    };
  }

  /**
   * Calculate enhanced match confidence with RFSA patterns
   */
  private calculateEnhancedMatchConfidence(
    bank: EnhancedTransaction,
    ledger: EnhancedTransaction
  ): EnhancedMatchResult {
    let confidence = 0;
    const reasons: string[] = [];
    const matchedFields: string[] = [];
    let matchType: EnhancedMatchResult['matchType'] = 'fuzzy';

    // 1. Check Number Matching (45% weight) - Highest priority for RFSA
    const checkMatch = this.extractAndMatchCheckNumber(bank, ledger);
    if (checkMatch.matched) {
      confidence += 0.45;
      reasons.push(`Check number match: ${checkMatch.pattern}`);
      matchedFields.push('check_number');
      matchType = 'check_number';
    }

    // 2. FIN Reference Matching (40% weight) - Critical RFSA pattern
    const finMatch = this.extractAndMatchFinReference(bank, ledger);
    if (finMatch.matched) {
      confidence += 0.4;
      reasons.push(`FIN reference match: ${finMatch.pattern}`);
      matchedFields.push('fin_reference');
      matchType = 'fin_reference';
    }

    // 3. Exact Amount Match (35% weight)
    const amountMatch = Math.abs(Math.abs(bank.amount) - Math.abs(ledger.amount)) < 1;
    if (amountMatch) {
      confidence += 0.35;
      reasons.push('Exact amount match');
      matchedFields.push('amount');
    }

    // 4. Date Proximity (15% weight)
    const dateMatch = this.calculateDateProximity(bank.date, ledger.date);
    if (dateMatch.score > 0) {
      confidence += 0.15 * dateMatch.score;
      reasons.push(`Date proximity: ${dateMatch.daysDiff} days`);
      if (dateMatch.score > 0.8) {
        matchedFields.push('date');
      }
    }

    // 5. Description Similarity (10% weight)
    const descSimilarity = this.calculateDescriptionSimilarity(
      bank.description || '',
      ledger.description || ''
    );
    if (descSimilarity.score > 0.6) {
      confidence += 0.1 * descSimilarity.score;
      reasons.push(`Description similarity: ${(descSimilarity.score * 100).toFixed(0)}%`);
      matchedFields.push('description');
    }

    // 6. Transaction Type Consistency (5% weight)
    const typeConsistent = this.isTransactionTypeConsistent(bank, ledger);
    if (typeConsistent) {
      confidence += 0.05;
      reasons.push('Transaction type consistent');
    }

    // 7. Reference Number Matching (20% weight)
    if (bank.reference && ledger.reference) {
      const refSimilarity = this.calculateStringSimilarity(bank.reference, ledger.reference);
      if (refSimilarity > 0.8) {
        confidence += 0.2;
        reasons.push(`Reference match: ${(refSimilarity * 100).toFixed(0)}%`);
        matchedFields.push('reference');
        if (matchType === 'fuzzy') {
          matchType = 'exact';
        }
      }
    }

    // 8. Voucher Number Matching (15% weight)
    if (bank.voucher_number && ledger.voucher_number) {
      const voucherMatch = bank.voucher_number === ledger.voucher_number;
      if (voucherMatch) {
        confidence += 0.15;
        reasons.push('Voucher number match');
        matchedFields.push('voucher_number');
      }
    }

    // 9. Serial Number Matching (10% weight)
    if (bank.serial_number && ledger.serial_number) {
      const serialMatch = bank.serial_number === ledger.serial_number;
      if (serialMatch) {
        confidence += 0.1;
        reasons.push('Serial number match');
        matchedFields.push('serial_number');
      }
    }

    // Cap confidence at 1.0
    confidence = Math.min(confidence, 1.0);

    const amountDifference = Math.abs(bank.amount - ledger.amount);
    const dateDifference = this.calculateDateDifference(bank.date, ledger.date);

    return {
      bankTransaction: bank,
      ledgerTransaction: ledger,
      confidence,
      matchType,
      reasons,
      matchedFields,
      amountDifference,
      dateDifference
    };
  }

  /**
   * Extract and match FIN reference patterns (RFSA-specific)
   */
  private extractAndMatchFinReference(
    bank: EnhancedTransaction,
    ledger: EnhancedTransaction
  ): { matched: boolean; pattern?: string } {
    const bankDesc = bank.description || '';
    const ledgerRef = ledger.reference || '';

    // Pattern 1: "FIN ********" -> "FIN/2030/25"
    const finPattern1 = bankDesc.match(/FIN\s*(\d{4})(\d{4})/i);
    if (finPattern1) {
      const [, num, year] = finPattern1;
      const expectedRef = `FIN/${num}/${year.slice(-2)}`;
      if (ledgerRef.includes(expectedRef)) {
        return { matched: true, pattern: expectedRef };
      }
    }

    // Pattern 2: "FIN/2052/2025" -> "FIN/2052/25"
    const finPattern2 = bankDesc.match(/FIN\/(\d{4})\/(\d{4})/i);
    if (finPattern2) {
      const [, num, year] = finPattern2;
      const expectedRef = `FIN/${num}/${year.slice(-2)}`;
      if (ledgerRef.includes(expectedRef)) {
        return { matched: true, pattern: expectedRef };
      }
    }

    // Pattern 3: "FIN ********" -> "FIN/3024/25" (space separated)
    const finPattern3 = bankDesc.match(/FIN\s*(\d{4})(\d{4})/i);
    if (finPattern3) {
      const [, num, year] = finPattern3;
      const expectedRef = `FIN/${num}/${year.slice(-2)}`;
      if (ledgerRef.includes(expectedRef)) {
        return { matched: true, pattern: expectedRef };
      }
    }

    // Pattern 4: Direct FIN number match with flexible formatting
    const finNumber = bankDesc.match(/FIN[\/\s]*(\d{4})/i);
    if (finNumber) {
      const num = finNumber[1];
      if (ledgerRef.includes(`FIN/${num}/25`) ||
          ledgerRef.includes(`FIN/${num}`) ||
          ledgerRef.includes(`Fin/${num}`)) {
        return { matched: true, pattern: `FIN/${num}` };
      }
    }

    // Pattern 5: Reverse match - extract from ledger and match to bank
    const ledgerFinMatch = ledgerRef.match(/FIN\/(\d{4})\/(\d{2})/i);
    if (ledgerFinMatch) {
      const [, num, shortYear] = ledgerFinMatch;
      const fullYear = `20${shortYear}`;
      if (bankDesc.includes(`FIN ${num}${fullYear}`) ||
          bankDesc.includes(`FIN/${num}/${fullYear}`) ||
          bankDesc.includes(`FIN${num}${fullYear}`)) {
        return { matched: true, pattern: `FIN/${num}/${shortYear}` };
      }
    }

    return { matched: false };
  }

  /**
   * Extract and match check number patterns (RFSA-specific)
   */
  private extractAndMatchCheckNumber(
    bank: EnhancedTransaction,
    ledger: EnhancedTransaction
  ): { matched: boolean; pattern?: string } {
    const bankText = `${bank.description || ''} ${bank.reference || ''}`;
    const ledgerRef = ledger.reference || '';

    // Pattern: "CHQ NO ********" or "********" in bank -> "CD********" or "CD-********" in ledger
    const checkNum = bankText.match(/(?:CHQ\s*NO\s*)?(\d{8,})/i);
    if (checkNum) {
      const number = checkNum[1];
      if (ledgerRef.includes(`CD${number}`) ||
          ledgerRef.includes(`CD-${number}`) ||
          ledgerRef.includes(number)) {
        return { matched: true, pattern: number };
      }
    }

    // Also try shorter check numbers (6+ digits)
    const shortCheckNum = bankText.match(/(?:CHQ\s*NO\s*)?(\d{6,})/i);
    if (shortCheckNum) {
      const number = shortCheckNum[1];
      if (ledgerRef.includes(`CD${number}`) ||
          ledgerRef.includes(`CD-${number}`)) {
        return { matched: true, pattern: number };
      }
    }

    return { matched: false };
  }

  /**
   * Calculate date proximity score
   */
  private calculateDateProximity(date1: string, date2: string): { score: number; daysDiff: number } {
    try {
      const d1 = new Date(date1);
      const d2 = new Date(date2);
      const diffMs = Math.abs(d1.getTime() - d2.getTime());
      const daysDiff = Math.floor(diffMs / (1000 * 60 * 60 * 24));

      if (daysDiff === 0) return { score: 1.0, daysDiff };
      if (daysDiff <= 1) return { score: 0.9, daysDiff };
      if (daysDiff <= 3) return { score: 0.8, daysDiff };
      if (daysDiff <= 7) return { score: 0.6, daysDiff };
      if (daysDiff <= 14) return { score: 0.3, daysDiff };
      return { score: 0, daysDiff };
    } catch {
      return { score: 0, daysDiff: 999 };
    }
  }

  /**
   * Calculate description similarity
   */
  private calculateDescriptionSimilarity(desc1: string, desc2: string): { score: number; method: string } {
    if (!desc1 || !desc2) return { score: 0, method: 'empty' };

    const clean1 = desc1.toLowerCase().replace(/[^a-z0-9\s]/g, ' ').replace(/\s+/g, ' ').trim();
    const clean2 = desc2.toLowerCase().replace(/[^a-z0-9\s]/g, ' ').replace(/\s+/g, ' ').trim();

    // Exact match
    if (clean1 === clean2) return { score: 1.0, method: 'exact' };

    // Contains match
    if (clean1.includes(clean2) || clean2.includes(clean1)) {
      return { score: 0.8, method: 'contains' };
    }

    // Token overlap
    const tokens1 = new Set(clean1.split(' ').filter(t => t.length > 2));
    const tokens2 = new Set(clean2.split(' ').filter(t => t.length > 2));

    if (tokens1.size === 0 || tokens2.size === 0) return { score: 0, method: 'no_tokens' };

    const intersection = new Set(Array.from(tokens1).filter(x => tokens2.has(x)));
    const union = new Set([...Array.from(tokens1), ...Array.from(tokens2)]);

    const jaccardScore = intersection.size / union.size;

    if (jaccardScore > 0.3) {
      return { score: jaccardScore, method: 'jaccard' };
    }

    return { score: 0, method: 'no_match' };
  }

  /**
   * Check transaction type consistency
   */
  private isTransactionTypeConsistent(bank: EnhancedTransaction, ledger: EnhancedTransaction): boolean {
    // Bank debit should match ledger credit (money leaving bank = money entering ledger)
    // Bank credit should match ledger debit (money entering bank = money leaving ledger)
    if (bank.amount < 0 && ledger.amount > 0) return true;
    if (bank.amount > 0 && ledger.amount < 0) return true;

    // Same type is also acceptable for some cases
    if ((bank.amount < 0 && ledger.amount < 0) || (bank.amount > 0 && ledger.amount > 0)) {
      return true;
    }

    return false;
  }

  /**
   * Calculate string similarity
   */
  private calculateStringSimilarity(str1: string, str2: string): number {
    if (!str1 || !str2) return 0;

    const s1 = str1.toLowerCase().trim();
    const s2 = str2.toLowerCase().trim();

    if (s1 === s2) return 100;

    // Simple substring matching
    if (s1.includes(s2) || s2.includes(s1)) {
      const shorter = s1.length < s2.length ? s1 : s2;
      const longer = s1.length >= s2.length ? s1 : s2;
      return (shorter.length / longer.length) * 80;
    }

    // Word overlap
    const words1 = s1.split(/\s+/);
    const words2 = s2.split(/\s+/);
    const commonWords = words1.filter(word => words2.includes(word));

    if (commonWords.length > 0) {
      const overlap = commonWords.length / Math.max(words1.length, words2.length);
      return overlap * 60;
    }

    return 0;
  }

  /**
   * Calculate date difference in days
   */
  private calculateDateDifference(date1: string, date2: string): number {
    const d1 = new Date(date1);
    const d2 = new Date(date2);
    return Math.abs(Math.floor((d1.getTime() - d2.getTime()) / (1000 * 60 * 60 * 24)));
  }

  /**
   * Validate matching completeness
   */
  private validateMatchingCompleteness(
    bankTransactions: EnhancedTransaction[],
    ledgerTransactions: EnhancedTransaction[],
    matches: EnhancedMatchResult[]
  ) {
    const matchedBankIds = new Set(matches.map(m => m.bankTransaction.id));
    const matchedLedgerIds = new Set(matches.map(m => m.ledgerTransaction.id));

    const unmatchedBank = bankTransactions.filter(t => !matchedBankIds.has(t.id));
    const unmatchedLedger = ledgerTransactions.filter(t => !matchedLedgerIds.has(t.id));

    // Identify high-value unmatched transactions
    const highValueThreshold = 50000;
    const highValueUnmatchedBank = unmatchedBank.filter(t => Math.abs(t.amount) > highValueThreshold);
    const highValueUnmatchedLedger = unmatchedLedger.filter(t => Math.abs(t.amount) > highValueThreshold);

    // Check for exact amount matches among unmatched transactions
    const potentialMissedMatches = [];
    for (const bankTxn of highValueUnmatchedBank) {
      for (const ledgerTxn of highValueUnmatchedLedger) {
        if (Math.abs(Math.abs(bankTxn.amount) - Math.abs(ledgerTxn.amount)) < 1) {
          const confidence = this.calculateEnhancedMatchConfidence(bankTxn, ledgerTxn);
          if (confidence.confidence > 0.2) {
            potentialMissedMatches.push({
              bank: bankTxn,
              ledger: ledgerTxn,
              confidence: confidence.confidence,
              reasons: confidence.reasons
            });
          }
        }
      }
    }

    const matchingCompleteness = bankTransactions.length > 0
      ? (matches.length / bankTransactions.length) * 100
      : 0;

    return {
      highValueUnmatchedBank: highValueUnmatchedBank.length,
      highValueUnmatchedLedger: highValueUnmatchedLedger.length,
      potentialMissedMatches: potentialMissedMatches.length,
      matchingCompleteness: Math.round(matchingCompleteness * 100) / 100
    };
  }

  /**
   * Get transactions for matching from database
   */
  private async getTransactionsForMatching(
    companyId: string,
    bankFileId?: string,
    ledgerFileId?: string
  ): Promise<{ bankTransactions: EnhancedTransaction[]; ledgerTransactions: EnhancedTransaction[] }> {
    let bankQuery = this.supabase
      .from('transactions')
      .select('*')
      .eq('company_id', companyId)
      .eq('transaction_type', 'bank_statement');

    let ledgerQuery = this.supabase
      .from('transactions')
      .select('*')
      .eq('company_id', companyId)
      .eq('transaction_type', 'ledger_entry');

    if (bankFileId) {
      bankQuery = bankQuery.eq('file_id', bankFileId);
    }

    if (ledgerFileId) {
      ledgerQuery = ledgerQuery.eq('file_id', ledgerFileId);
    }

    const [bankResult, ledgerResult] = await Promise.all([
      bankQuery,
      ledgerQuery
    ]);

    if (bankResult.error) {
      throw new Error(`Failed to fetch bank transactions: ${bankResult.error.message}`);
    }

    if (ledgerResult.error) {
      throw new Error(`Failed to fetch ledger transactions: ${ledgerResult.error.message}`);
    }

    return {
      bankTransactions: bankResult.data || [],
      ledgerTransactions: ledgerResult.data || []
    };
  }

  /**
   * Save enhanced matching results to database
   */
  async saveEnhancedMatchingResults(
    companyId: string,
    matchingResult: EnhancedMatchingResult,
    userId: string
  ): Promise<void> {
    const allMatches = [
      ...matchingResult.exactMatches,
      ...matchingResult.finReferenceMatches,
      ...matchingResult.checkNumberMatches,
      ...matchingResult.fuzzyMatches
    ];

    // Save reconciliations
    const reconciliations = allMatches.map(match => ({
      company_id: companyId,
      bank_transaction_id: match.bankTransaction.id,
      ledger_transaction_id: match.ledgerTransaction.id,
      match_type: match.matchType,
      match_confidence: match.confidence,
      matched_fields: match.matchedFields,
      amount_difference: match.amountDifference,
      date_difference: match.dateDifference,
      status: 'matched',
      created_by: userId,
      metadata: {
        reasons: match.reasons,
        statistics: matchingResult.statistics,
        validation: matchingResult.validation
      }
    }));

    const { error } = await this.supabase
      .from('reconciliations')
      .insert(reconciliations as any);

    if (error) {
      console.error('Error saving enhanced matching results:', error);
      throw new Error(`Failed to save matching results: ${error.message}`);
    }

    console.log(`Saved ${reconciliations.length} enhanced matches to database`);
  }
}

export default EnhancedTransactionMatcher;
