import { WorkflowState, WorkflowStep, WorkflowStatus, WorkflowProgress, WORKFLOW_STEPS } from '@/lib/types/workflow';
import { createClient } from '@/lib/supabase';

export class WorkflowManager {
  private supabase = createClient();

  async getWorkflowState(companyId: string): Promise<WorkflowState> {
    // Check files status to determine current step
    const { data: files } = await this.supabase
      .from('files')
      .select('status, processing_started_at, processing_completed_at, processing_error')
      .eq('company_id', companyId)
      .order('created_at', { ascending: false });

    if (!files || files.length === 0) {
      return {
        currentStep: 'upload',
        status: 'not_started',
        completedSteps: [],
        progress: 0
      };
    }

    // Determine current step based on file statuses
    const hasUploadedFiles = files.some((f: any) => f.status === 'uploaded' || f.status === 'processing' || f.status === 'extracted' || f.status === 'completed');
    const hasProcessedFiles = files.some((f: any) => f.status === 'extracted' || f.status === 'completed');
    const hasCompletedFiles = files.some((f: any) => f.status === 'completed');

    // Check reconciliations
    const { data: reconciliations } = await this.supabase
      .from('reconciliations')
      .select('status')
      .eq('company_id', companyId);

    const hasReconciliations = reconciliations && reconciliations.length > 0;

    // Check discrepancies status to determine if review is needed or completed
    const { data: discrepancies } = await this.supabase
      .from('discrepancies')
      .select('status')
      .eq('company_id', companyId);
    const totalDiscrepancies = (discrepancies?.length || 0);
    const resolvedDiscrepancies = (discrepancies || []).filter((d: any) => d.status === 'resolved').length;

    // Review is only completed if:
    // 1. We have reconciliations AND
    // 2. We have detected discrepancies (totalDiscrepancies > 0) AND all are resolved
    // OR we have confirmed there are no discrepancies after detection
    const reviewCompleted = hasReconciliations && totalDiscrepancies > 0 && resolvedDiscrepancies === totalDiscrepancies;

    // Check reports
    const { data: reports } = await this.supabase
      .from('reports')
      .select('status')
      .eq('company_id', companyId);

    const hasReports = reports && reports.length > 0;

    let currentStep: WorkflowStep = 'upload';
    let completedSteps: WorkflowStep[] = [];
    let status: WorkflowStatus = 'not_started';
    let progress = 0;

    if (hasUploadedFiles) {
      completedSteps.push('upload');
      progress += 20;
      currentStep = 'processing';

      if (hasProcessedFiles) {
        completedSteps.push('processing');
        progress += 20;
        currentStep = 'matching';

        if (hasReconciliations) {
          completedSteps.push('matching');
          progress += 20;
          currentStep = 'review';

          if (reviewCompleted) {
            completedSteps.push('review');
            progress += 20;
            currentStep = 'reports';

            if (hasReports) {
              completedSteps.push('reports');
              progress = 100;
              status = 'completed';
            }
          }
        }
      }
    }

    // Check for errors
    const hasErrors = files.some((f: any) => f.status === 'failed' || f.processing_error);
    if (hasErrors) {
      status = 'failed';
    } else if (progress > 0 && status !== 'completed') {
      status = 'in_progress';
    }

    return {
      currentStep,
      status,
      completedSteps,
      progress,
      startedAt: hasUploadedFiles ? new Date() : undefined
    };
  }

  async getStepProgress(companyId: string, step: WorkflowStep): Promise<WorkflowProgress> {
    const workflowState = await this.getWorkflowState(companyId);
    const stepConfig = WORKFLOW_STEPS.find(s => s.step === step);

    if (!stepConfig) {
      throw new Error(`Unknown step: ${step}`);
    }

    const isCompleted = workflowState.completedSteps.includes(step);
    const isCurrent = workflowState.currentStep === step;
    const isStarted = workflowState.completedSteps.includes(step) || isCurrent;

    let progress = 0;
    let message = stepConfig.description;
    let canProceed = false;

    switch (step) {
      case 'upload':
        const { data: uploadFiles } = await this.supabase
          .from('files')
          .select('status')
          .eq('company_id', companyId);

        if (uploadFiles && uploadFiles.length > 0) {
          progress = 100;
          message = `${uploadFiles.length} files uploaded successfully`;
          canProceed = true;
        }
        break;

      case 'processing':
        const { data: processingFiles } = await this.supabase
          .from('files')
          .select('status, processing_started_at, processing_completed_at')
          .eq('company_id', companyId);

        if (processingFiles && processingFiles.length > 0) {
          const completed = processingFiles.filter((f: any) => f.status === 'extracted' || f.status === 'completed').length;
          progress = (completed / processingFiles.length) * 100;

          if (progress === 100) {
            message = `All ${processingFiles.length} files processed successfully`;
            canProceed = true;
          } else {
            const inProgress = processingFiles.filter((f: any) => f.status === 'processing').length;
            message = `Processing ${inProgress} files... ${completed}/${processingFiles.length} completed`;
          }
        }
        break;

      case 'matching':
        const { data: matchingReconciliations } = await this.supabase
          .from('reconciliations')
          .select('status, match_confidence')
          .eq('company_id', companyId);

        if (matchingReconciliations && matchingReconciliations.length > 0) {
          const highConfidence = matchingReconciliations.filter((r: any) => (r.match_confidence || 0) > 0.8).length;
          progress = (highConfidence / matchingReconciliations.length) * 100;

          if (progress > 0) {
            message = `${highConfidence}/${matchingReconciliations.length} high-confidence matches found`;
            canProceed = true;
          }
        }
        break;

      case 'review':
        const { data: reviewDiscrepancies } = await this.supabase
          .from('discrepancies')
          .select('status')
          .eq('company_id', companyId);

        if (reviewDiscrepancies && reviewDiscrepancies.length > 0) {
          const reviewed = reviewDiscrepancies.filter((d: any) => d.status === 'resolved').length;
          progress = (reviewed / reviewDiscrepancies.length) * 100;
          canProceed = progress === 100; // Only proceed when ALL discrepancies are resolved

          if (progress === 0) {
            message = `${reviewDiscrepancies.length} discrepancies need review`;
          } else if (progress === 100) {
            message = `All ${reviewDiscrepancies.length} discrepancies reviewed and resolved`;
          } else {
            message = `${reviewed}/${reviewDiscrepancies.length} discrepancies reviewed (${Math.round(progress)}% complete)`;
          }
        } else {
          // No discrepancies found - check if detection has been run
          const { data: reconciliations } = await this.supabase
            .from('reconciliations')
            .select('id')
            .eq('company_id', companyId)
            .limit(1);

          if (reconciliations && reconciliations.length > 0) {
            // Reconciliations exist but no discrepancies - need to run detection
            progress = 0;
            message = 'Discrepancy detection needed - click "Start Review" to analyze unmatched transactions';
            canProceed = false;
          } else {
            // No reconciliations at all
            progress = 0;
            message = 'Complete matching first before reviewing discrepancies';
            canProceed = false;
          }
        }
        break;

      case 'reports':
        const { data: reportData } = await this.supabase
          .from('reports')
          .select('status')
          .eq('company_id', companyId);

        if (reportData && reportData.length > 0) {
          progress = 100;
          message = `${reportData.length} reports generated successfully`;
          canProceed = true;
        } else {
          // Check if review is actually completed
          const { data: reviewDiscrepancies } = await this.supabase
            .from('discrepancies')
            .select('status')
            .eq('company_id', companyId);

          if (reviewDiscrepancies && reviewDiscrepancies.length > 0) {
            const resolvedCount = reviewDiscrepancies.filter((d: any) => d.status === 'resolved').length;
            if (resolvedCount === reviewDiscrepancies.length) {
              progress = 0;
              message = 'Ready to generate reports - all discrepancies resolved';
              canProceed = true;
            } else {
              progress = 0;
              message = `Complete review first: ${resolvedCount}/${reviewDiscrepancies.length} discrepancies resolved`;
              canProceed = false;
            }
          } else {
            progress = 0;
            message = 'Complete review step first before generating reports';
            canProceed = false;
          }
        }
        break;
    }

    return {
      step,
      status: isCompleted ? 'completed' : isCurrent ? 'in_progress' : 'not_started',
      progress: Math.round(progress),
      message,
      canProceed,
      nextStep: this.getNextStep(step)
    };
  }

  private getNextStep(currentStep: WorkflowStep): WorkflowStep | undefined {
    const currentIndex = WORKFLOW_STEPS.findIndex(s => s.step === currentStep);
    return WORKFLOW_STEPS[currentIndex + 1]?.step;
  }

  async canStartStep(companyId: string, step: WorkflowStep): Promise<boolean> {
    const stepConfig = WORKFLOW_STEPS.find(s => s.step === step);
    if (!stepConfig) return false;

    // Check if all dependencies are completed
    for (const dependency of stepConfig.dependencies) {
      const dependencyProgress = await this.getStepProgress(companyId, dependency);
      if (!dependencyProgress.canProceed) {
        return false;
      }
    }

    return true;
  }

  async startStep(companyId: string, step: WorkflowStep): Promise<void> {
    const canStart = await this.canStartStep(companyId, step);
    if (!canStart) {
      throw new Error(`Cannot start step ${step} - dependencies not met`);
    }

    // Trigger the appropriate action based on step
    switch (step) {
      case 'processing':
        await this.startProcessing(companyId);
        break;
      case 'matching':
        await this.startMatching(companyId);
        break;
      case 'review':
        await this.startReview(companyId);
        break;
      case 'reports':
        await this.generateReports(companyId);
        break;
    }
  }

  private async startProcessing(companyId: string): Promise<void> {
    // Call the workflow processing API
    const response = await fetch('/api/workflow/process', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        step: 'processing'
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to start processing');
    }
  }

  private async startMatching(companyId: string): Promise<void> {
    // Call the enhanced matching API
    const response = await fetch('/api/enhanced-match-transactions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        // No specific file IDs - will process all available files
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to start matching');
    }
  }

  private async startReview(companyId: string): Promise<void> {
    // First check if discrepancies exist, if not, trigger detection
    const { data: existingDiscrepancies } = await this.supabase
      .from('discrepancies')
      .select('id')
      .eq('company_id', companyId)
      .limit(1);

    if (!existingDiscrepancies || existingDiscrepancies.length === 0) {
      // Trigger discrepancy detection first
      const response = await fetch('/api/detect-discrepancies', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to detect discrepancies');
      }
    }

    // Then start AI analysis if requested
    const response = await fetch('/api/analyze-discrepancies', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        generateJournalVouchers: true,
        analysisOptions: {
          includeAIExplanations: true
        }
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to start AI analysis');
    }
  }

  private async generateReports(companyId: string): Promise<void> {
    // Call the workflow processing API for reports
    const response = await fetch('/api/workflow/process', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        step: 'reports',
        options: {
          includeJournalVouchers: true
        }
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to generate reports');
    }
  }
}
