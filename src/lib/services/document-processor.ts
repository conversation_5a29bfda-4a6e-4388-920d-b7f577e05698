import { SandboxManager, ProcessingResult } from '@/lib/e2b/sandbox-manager'
import { createClient } from '@/lib/supabase'

export interface DocumentProcessingRequest {
  fileId: string
  filePath: string
  fileName: string
  fileType: 'pdf' | 'xlsx' | 'xls' | 'csv'
  documentType: 'bank_statement' | 'ledger_entry'
  companyId: string
}

export interface Transaction {
  date: string
  amount: number
  description: string
  reference?: string
  account?: string
  category?: string
  balance?: number
}

export interface ProcessingResponse {
  success: boolean
  fileId: string
  transactions?: Transaction[]
  error?: string
  processingTime: number
  aiModel: 'mistral' | 'gemini'
}

export class DocumentProcessor {
  private sandboxManager: SandboxManager
  private supabase: ReturnType<typeof createClient>

  constructor() {
    this.sandboxManager = SandboxManager.getInstance()
    this.supabase = createClient()
  }

  /**
   * Process a document based on its type
   */
  async processDocument(request: DocumentProcessingRequest): Promise<ProcessingResponse> {
    const startTime = Date.now()

    try {
      // Update file status to processing
      await this.updateFileStatus(request.fileId, 'processing')

      // Get file content from Supabase Storage
      const fileContent = await this.getFileContent(request.filePath)

      // Create sandbox for processing
      const sandbox = await this.sandboxManager.createSandbox({
        timeout: 300, // 5 minutes
        memory: '2GB',
        networkAccess: true
      })

      let result: ProcessingResult
      let aiModel: 'mistral' | 'gemini'

      try {
        // Route to appropriate AI model based on file type
        if (request.fileType === 'pdf' && request.documentType === 'bank_statement') {
          // Use Mistral for PDF bank statements
          aiModel = 'mistral'
          result = await this.sandboxManager.processPDFWithMistral(
            sandbox,
            fileContent,
            request.fileName,
            process.env.MISTRAL_API_KEY!
          )
        } else {
          // Use Gemini for Excel/CSV ledgers
          aiModel = 'gemini'
          result = await this.sandboxManager.processExcelWithGemini(
            sandbox,
            fileContent,
            request.fileName,
            process.env.GEMINI_API_KEY!
          )
        }

        if (result.success && result.data?.transactions) {
          // Store transactions in database
          await this.storeTransactions(
            request.fileId,
            request.companyId,
            result.data.transactions,
            request.documentType === 'ledger_entry' ? 'ledger_entry' : 'bank_statement',
            result.data
          )

          // Update file status to completed
          await this.updateFileStatus(request.fileId, 'completed', {
            transaction_count: result.data.transactions.length,
            processing_time: result.processingTime,
            ai_model: aiModel
          })

          return {
            success: true,
            fileId: request.fileId,
            transactions: result.data.transactions,
            processingTime: Date.now() - startTime,
            aiModel
          }
        } else {
          // Processing failed
          await this.updateFileStatus(request.fileId, 'failed', {
            error: result.error,
            processing_time: result.processingTime,
            ai_model: aiModel
          })

          return {
            success: false,
            fileId: request.fileId,
            error: result.error || 'Processing failed',
            processingTime: Date.now() - startTime,
            aiModel
          }
        }

      } finally {
        // Always clean up sandbox
        await this.sandboxManager.closeSandbox(sandbox)
      }

    } catch (error) {
      console.error('Document processing error:', error)

      // Update file status to failed
      await this.updateFileStatus(request.fileId, 'failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        processing_time: Date.now() - startTime
      })

      return {
        success: false,
        fileId: request.fileId,
        error: error instanceof Error ? error.message : 'Unknown error',
        processingTime: Date.now() - startTime,
        aiModel: 'mistral' // Default fallback
      }
    }
  }

  /**
   * Get file content from Supabase Storage
   */
  private async getFileContent(filePath: string): Promise<Buffer> {
    try {
      const { data, error } = await this.supabase.storage
        .from('documents')
        .download(filePath)

      if (error) {
        throw new Error(`Failed to download file: ${error.message}`)
      }

      return Buffer.from(await data.arrayBuffer())
    } catch (error) {
      console.error('Error downloading file:', error)
      throw error
    }
  }

  /**
   * Update file processing status in database
   */
  private async updateFileStatus(
    fileId: string,
    status: 'processing' | 'completed' | 'failed',
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      const updateData: any = {
        status,
        updated_at: new Date().toISOString()
      }

      if (status === 'processing') {
        updateData.processing_started_at = new Date().toISOString()
      } else if (status === 'completed') {
        updateData.processing_completed_at = new Date().toISOString()
      } else if (status === 'failed') {
        updateData.processing_error = metadata?.error
      }

      if (metadata) {
        updateData.metadata = {
          ...updateData.metadata,
          ...metadata
        }
      }

      const { error } = await (this.supabase as any)
        .from('files')
        .update(updateData)
        .eq('id', fileId)

      if (error) {
        console.error('Failed to update file status:', error)
      }
    } catch (error) {
      console.error('Error updating file status:', error)
    }
  }

  /**
   * Store extracted transactions in database
   */
  private async storeTransactions(
    fileId: string,
    companyId: string,
    transactions: Transaction[],
    transactionType: 'bank_statement' | 'ledger_entry',
    rawData: any
  ): Promise<void> {
    try {
      const transactionRecords = transactions.map(transaction => ({
        id: crypto.randomUUID(),
        company_id: companyId,
        file_id: fileId,
        transaction_type: transactionType as 'bank_statement' | 'ledger_entry',
        date: transaction.date,
        amount: transaction.amount,
        description: transaction.description,
        reference: transaction.reference,
        account: transaction.account,
        category: transaction.category,
        balance: transaction.balance,
        raw_data: {
          original_transaction: JSON.parse(JSON.stringify(transaction)),
          processing_metadata: rawData
        } as any,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }))

      const { error } = await (this.supabase as any)
        .from('transactions')
        .insert(transactionRecords)

      if (error) {
        console.error('Failed to store transactions:', error)
        throw new Error(`Failed to store transactions: ${error.message}`)
      }

      console.log(`Successfully stored ${transactions.length} transactions for file ${fileId}`)
    } catch (error) {
      console.error('Error storing transactions:', error)
      throw error
    }
  }

  /**
   * Get processing status for a file
   */
  async getProcessingStatus(fileId: string): Promise<{
    status: string
    progress?: number
    error?: string
    transactionCount?: number
  }> {
    try {
      const { data, error } = await (this.supabase as any)
        .from('files')
        .select('status, processing_error, metadata')
        .eq('id', fileId)
        .single()

      if (error) {
        throw new Error(`Failed to get file status: ${error.message}`)
      }

      return {
        status: data.status,
        error: data.processing_error || undefined,
        transactionCount: (data.metadata as any)?.transaction_count
      }
    } catch (error) {
      console.error('Error getting processing status:', error)
      return {
        status: 'unknown',
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Get extracted transactions for a file
   */
  async getExtractedTransactions(fileId: string): Promise<Transaction[]> {
    try {
      const { data, error } = await (this.supabase as any)
        .from('transactions')
        .select('*')
        .eq('file_id', fileId)
        .order('date', { ascending: true })

      if (error) {
        throw new Error(`Failed to get transactions: ${error.message}`)
      }

      return data.map((record: any) => ({
        date: record.date,
        amount: record.amount,
        description: record.description || '',
        reference: record.reference || undefined,
        account: record.account || undefined,
        category: record.category || undefined,
        balance: record.balance || undefined
      }))
    } catch (error) {
      console.error('Error getting transactions:', error)
      return []
    }
  }
}

export default DocumentProcessor
