export type WorkflowStep =
  | 'upload'
  | 'processing'
  | 'matching'
  | 'review'
  | 'reports';

export type WorkflowStatus =
  | 'not_started'
  | 'in_progress'
  | 'completed'
  | 'failed'
  | 'paused';

export interface WorkflowState {
  currentStep: WorkflowStep;
  status: WorkflowStatus;
  completedSteps: WorkflowStep[];
  startedAt?: Date;
  completedAt?: Date;
  error?: string;
  progress: number; // 0-100
}

export interface WorkflowProgress {
  step: WorkflowStep;
  status: WorkflowStatus;
  progress: number;
  message: string;
  canProceed: boolean;
  nextStep?: WorkflowStep;
}

export interface StepConfig {
  step: WorkflowStep;
  title: string;
  description: string;
  icon: string;
  estimatedTime: string;
  required: boolean;
  dependencies: WorkflowStep[];
}

export const WORKFLOW_STEPS: StepConfig[] = [
  {
    step: 'upload',
    title: 'Upload Documents',
    description: 'Upload bank statements and ledger files',
    icon: '📤',
    estimatedTime: '2 minutes',
    required: true,
    dependencies: []
  },
  {
    step: 'processing',
    title: 'Process Documents',
    description: 'Extract transactions using AI and OCR',
    icon: '⚙️',
    estimatedTime: '5-10 minutes',
    required: true,
    dependencies: ['upload']
  },
  {
    step: 'matching',
    title: 'Match Transactions',
    description: 'Automatically match bank and ledger transactions',
    icon: '🔗',
    estimatedTime: '3-5 minutes',
    required: true,
    dependencies: ['processing']
  },
  {
    step: 'review',
    title: 'Review Matches',
    description: 'Review discrepancies and approve matches',
    icon: '👀',
    estimatedTime: '10-15 minutes',
    required: true,
    dependencies: ['matching']
  },
  {
    step: 'reports',
    title: 'Generate Reports',
    description: 'Create reconciliation reports and journal vouchers',
    icon: '📊',
    estimatedTime: '2-3 minutes',
    required: true,
    dependencies: ['review']
  }
];
