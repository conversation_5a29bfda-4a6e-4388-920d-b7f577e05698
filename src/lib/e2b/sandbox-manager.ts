import { Sandbox } from '@e2b/code-interpreter'

export interface SandboxConfig {
  timeout?: number
  memory?: string
  networkAccess?: boolean
}

export interface ProcessingResult {
  success: boolean
  data?: any
  error?: string
  processingTime: number
}

export class SandboxManager {
  private static instance: SandboxManager
  private activeSandboxes: Map<string, Sandbox> = new Map()

  private constructor() {}

  static getInstance(): SandboxManager {
    if (!SandboxManager.instance) {
      SandboxManager.instance = new SandboxManager()
    }
    return SandboxManager.instance
  }

  /**
   * Create a new E2B sandbox for secure file processing
   */
  async createSandbox(config: SandboxConfig = {}): Promise<Sandbox> {
    const defaultConfig = {
      timeout: 300, // 5 minutes
      memory: '2GB',
      networkAccess: true, // Needed for AI API calls
      ...config
    }

    try {
      const sandbox = await Sandbox.create({
        timeoutMs: defaultConfig.timeout * 1000, // Convert to milliseconds
        // Additional E2B configuration
      })

      // Install required Python packages
      await this.setupPythonEnvironment(sandbox)

      const sandboxId = this.generateSandboxId()
      this.activeSandboxes.set(sandboxId, sandbox)

      return sandbox
    } catch (error) {
      console.error('Failed to create E2B sandbox:', error)
      throw new Error(`Sandbox creation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Setup Python environment with required packages
   */
  private async setupPythonEnvironment(sandbox: Sandbox): Promise<void> {
    const installScript = `
import subprocess
import sys

# Install required packages
packages = [
    'PyPDF2==3.0.1',
    'pandas==2.1.4',
    'openpyxl==3.1.2',
    'requests==2.31.0',
    'python-dateutil==2.8.2',
    'fuzzywuzzy==0.18.0',
    'python-Levenshtein==0.21.1'
]

for package in packages:
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
        print(f"Successfully installed {package}")
    except subprocess.CalledProcessError as e:
        print(f"Failed to install {package}: {e}")
        raise

print("All packages installed successfully!")
`

    try {
      const result = await sandbox.runCode(installScript)
      if (result.error) {
        throw new Error(`Package installation failed: ${result.error.name}: ${result.error.value}`)
      }
      console.log('Python environment setup completed')
    } catch (error) {
      console.error('Failed to setup Python environment:', error)
      throw error
    }
  }

  /**
   * Process a PDF document using Mistral AI
   */
  async processPDFWithMistral(
    sandbox: Sandbox,
    fileContent: Buffer,
    fileName: string,
    mistralApiKey: string
  ): Promise<ProcessingResult> {
    const startTime = Date.now()

    try {
      // Upload file to sandbox
      const filePath = `/tmp/${fileName}`
      await sandbox.files.write(filePath, new Blob([new Uint8Array(fileContent)]))

      // Python script for PDF processing with Mistral
      const processingScript = `
import PyPDF2
import requests
import json
import io
from datetime import datetime
import re

def extract_text_from_pdf(file_path):
    """Extract text from PDF file"""
    try:
        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text = ""
            for page in pdf_reader.pages:
                text += page.extract_text() + "\\n"
        return text
    except Exception as e:
        raise Exception(f"PDF extraction failed: {str(e)}")

def process_with_mistral(text, api_key):
    """Process extracted text with Mistral AI"""
    url = "https://api.mistral.ai/v1/chat/completions"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    prompt = f"""
    You are a financial document processing expert. Extract transaction data from this bank statement text.
    
    For each transaction, extract:
    - Date (YYYY-MM-DD format)
    - Amount (positive for credits, negative for debits)
    - Description
    - Reference number (if available)
    - Balance (if available)
    
    Return the data as a JSON array of transactions. Example format:
    [
        {{
            "date": "2025-01-15",
            "amount": -245.50,
            "description": "Office Supplies Purchase",
            "reference": "REF001",
            "balance": 5432.10
        }}
    ]
    
    Bank Statement Text:
    {text[:4000]}  # Limit text to avoid token limits
    """
    
    data = {
        "model": "mistral-large-latest",
        "messages": [
            {"role": "user", "content": prompt}
        ],
        "temperature": 0.1,
        "max_tokens": 2000
    }
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=60)
        response.raise_for_status()
        
        result = response.json()
        content = result['choices'][0]['message']['content']
        
        # Extract JSON from response
        json_match = re.search(r'\\[.*\\]', content, re.DOTALL)
        if json_match:
            transactions = json.loads(json_match.group())
            return transactions
        else:
            raise Exception("No valid JSON found in Mistral response")
            
    except Exception as e:
        raise Exception(f"Mistral API call failed: {str(e)}")

# Main processing
try:
    # Extract text from PDF
    pdf_text = extract_text_from_pdf("${filePath}")
    
    # Process with Mistral AI
    transactions = process_with_mistral(pdf_text, "${mistralApiKey}")
    
    # Return results
    result = {
        "success": True,
        "transactions": transactions,
        "extracted_text_length": len(pdf_text),
        "transaction_count": len(transactions)
    }
    
    print(json.dumps(result))
    
except Exception as e:
    error_result = {
        "success": False,
        "error": str(e)
    }
    print(json.dumps(error_result))
`

      const result = await sandbox.runCode(processingScript)
      const processingTime = Date.now() - startTime

      if (result.error) {
        return {
          success: false,
          error: `${result.error.name}: ${result.error.value}`,
          processingTime
        }
      }

      // Parse the JSON output
      const output = result.text?.trim() || ''
      if (!output) {
        return {
          success: false,
          error: 'No output received from processing',
          processingTime
        }
      }

      const parsedResult = JSON.parse(output)

      return {
        success: parsedResult.success,
        data: parsedResult.success ? parsedResult : undefined,
        error: parsedResult.success ? undefined : parsedResult.error,
        processingTime
      }

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        processingTime: Date.now() - startTime
      }
    }
  }

  /**
   * Process Excel/CSV document using Gemini 2.5 Flash
   */
  async processExcelWithGemini(
    sandbox: Sandbox,
    fileContent: Buffer,
    fileName: string,
    geminiApiKey: string
  ): Promise<ProcessingResult> {
    const startTime = Date.now()

    try {
      // Upload file to sandbox
      const filePath = `/tmp/${fileName}`
      await sandbox.files.write(filePath, new Blob([new Uint8Array(fileContent)]))

      // Python script for Excel/CSV processing with Gemini
      const processingScript = `
import pandas as pd
import requests
import json
from datetime import datetime
import re

def read_excel_or_csv(file_path):
    """Read Excel or CSV file and convert to structured data"""
    try:
        if file_path.endswith('.csv'):
            df = pd.read_csv(file_path)
        else:
            df = pd.read_excel(file_path)
        
        # Convert to JSON for AI processing
        return df.to_json(orient='records', date_format='iso')
    except Exception as e:
        raise Exception(f"File reading failed: {str(e)}")

def process_with_gemini(data_json, api_key):
    """Process structured data with Gemini 2.5 Flash"""
    url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent"
    
    headers = {
        "Content-Type": "application/json"
    }
    
    prompt = f"""
    You are a financial data processing expert. Process this ledger data and extract transaction information.
    
    For each row, extract and standardize:
    - Date (convert to YYYY-MM-DD format)
    - Amount (positive for credits, negative for debits, ensure proper numeric format)
    - Description (clean and standardize)
    - Reference number (if available)
    - Account (if available)
    - Category (if available)
    
    Return as a JSON array of transactions. Example format:
    [
        {{
            "date": "2025-01-15",
            "amount": -245.50,
            "description": "Office Supplies Purchase",
            "reference": "REF001",
            "account": "Operating Account",
            "category": "Office Expenses"
        }}
    ]
    
    Ledger Data:
    {data_json[:4000]}  # Limit data to avoid token limits
    """
    
    data = {
        "contents": [{
            "parts": [{"text": prompt}]
        }],
        "generationConfig": {
            "temperature": 0.1,
            "maxOutputTokens": 2000
        }
    }
    
    try:
        response = requests.post(f"{url}?key={api_key}", headers=headers, json=data, timeout=60)
        response.raise_for_status()
        
        result = response.json()
        content = result['candidates'][0]['content']['parts'][0]['text']
        
        # Extract JSON from response
        json_match = re.search(r'\\[.*\\]', content, re.DOTALL)
        if json_match:
            transactions = json.loads(json_match.group())
            return transactions
        else:
            raise Exception("No valid JSON found in Gemini response")
            
    except Exception as e:
        raise Exception(f"Gemini API call failed: {str(e)}")

# Main processing
try:
    # Read Excel/CSV file
    data_json = read_excel_or_csv("${filePath}")
    
    # Process with Gemini AI
    transactions = process_with_gemini(data_json, "${geminiApiKey}")
    
    # Return results
    result = {
        "success": True,
        "transactions": transactions,
        "original_data_length": len(data_json),
        "transaction_count": len(transactions)
    }
    
    print(json.dumps(result))
    
except Exception as e:
    error_result = {
        "success": False,
        "error": str(e)
    }
    print(json.dumps(error_result))
`

      const result = await sandbox.runCode(processingScript)
      const processingTime = Date.now() - startTime

      if (result.error) {
        return {
          success: false,
          error: `${result.error.name}: ${result.error.value}`,
          processingTime
        }
      }

      // Parse the JSON output
      const output = result.text?.trim() || ''
      if (!output) {
        return {
          success: false,
          error: 'No output received from processing',
          processingTime
        }
      }

      const parsedResult = JSON.parse(output)

      return {
        success: parsedResult.success,
        data: parsedResult.success ? parsedResult : undefined,
        error: parsedResult.success ? undefined : parsedResult.error,
        processingTime
      }

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        processingTime: Date.now() - startTime
      }
    }
  }

  /**
   * Clean up sandbox resources
   */
  async closeSandbox(sandbox: Sandbox): Promise<void> {
    try {
      // E2B sandboxes are automatically cleaned up
      // Just remove from our tracking
      for (const [id, sb] of Array.from(this.activeSandboxes.entries())) {
        if (sb === sandbox) {
          this.activeSandboxes.delete(id)
          break
        }
      }
    } catch (error) {
      console.error('Error cleaning up sandbox:', error)
    }
  }

  /**
   * Clean up all active sandboxes
   */
  async closeAllSandboxes(): Promise<void> {
    const promises = Array.from(this.activeSandboxes.entries()).map(([id, sandbox]) => 
      this.closeSandbox(sandbox)
    )
    await Promise.all(promises)
  }

  private generateSandboxId(): string {
    return `sandbox_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
}

export default SandboxManager
