// Temporary type definitions to fix TypeScript errors
// These will be replaced when we can generate proper types from Supabase

export interface DatabaseReport {
  id: string
  company_id: string
  name: string
  type: 'reconciliation' | 'discrepancy' | 'journal_voucher' | 'summary'
  status: 'generating' | 'completed' | 'failed'
  date_from?: string
  date_to?: string
  bank_statement_file_id?: string
  ledger_file_id?: string
  report_data: any
  generated_by: string
  generated_at: string
  file_path?: string
  file_size?: number
  error_message?: string
}

export interface DatabaseDiscrepancy {
  id: string
  company_id: string
  type: 'bank_only' | 'ledger_only' | 'amount_mismatch' | 'date_discrepancy' | 'reference_mismatch'
  status: 'pending' | 'reviewed' | 'resolved'
  bank_transaction_id?: string
  ledger_transaction_id?: string
  amount_difference: number
  date_variance: number
  description: string
  category?: string
  notes?: string
  recommendation: string
  reviewed_by?: string
  reviewed_at?: string
  resolved_by?: string
  resolved_at?: string
  created_at: string
}

export interface DatabaseJournalVoucher {
  id: string
  company_id: string
  report_id?: string
  discrepancy_id?: string
  voucher_number: string
  date: string
  description: string
  entries: any // JSONB
  total_amount: number
  status: 'draft' | 'approved' | 'posted'
  created_by: string
  created_at: string
}

// Extend the existing database types
declare module '@/lib/supabase' {
  interface Database {
    public: {
      Tables: {
        reports: {
          Row: DatabaseReport
          Insert: Omit<DatabaseReport, 'id' | 'created_at'>
          Update: Partial<Omit<DatabaseReport, 'id'>>
        }
        discrepancies: {
          Row: DatabaseDiscrepancy
          Insert: Omit<DatabaseDiscrepancy, 'id' | 'created_at'>
          Update: Partial<Omit<DatabaseDiscrepancy, 'id'>>
        }
        journal_vouchers: {
          Row: DatabaseJournalVoucher
          Insert: Omit<DatabaseJournalVoucher, 'id' | 'created_at'>
          Update: Partial<Omit<DatabaseJournalVoucher, 'id'>>
        }
      }
    }
  }
}
