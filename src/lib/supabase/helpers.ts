import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/types/database';

type SupabaseClientType = SupabaseClient<Database>;

/**
 * Helper function to update a record in Supabase with proper typing
 */
export async function typedUpdate<T extends keyof Database['public']['Tables']>(
  supabase: SupabaseClientType,
  table: T,
  data: Database['public']['Tables'][T]['Update'],
  matchColumn: string,
  matchValue: string | string[]
) {
  const query = supabase.from(table).update(data as any);

  if (Array.isArray(matchValue)) {
    return query.in(matchColumn, matchValue as any);
  } else {
    return query.eq(matchColumn, matchValue as any);
  }
}

/**
 * Helper function to insert records in Supabase with proper typing
 */
export async function typedInsert<T extends keyof Database['public']['Tables']>(
  supabase: SupabaseClientType,
  table: T,
  data: Database['public']['Tables'][T]['Insert'] | Database['public']['Tables'][T]['Insert'][]
) {
  return supabase.from(table).insert(data as any);
}
