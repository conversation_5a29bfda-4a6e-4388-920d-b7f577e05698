/**
 * Status mapping utilities for reconciling database and UI status values
 */

// Database status types
export type DatabaseStatus = 'uploaded' | 'uploading' | 'processing' | 'completed' | 'failed';

// UI status types
export type UIStatus = 'uploaded' | 'uploading' | 'processing' | 'extracted' | 'failed' | 'no_files' | 'files_uploaded' | 'ready_for_reconciliation';

/**
 * Maps database status values to UI-friendly status values
 */
export const mapDatabaseStatusToUIStatus = (status: DatabaseStatus): UIStatus => {
  switch (status) {
    case 'completed':
      return 'extracted'; // Map DB 'completed' to UI 'extracted'
    case 'uploaded':
      return 'files_uploaded'; // Map DB 'uploaded' to UI 'files_uploaded'
    case 'uploading':
    case 'processing':
    case 'failed':
      return status;
    default:
      // Handle unknown status (should not happen with proper typing)
      console.warn(`Unknown database status: ${status}`);
      return 'uploaded';
  }
};

/**
 * Maps UI status values to database-compatible status values
 */
export const mapUIStatusToDatabaseStatus = (status: UIStatus): DatabaseStatus => {
  switch (status) {
    case 'extracted':
      return 'completed'; // Map UI 'extracted' to DB 'completed'
    case 'files_uploaded':
      return 'uploaded'; // Map UI 'files_uploaded' to DB 'uploaded'
    case 'uploaded':
    case 'uploading':
    case 'processing':
    case 'failed':
      return status;
    case 'no_files':
    case 'ready_for_reconciliation':
      // These are UI-only statuses, default to 'uploaded' in DB
      return 'uploaded';
    default:
      // Handle unknown status (should not happen with proper typing)
      console.warn(`Unknown UI status: ${status}`);
      return 'uploaded';
  }
};

/**
 * Get a human-readable status label
 */
export const getStatusLabel = (status: UIStatus): string => {
  switch (status) {
    case 'uploaded':
      return 'Uploaded';
    case 'uploading':
      return 'Uploading';
    case 'processing':
      return 'Processing';
    case 'extracted':
      return 'Extracted';
    case 'failed':
      return 'Failed';
    case 'no_files':
      return 'No Files';
    case 'files_uploaded':
      return 'Files Uploaded';
    case 'ready_for_reconciliation':
      return 'Ready for Reconciliation';
    default:
      return 'Unknown';
  }
};

/**
 * Get a color class for the status
 */
export const getStatusColor = (status: UIStatus): string => {
  switch (status) {
    case 'uploaded':
      return 'text-blue-700 bg-blue-50';
    case 'uploading':
      return 'text-blue-700 bg-blue-50';
    case 'files_uploaded':
      return 'text-blue-700 bg-blue-50';
    case 'processing':
      return 'text-yellow-700 bg-yellow-50';
    case 'extracted':
      return 'text-green-700 bg-green-50';
    case 'ready_for_reconciliation':
      return 'text-green-700 bg-green-50';
    case 'failed':
      return 'text-red-700 bg-red-50';
    case 'no_files':
      return 'text-gray-700 bg-gray-50';
    default:
      return 'text-gray-700 bg-gray-50';
  }
};
