import { createBrowserClient } from '@supabase/ssr'
import { Database } from '@/types/database'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

/**
 * Create a Supabase client for client-side usage
 * Using the latest recommended pattern from Supabase
 */
export function createClient() {
  return createBrowserClient<Database>(
    supabaseUrl,
    supabaseAnonKey
  )
}

// For backward compatibility - browser client instance
export const supabase = createClient()

// Authentication helper functions using modern patterns
export const auth = {
  signUp: async (email: string, password: string, options?: { emailRedirectTo?: string }) => {
    const client = createClient()
    return await client.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: options?.emailRedirectTo || `${typeof window !== 'undefined' ? window.location.origin : ''}/auth/callback`,
      },
    })
  },

  signIn: async (email: string, password: string) => {
    const client = createClient()
    return await client.auth.signInWithPassword({
      email,
      password,
    })
  },

  signOut: async () => {
    const client = createClient()
    return await client.auth.signOut()
  },

  getUser: async () => {
    const client = createClient()
    const { data: { user }, error } = await client.auth.getUser()
    return { user, error }
  },

  getSession: async () => {
    const client = createClient()
    const { data: { session }, error } = await client.auth.getSession()
    return { session, error }
  }
}