export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: <PERSON><PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      companies: {
        Row: {
          id: string
          name: string
          slug: string
          settings: <PERSON><PERSON>
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          slug: string
          settings?: <PERSON><PERSON>
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          slug?: string
          settings?: <PERSON><PERSON>
          created_at?: string
          updated_at?: string
        }
      }
      company_users: {
        Row: {
          id: string
          company_id: string
          user_id: string
          role: string
          invited_by: string | null
          invited_at: string
          accepted_at: string | null
          created_at: string
        }
        Insert: {
          id?: string
          company_id: string
          user_id: string
          role?: string
          invited_by?: string | null
          invited_at?: string
          accepted_at?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          company_id?: string
          user_id?: string
          role?: string
          invited_by?: string | null
          invited_at?: string
          accepted_at?: string | null
          created_at?: string
        }
      }
      files: {
        Row: {
          id: string
          company_id: string
          uploaded_by: string
          filename: string
          original_filename: string
          file_size: number
          mime_type: string
          storage_path: string
          status: string
          processing_started_at: string | null
          processing_completed_at: string | null
          processing_error: string | null
          metadata: <PERSON><PERSON>
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          company_id: string
          uploaded_by: string
          filename: string
          original_filename: string
          file_size: number
          mime_type: string
          storage_path: string
          status?: string
          processing_started_at?: string | null
          processing_completed_at?: string | null
          processing_error?: string | null
          metadata?: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          company_id?: string
          uploaded_by?: string
          filename?: string
          original_filename?: string
          file_size?: number
          mime_type?: string
          storage_path?: string
          status?: string
          processing_started_at?: string | null
          processing_completed_at?: string | null
          processing_error?: string | null
          metadata?: Json
          created_at?: string
          updated_at?: string
        }
      }
      transactions: {
        Row: {
          id: string
          company_id: string
          file_id: string
          transaction_type: string
          date: string
          amount: number
          description: string | null
          reference: string | null
          account: string | null
          category: string | null
          balance: number | null
          raw_data: Json | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          company_id: string
          file_id: string
          transaction_type: string
          date: string
          amount: number
          description?: string | null
          reference?: string | null
          account?: string | null
          category?: string | null
          balance?: number | null
          raw_data?: Json | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          company_id?: string
          file_id?: string
          transaction_type?: string
          date?: string
          amount?: number
          description?: string | null
          reference?: string | null
          account?: string | null
          category?: string | null
          balance?: number | null
          raw_data?: Json | null
          created_at?: string
          updated_at?: string
        }
      }
      reconciliations: {
        Row: {
          id: string
          company_id: string
          bank_transaction_id: string | null
          ledger_transaction_id: string | null
          status: string
          match_confidence: number | null
          amount_difference: number | null
          date_difference: number | null
          notes: string | null
          reviewed_by: string | null
          reviewed_at: string | null
          created_at: string
          updated_at: string
          match_type: string | null
          matched_fields: Json | null
          created_by: string | null
        }
        Insert: {
          id?: string
          company_id: string
          bank_transaction_id?: string | null
          ledger_transaction_id?: string | null
          status?: string
          match_confidence?: number | null
          amount_difference?: number | null
          date_difference?: number | null
          notes?: string | null
          reviewed_by?: string | null
          reviewed_at?: string | null
          created_at?: string
          updated_at?: string
          match_type?: string | null
          matched_fields?: Json | null
          created_by?: string | null
        }
        Update: {
          id?: string
          company_id?: string
          bank_transaction_id?: string | null
          ledger_transaction_id?: string | null
          status?: string
          match_confidence?: number | null
          amount_difference?: number | null
          date_difference?: number | null
          notes?: string | null
          reviewed_by?: string | null
          reviewed_at?: string | null
          created_at?: string
          updated_at?: string
          match_type?: string | null
          matched_fields?: Json | null
          created_by?: string | null
        }
      }
      user_profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {}
    Functions: {
      get_reconciliation_summary: {
        Args: {
          p_company_id: string
        }
        Returns: {
          total_bank_transactions: number
          total_ledger_transactions: number
          matched_transactions: number
          unmatched_bank_transactions: number
          unmatched_ledger_transactions: number
          total_discrepancy: number
          reconciliation_status: string
        }[]
      }
      get_reconciliation_transactions: {
        Args: {
          p_company_id: string
          p_status: string | null
          p_limit: number
          p_offset: string
        }
        Returns: {
          id: string
          date: string
          description: string
          amount: number
          reference: string
          source: string
          matched: boolean
          matched_with: string | null
          confidence: number | null
          reconciliation_id: string | null
          match_type: string | null
        }[]
      }
      manual_match_transactions: {
        Args: {
          p_company_id: string
          p_bank_transaction_id: string
          p_ledger_transaction_id: string
          p_user_id: string
          p_notes: string | null
        }
        Returns: string
      }
      unmatch_transactions: {
        Args: {
          p_reconciliation_id: string
          p_company_id: string
          p_user_id: string
        }
        Returns: undefined
      }
    }
    Enums: {}
  }
}
