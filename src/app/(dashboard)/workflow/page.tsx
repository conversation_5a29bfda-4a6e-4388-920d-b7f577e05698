'use client';

import { useState, useEffect } from 'react';

// Force dynamic rendering
export const dynamic = 'force-dynamic';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Upload,
  Settings,
  Link,
  Eye,
  FileText,
  CheckCircle,
  Clock,
  AlertCircle,
  ArrowRight,
  RefreshCw
} from 'lucide-react';
import { WorkflowManager } from '@/lib/services/workflow-manager';
import { WorkflowState, WorkflowProgress, WORKFLOW_STEPS } from '@/lib/types/workflow';
import { useAuth } from '@/lib/hooks/use-auth';

const stepIcons = {
  upload: Upload,
  processing: Settings,
  matching: Link,
  review: Eye,
  reports: FileText
};

const statusColors = {
  not_started: 'bg-gray-100 text-gray-600',
  in_progress: 'bg-blue-100 text-blue-700',
  completed: 'bg-green-100 text-green-700',
  failed: 'bg-red-100 text-red-700',
  paused: 'bg-yellow-100 text-yellow-700'
};

export default function WorkflowPage() {
  const router = useRouter();
  const { user, company } = useAuth();
  const [workflowState, setWorkflowState] = useState<WorkflowState | null>(null);
  const [stepProgress, setStepProgress] = useState<Record<string, WorkflowProgress>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  const workflowManager = new WorkflowManager();

  useEffect(() => {
    if (company?.id) {
      loadWorkflowState();
    }
  }, [company?.id]);

  const loadWorkflowState = async () => {
    if (!company?.id) return;

    try {
      setLoading(true);
      setError(null);

      const state = await workflowManager.getWorkflowState(company.id);
      setWorkflowState(state);

      // Load progress for each step
      const progressData: Record<string, WorkflowProgress> = {};
      for (const step of WORKFLOW_STEPS) {
        const progress = await workflowManager.getStepProgress(company.id, step.step);
        progressData[step.step] = progress;
      }
      setStepProgress(progressData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load workflow state');
    } finally {
      setLoading(false);
    }
  };

  const handleStepAction = async (step: string) => {
    if (!company?.id) return;

    try {
      setActionLoading(step);

      switch (step) {
        case 'upload':
          router.push('/upload');
          break;
        case 'processing':
          await workflowManager.startStep(company.id, 'processing');
          break;
        case 'matching':
          await workflowManager.startStep(company.id, 'matching');
          break;
        case 'review':
          // Start the review step which will detect discrepancies if needed
          await workflowManager.startStep(company.id, 'review');
          break;
        case 'reports':
          await workflowManager.startStep(company.id, 'reports');
          break;
      }

      // Reload state after action
      await loadWorkflowState();
    } catch (err) {
      setError(err instanceof Error ? err.message : `Failed to start ${step}`);
    } finally {
      setActionLoading(null);
    }
  };

  const getStepButtonText = (step: string, progress: WorkflowProgress) => {
    if (actionLoading === step) {
      return 'Starting...';
    }

    if (progress.status === 'completed') {
      return 'Completed';
    }

    if (progress.canProceed) {
      return step === 'upload' ? 'Upload Files' :
             step === 'review' ? 'Start Review' :
             `Start ${step.charAt(0).toUpperCase() + step.slice(1)}`;
    }

    return 'Waiting...';
  };

  const getStepButtonVariant = (progress: WorkflowProgress) => {
    if (progress.status === 'completed') {
      return 'outline';
    }
    if (progress.canProceed) {
      return 'default';
    }
    return 'outline';
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  if (!workflowState) {
    return (
      <div className="container mx-auto p-6">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Unable to load workflow state. Please try again.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Reconciliation Workflow</h1>
          <p className="text-gray-600 mt-2">
            Complete your bank statement and ledger reconciliation in 5 simple steps
          </p>
        </div>
        <Button
          onClick={loadWorkflowState}
          variant="outline"
          size="sm"
          disabled={loading}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Overall Progress */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              📊
            </div>
            Overall Progress
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Workflow Progress</span>
              <span className="text-sm text-gray-600">{workflowState.progress}%</span>
            </div>
            <Progress value={workflowState.progress} className="h-2" />
            <div className="flex items-center gap-2">
              <Badge className={statusColors[workflowState.status]}>
                {workflowState.status.replace('_', ' ')}
              </Badge>
              <span className="text-sm text-gray-600">
                Current step: {workflowState.currentStep}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Workflow Steps */}
      <div className="grid gap-6">
        {WORKFLOW_STEPS.map((stepConfig, index) => {
          const progress = stepProgress[stepConfig.step];
          const Icon = stepIcons[stepConfig.step];
          const isCompleted = progress?.status === 'completed';
          const isCurrent = workflowState.currentStep === stepConfig.step;
          const isDisabled = !progress?.canProceed && !isCompleted;

          return (
            <Card key={stepConfig.step} className={`transition-all ${
              isCurrent ? 'ring-2 ring-blue-500' : ''
            }`}>
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  {/* Step Icon */}
                  <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                    isCompleted ? 'bg-green-100 text-green-700' :
                    isCurrent ? 'bg-blue-100 text-blue-700' :
                    'bg-gray-100 text-gray-600'
                  }`}>
                    {isCompleted ? (
                      <CheckCircle className="h-6 w-6" />
                    ) : (
                      <Icon className="h-6 w-6" />
                    )}
                  </div>

                  {/* Step Content */}
                  <div className="flex-1 space-y-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-lg font-semibold flex items-center gap-2">
                          {stepConfig.icon} {stepConfig.title}
                          {isCompleted && <CheckCircle className="h-5 w-5 text-green-600" />}
                        </h3>
                        <p className="text-gray-600">{stepConfig.description}</p>
                      </div>
                      <div className="text-right">
                        <Badge variant="outline" className="text-xs">
                          {stepConfig.estimatedTime}
                        </Badge>
                      </div>
                    </div>

                    {/* Progress Bar */}
                    {progress && (
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <span>{progress.message}</span>
                          <span>{progress.progress}%</span>
                        </div>
                        <Progress value={progress.progress} className="h-2" />
                      </div>
                    )}

                    {/* Action Button */}
                    <div className="flex items-center gap-2">
                      <Button
                        onClick={() => handleStepAction(stepConfig.step)}
                        variant={getStepButtonVariant(progress)}
                        disabled={isDisabled || actionLoading === stepConfig.step}
                        className="flex items-center gap-2"
                      >
                        {getStepButtonText(stepConfig.step, progress)}
                        {!isCompleted && progress?.canProceed && (
                          <ArrowRight className="h-4 w-4" />
                        )}
                      </Button>

                      {/* Clear next actions for Review stage */}
                      {stepConfig.step === 'review' && (
                        <>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => router.push('/dashboard/reconciliation')}
                          >
                            Open Reviewer
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            disabled={!progress?.canProceed}
                            onClick={() => handleStepAction('reports')}
                          >
                            Continue to Reports
                          </Button>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button
              variant="outline"
              onClick={() => router.push('/files')}
              className="flex flex-col items-center gap-2 h-auto p-4"
            >
              <Upload className="h-6 w-6" />
              <span className="text-sm">View Files</span>
            </Button>
            <Button
              variant="outline"
              onClick={() => router.push('/dashboard/reconciliation')}
              className="flex flex-col items-center gap-2 h-auto p-4"
            >
              <Link className="h-6 w-6" />
              <span className="text-sm">Reconciliation</span>
            </Button>
            <Button
              variant="outline"
              onClick={() => router.push('/reports')}
              className="flex flex-col items-center gap-2 h-auto p-4"
            >
              <FileText className="h-6 w-6" />
              <span className="text-sm">Reports</span>
            </Button>
            <Button
              variant="outline"
              onClick={() => router.push('/dashboard')}
              className="flex flex-col items-center gap-2 h-auto p-4"
            >
              <Eye className="h-6 w-6" />
              <span className="text-sm">Dashboard</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
