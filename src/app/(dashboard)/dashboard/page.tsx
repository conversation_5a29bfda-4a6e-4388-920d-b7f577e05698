'use client'

import { useState, useEffect } from 'react'

// Force dynamic rendering
export const dynamic = 'force-dynamic';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import Link from 'next/link'
import {
  FileText,
  Upload,
  CheckSquare,
  AlertTriangle,
  TrendingUp,
  Calendar,
  DollarSign,
  ArrowUpRight,
  ArrowRight,
  Clock,
  Users,
  PieChart,
  Activity,
  Zap,
  CheckCircle
} from 'lucide-react'

export default function DashboardPage() {
  const [stats, setStats] = useState({
    totalFiles: 0,
    pendingReconciliations: 0,
    totalDiscrepancies: 0,
    totalAmount: 0
  })

  // This would be replaced with actual data fetching from Supabase
  useEffect(() => {
    // Mock data for now
    setStats({
      totalFiles: 12,
      pendingReconciliations: 8,
      totalDiscrepancies: 3,
      totalAmount: 125420.50
    })
  }, [])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-4xl font-bold tracking-tight text-gray-900 mb-4">
          Bank Statement Reconciliation
        </h1>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-8">
          Upload your bank statement and ledger files. We'll handle the rest automatically.
        </p>
        <Link href="/dashboard/seamless">
          <Button size="lg" className="bg-green-600 hover:bg-green-700 text-lg px-8 py-4">
            <Zap className="mr-3 h-6 w-6" />
            Start Reconciliation Now
            <ArrowRight className="ml-3 h-6 w-6" />
          </Button>
        </Link>
      </div>

      {/* How it works */}
      <Card className="max-w-4xl mx-auto">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl text-gray-900">How It Works</CardTitle>
          <CardDescription className="text-lg text-gray-600">
            Simple 3-step process to get your reconciliation report
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Upload className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">1. Upload Files</h3>
              <p className="text-gray-600">Upload your bank statement (PDF) and ledger file (Excel/CSV)</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Zap className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">2. AI Processing</h3>
              <p className="text-gray-600">Our AI extracts and matches transactions automatically</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <FileText className="h-8 w-8 text-purple-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">3. Get Report</h3>
              <p className="text-gray-600">Download your complete reconciliation report with discrepancies</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Features */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto">
        <Card className="text-center border-green-200 bg-green-50">
          <CardContent className="pt-6">
            <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
            <h3 className="font-semibold text-green-900 mb-2">Automatic Matching</h3>
            <p className="text-sm text-green-700">AI matches transactions by reference, amount, and date</p>
          </CardContent>
        </Card>
        <Card className="text-center border-blue-200 bg-blue-50">
          <CardContent className="pt-6">
            <Zap className="h-12 w-12 text-blue-600 mx-auto mb-4" />
            <h3 className="font-semibold text-blue-900 mb-2">Fast Processing</h3>
            <p className="text-sm text-blue-700">Complete reconciliation in under 5 minutes</p>
          </CardContent>
        </Card>
        <Card className="text-center border-purple-200 bg-purple-50">
          <CardContent className="pt-6">
            <AlertTriangle className="h-12 w-12 text-purple-600 mx-auto mb-4" />
            <h3 className="font-semibold text-purple-900 mb-2">Find Discrepancies</h3>
            <p className="text-sm text-purple-700">Identify unmatched transactions and errors</p>
          </CardContent>
        </Card>
        <Card className="text-center border-orange-200 bg-orange-50">
          <CardContent className="pt-6">
            <FileText className="h-12 w-12 text-orange-600 mx-auto mb-4" />
            <h3 className="font-semibold text-orange-900 mb-2">Professional Reports</h3>
            <p className="text-sm text-orange-700">Export detailed reconciliation reports</p>
          </CardContent>
        </Card>
      </div>

      {/* CTA Section */}
      <Card className="max-w-4xl mx-auto border-green-200 bg-gradient-to-r from-green-50 to-emerald-50">
        <CardContent className="pt-8 pb-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-green-900 mb-4">
              Ready to reconcile your accounts?
            </h2>
            <p className="text-lg text-green-700 mb-8 max-w-2xl mx-auto">
              Upload your files and get a complete reconciliation report in minutes, not hours.
            </p>
            <Link href="/dashboard/seamless">
              <Button size="lg" className="bg-green-600 hover:bg-green-700 text-xl px-12 py-6">
                <Zap className="mr-3 h-8 w-8" />
                Start Now - It's Free
                <ArrowRight className="ml-3 h-8 w-8" />
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>

    </div>
  )
}