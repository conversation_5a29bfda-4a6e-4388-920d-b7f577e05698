'use client'

import { useState, useEffect, useRef } from 'react'
import { createClient } from '@/lib/supabase'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { FileUploadZone } from '@/components/upload/file-upload-zone'
import {
  CheckCircle,
  AlertTriangle,
  FileText,
  Upload,
  Play,
  BarChart3,
  Zap,
  Clock,
  RefreshCw,
  ArrowRight
} from 'lucide-react'
import Link from 'next/link'

import { Database } from '@/types/database'

interface WorkflowStep {
  id: string
  title: string
  description: string
  status: 'pending' | 'current' | 'completed' | 'error'
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>
}

// Use the database type for files
type FileRecord = Database['public']['Tables']['files']['Row']

// Define interfaces for reconciliation data
interface ReconciliationSummary {
  total_bank_transactions: number
  total_ledger_transactions: number
  total_matches: number
  match_rate: number
  unmatched_bank_transactions?: number
  unmatched_ledger_transactions?: number
  total_discrepancy?: number
}

interface ReconciliationResult {
  success: boolean
  message: string
  data: ReconciliationSummary
}

export default function SeamlessReconciliationPage() {
  const [files, setFiles] = useState<FileRecord[]>([])
  const [processing, setProcessing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [progress, setProgress] = useState(0)
  const [uploadedFilesCount, setUploadedFilesCount] = useState(0)
  const [results, setResults] = useState<ReconciliationResult | null>(null)
  const [reconciliationSummary, setReconciliationSummary] = useState<ReconciliationSummary | null>(null)
  const [currentStatus, setCurrentStatus] = useState<'upload' | 'processing' | 'completed' | 'error'>('upload')

  const isMountedRef = useRef(true)
  const supabase = createClient()


  const fetchFiles = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession()
      if (!session || !isMountedRef.current) return

      // Get company ID for the user first
      const { data: companyUser } = await supabase
        .from('company_users')
        .select('company_id')
        .eq('user_id', session.user.id)
        .single()

      if (!companyUser) {
        console.error('User not associated with any company')
        return
      }

      // Query files directly from Supabase, filtered by company_id
      const { data: filesData, error } = await supabase
        .from('files')
        .select('*')
        .eq('company_id', companyUser.company_id)
        .in('status', ['uploaded', 'completed', 'processing']) // Include relevant statuses
        .order('created_at', { ascending: false }) as {
          data: FileRecord[] | null,
          error: Error | null
        }

      if (error) {
        console.error('Error fetching files:', error)
        return
      }

      if (filesData && isMountedRef.current) {
        console.log('📁 Fetched files:', filesData.length, filesData.map((f: FileRecord) => ({ name: f.original_filename, status: f.status })))
        setFiles(filesData)
        setUploadedFilesCount(filesData.length)
      }
    } catch (error) {
      console.error('Error in fetchFiles:', error)
    }
  }

  const startSeamlessReconciliation = async () => {
    if (processing) return

    setProcessing(true)
    setProgress(0)
    setError(null)
    setCurrentStatus('processing')

    try {
      console.log('🚀 Starting seamless reconciliation...')

      // Simulate realistic progress updates
      const progressSteps = [
        { progress: 10, message: 'Initializing reconciliation process...' },
        { progress: 25, message: 'Fetching uploaded files...' },
        { progress: 40, message: 'Processing bank statements...' },
        { progress: 60, message: 'Extracting ledger transactions...' },
        { progress: 80, message: 'Matching transactions...' },
        { progress: 95, message: 'Generating reconciliation report...' },
      ]

      for (const step of progressSteps) {
        if (!isMountedRef.current) return
        setProgress(step.progress)
        await new Promise(resolve => setTimeout(resolve, 1000))
      }

      // Call the API
      const response = await fetch('/api/seamless-reconciliation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Reconciliation failed')
      }

      const result = await response.json()
      console.log('✅ Reconciliation completed:', result)

      setProgress(100)
      setReconciliationSummary(result.data)
      setCurrentStatus('completed')

      // Refresh files to show updated status
      await fetchFiles()

    } catch (error) {
      console.error('❌ Reconciliation error:', error)
      setError(error instanceof Error ? error.message : 'An unexpected error occurred')
      setCurrentStatus('error')
      setProgress(0)
    } finally {
      setProcessing(false)
    }
  }

  // Handle upload success callback
  const handleUploadSuccess = (fileId: string) => {
    console.log('📥 Upload success callback triggered for file:', fileId)
    setUploadedFilesCount((prev) => prev + 1)
    fetchFiles()
  }

  // Handle files changed callback
  const handleFilesChanged = () => {
    console.log('🗂️ Files changed callback triggered')
    fetchFiles()
  }

  useEffect(() => {
    fetchFiles()
  }, []) // Initial fetch on component mount

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false
    }
  }, [])

  // GUARANTEED BUTTON VISIBILITY: Always show button when files exist OR when uploadedFilesCount > 0
  const shouldShowButton = () => {
    const hasFiles = files.length > 0 || uploadedFilesCount > 0
    console.log('🔍 Button visibility check:', { filesLength: files.length, uploadedFilesCount, hasFiles })
    return hasFiles
  }

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-4xl font-bold tracking-tight text-gray-900 mb-4">
          Bank Statement Reconciliation
        </h1>
        <p className="text-xl text-gray-600">
          Upload your files and get your reconciliation report in minutes
        </p>
        <div className="mt-6 space-y-4">
          {/* ALWAYS VISIBLE BUTTON - GUARANTEED TO SHOW */}
          <Button
            onClick={startSeamlessReconciliation}
            disabled={processing}
            size="lg"
            className="bg-green-600 hover:bg-green-700 text-white px-16 py-6 text-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300"
          >
            {processing ? (
              <>
                <RefreshCw className="mr-3 h-8 w-8 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                <Play className="mr-3 h-8 w-8" />
                START RECONCILIATION
              </>
            )}
          </Button>

          {/* Status indicator */}
          <div className="text-center">
            <Badge variant={shouldShowButton() ? "default" : "secondary"}>
              {shouldShowButton() ? `${files.length} files ready` : "Upload files to begin"}
            </Badge>
          </div>
        </div>
      </div>

      {/* DEBUG PANEL - Shows file detection status */}
      <Card className="border-blue-200 bg-blue-50">
        <CardHeader>
          <CardTitle className="text-lg text-blue-900">🔍 Debug Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <strong>Files from DB:</strong> {files.length}
              <br />
              <strong>Upload Counter:</strong> {uploadedFilesCount}
              <br />
              <strong>Should Show Button:</strong> {shouldShowButton() ? '✅ YES' : '❌ NO'}
            </div>
            <div>
              <strong>Current Status:</strong> {currentStatus}
              <br />
              <strong>Processing:</strong> {processing ? '🔄 YES' : '⏸️ NO'}
              <br />
              <strong>Files List:</strong> {files.map(f => f.original_filename).join(', ') || 'None'}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content */}
      {currentStatus === 'upload' && (
        <Card>
          <CardHeader className="text-center">
            <CardTitle className="text-2xl">Upload Your Documents</CardTitle>
            <CardDescription className="text-lg">
              Upload both your bank statement (PDF) and ledger file (Excel/CSV)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <FileUploadZone
              onUploadSuccess={handleUploadSuccess}
              onFilesChanged={handleFilesChanged}
              onStartReconciliation={startSeamlessReconciliation}
            />

            {files.length > 0 && (
              <div className="mt-6 space-y-3">
                <h3 className="font-semibold text-gray-900">Uploaded Files ({files.length})</h3>
                {files.map((file) => (
                  <div key={file.id} className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200">
                    <div className="flex items-center space-x-3">
                      <FileText className="h-5 w-5 text-green-600" />
                      <div>
                        <p className="font-medium text-green-900">{file.original_filename}</p>
                        <p className="text-sm text-green-700 capitalize">{file.mime_type.split('/')[0]}</p>
                        <p className="text-xs text-gray-500">
                          Status: <span className="font-medium text-green-600">Ready for processing</span>
                        </p>
                      </div>
                    </div>
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  </div>
                ))}

                <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <p className="text-sm text-blue-800">
                    ✅ Ready to process! Click the button below to start reconciliation.
                  </p>
                </div>

                {/* GUARANTEED BUTTON VISIBILITY: Show button when files exist */}
                {shouldShowButton() && (
                  <div className="mt-6 text-center">
                    <Button
                      onClick={startSeamlessReconciliation}
                      disabled={processing}
                      size="lg"
                      className="bg-green-600 hover:bg-green-700 text-white px-16 py-6 text-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300 animate-pulse"
                    >
                      {processing ? (
                        <>
                          <RefreshCw className="mr-3 h-8 w-8 animate-spin" />
                          Processing...
                        </>
                      ) : (
                        <>
                          <Play className="mr-3 h-8 w-8" />
                          START RECONCILIATION NOW
                        </>
                      )}
                    </Button>
                    <p className="mt-2 text-sm text-gray-600">
                      {files.length} file(s) ready for processing
                    </p>
                  </div>
                )}

                <div className="mt-4 text-center">
                  <Button
                    onClick={fetchFiles}
                    variant="outline"
                    className="text-blue-600 border-blue-300 hover:bg-blue-50"
                  >
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Refresh Files
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Processing Status */}
      {currentStatus === 'processing' && (
        <Card className="border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50">
          <CardContent className="pt-8 pb-8">
            <div className="text-center space-y-6">
              <RefreshCw className="h-16 w-16 text-blue-600 animate-spin mx-auto" />
              <div>
                <h2 className="text-2xl font-bold text-blue-900 mb-2">Processing Your Documents</h2>
                <p className="text-blue-700 text-lg mb-6">Please wait while we analyze your files...</p>
              </div>

              <div className="max-w-md mx-auto">
                <Progress value={progress} className="h-4 mb-4" />
                <p className="text-blue-600 font-medium">{progress}% Complete</p>
              </div>

              {success && (
                <div className="bg-blue-100 border border-blue-200 rounded-lg p-4 max-w-md mx-auto">
                  <p className="text-blue-800">{success}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Completed Status */}
      {currentStatus === 'completed' && reconciliationSummary && (
        <Card className="border-green-200 bg-gradient-to-r from-green-50 to-emerald-50">
          <CardContent className="pt-8 pb-8">
            <div className="text-center space-y-6">
              <CheckCircle className="h-16 w-16 text-green-600 mx-auto" />
              <div>
                <h2 className="text-3xl font-bold text-green-900 mb-2">Reconciliation Complete!</h2>
                <p className="text-green-700 text-lg">Your bank statement has been successfully reconciled</p>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-2xl mx-auto">
                <div className="bg-white rounded-lg p-4 border border-green-200">
                  <p className="text-sm font-medium text-green-900">Bank Transactions</p>
                  <p className="text-3xl font-bold text-green-700">{reconciliationSummary.total_bank_transactions}</p>
                </div>
                <div className="bg-white rounded-lg p-4 border border-green-200">
                  <p className="text-sm font-medium text-green-900">Ledger Transactions</p>
                  <p className="text-3xl font-bold text-green-700">{reconciliationSummary.total_ledger_transactions}</p>
                </div>
                <div className="bg-white rounded-lg p-4 border border-green-200">
                  <p className="text-sm font-medium text-green-900">Matches Found</p>
                  <p className="text-3xl font-bold text-green-700">{reconciliationSummary.total_matches}</p>
                </div>
                <div className="bg-white rounded-lg p-4 border border-green-200">
                  <p className="text-sm font-medium text-green-900">Match Rate</p>
                  <p className="text-3xl font-bold text-green-700">{reconciliationSummary.match_rate?.toFixed(1)}%</p>
                </div>
              </div>

              <div className="space-y-4">
                <Link href="/dashboard/reports">
                  <Button size="lg" className="bg-green-600 hover:bg-green-700 text-white px-12 py-4 text-lg">
                    <BarChart3 className="mr-3 h-6 w-6" />
                    View Detailed Report
                    <ArrowRight className="ml-3 h-6 w-6" />
                  </Button>
                </Link>

                <div>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setCurrentStatus('upload')
                      setFiles([])
                      setReconciliationSummary(null)
                      setProgress(0)
                      setSuccess(null)
                      setError(null)
                    }}
                    className="text-green-700 border-green-300 hover:bg-green-50"
                  >
                    Start New Reconciliation
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Error Message */}
      {error && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            {error}
            <Button
              variant="outline"
              size="sm"
              className="ml-4"
              onClick={() => setError(null)}
            >
              Dismiss
            </Button>
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}
