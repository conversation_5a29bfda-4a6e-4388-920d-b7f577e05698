'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase'
import { useAuthFetch } from '@/lib/hooks/useAuthFetch'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  CheckSquare,
  AlertTriangle,
  FileText,
  DollarSign,
  TrendingUp,
  Search,
  Filter,
  Download,
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  ArrowRight,
  RefreshCw,
  Zap,
  Upload,
  Play,
  BarChart3,
  FileCheck
} from 'lucide-react'
import Link from 'next/link'

interface WorkflowStatus {
  currentStage: 'upload' | 'uploaded' | 'processing' | 'matching' | 'review' | 'failed'
  stageProgress: number
  files: any[]
  summary: {
    totalFiles: number
    uploadedFiles: number
    processingFiles: number
    completedFiles: number
    failedFiles: number
    bankTransactions: number
    ledgerTransactions: number
    matchedTransactions: number
    reviewedTransactions: number
  }
  nextSteps: Array<{
    action: string
    title: string
    description: string
    priority: 'high' | 'medium' | 'low'
    estimatedTime: string
  }>
}

interface ReconciliationSummary {
  totalBankTransactions: number
  totalLedgerTransactions: number
  matchedTransactions: number
  unmatchedBankTransactions: number
  unmatchedLedgerTransactions: number
  totalDiscrepancy: number
  reconciliationStatus: 'balanced' | 'discrepancies' | 'pending'
}

export default function ReconciliationPage() {
  const [workflowStatus, setWorkflowStatus] = useState<WorkflowStatus | null>(null)
  const [reconciliationSummary, setReconciliationSummary] = useState<ReconciliationSummary | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [processing, setProcessing] = useState(false)

  const supabase = createClient()
  const { authFetch } = useAuthFetch()

  const fetchWorkflowStatus = async () => {
    try {
      setLoading(true)
      setError(null)

      const data = await authFetch('/api/workflow-status')
      setWorkflowStatus(data)

      // If we have completed files, also fetch reconciliation data
      if (data.summary.completedFiles > 0) {
        try {
          const reconciliationData = await authFetch('/api/reconciliation')
          setReconciliationSummary(reconciliationData.summary)
        } catch (reconciliationError) {
          console.error('Error fetching reconciliation data:', reconciliationError)
          // Continue without reconciliation data
        }
      }

    } catch (err) {
      console.error('Error fetching workflow status:', err)
      setError(err instanceof Error ? err.message : 'Failed to load workflow status')
    } finally {
      setLoading(false)
    }
  }

  const startProcessing = async () => {
    if (!workflowStatus?.files || workflowStatus.files.length === 0) {
      setError('No files to process')
      return
    }

    try {
      setProcessing(true)
      setError(null)

      const fileIds = workflowStatus.files.map(f => f.id)

      const result = await authFetch('/api/process-reconciliation', {
        method: 'POST',
        body: JSON.stringify({ fileIds })
      })
      console.log('Processing started:', result)

      // Refresh workflow status
      await fetchWorkflowStatus()

    } catch (err) {
      console.error('Error starting processing:', err)
      setError(err instanceof Error ? err.message : 'Failed to start processing')
    } finally {
      setProcessing(false)
    }
  }

  useEffect(() => {
    fetchWorkflowStatus()

    // Set up polling for real-time updates during processing
    const interval = setInterval(() => {
      if (workflowStatus?.currentStage === 'processing') {
        fetchWorkflowStatus()
      }
    }, 3000) // Poll every 3 seconds during processing

    return () => clearInterval(interval)
  }, [workflowStatus?.currentStage])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ET', {
      style: 'currency',
      currency: 'ETB'
    }).format(amount)
  }

  const getStageIcon = (stage: string) => {
    switch (stage) {
      case 'upload': return Upload
      case 'uploaded': return FileCheck
      case 'processing': return RefreshCw
      case 'matching': return Zap
      case 'review': return Eye
      default: return FileText
    }
  }

  const getStageColor = (stage: string) => {
    switch (stage) {
      case 'upload': return 'text-gray-500'
      case 'uploaded': return 'text-blue-500'
      case 'processing': return 'text-yellow-500'
      case 'matching': return 'text-purple-500'
      case 'review': return 'text-green-500'
      case 'failed': return 'text-red-500'
      default: return 'text-gray-500'
    }
  }

  if (loading) {
    return (
      <div className="space-y-8">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-sm text-gray-600">Loading workflow status...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight text-gray-900">Reconciliation Workflow</h1>
            <p className="mt-2 text-gray-600">
              Complete end-to-end reconciliation from upload to reporting
            </p>
          </div>
        </div>
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            {error}
            <Button
              variant="outline"
              size="sm"
              className="ml-4"
              onClick={fetchWorkflowStatus}
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  if (!workflowStatus) {
    return (
      <div className="space-y-8">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-sm text-gray-600">No workflow data available</p>
          </div>
        </div>
      </div>
    )
  }

  const StageIcon = getStageIcon(workflowStatus.currentStage)

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900">Reconciliation Workflow</h1>
          <p className="mt-2 text-gray-600">
            Complete end-to-end reconciliation from upload to reporting
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          {workflowStatus.currentStage === 'uploaded' && (
            <Button
              onClick={startProcessing}
              disabled={processing}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {processing ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <Play className="mr-2 h-4 w-4" />
                  Start Processing
                </>
              )}
            </Button>
          )}
          <Link href="/dashboard/upload">
            <Button variant="outline">
              <Upload className="mr-2 h-4 w-4" />
              Upload Files
            </Button>
          </Link>
          <Link href="/dashboard/reports">
            <Button variant="outline">
              <BarChart3 className="mr-2 h-4 w-4" />
              View Reports
            </Button>
          </Link>
        </div>
      </div>

      {/* Current Stage Status */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-lg bg-gray-100 ${getStageColor(workflowStatus.currentStage)}`}>
              <StageIcon className="h-6 w-6" />
            </div>
            <div>
              <CardTitle className="capitalize">{workflowStatus.currentStage} Stage</CardTitle>
              <CardDescription>
                {workflowStatus.currentStage === 'upload' && 'Upload your bank statements and ledger files'}
                {workflowStatus.currentStage === 'uploaded' && 'Files uploaded successfully, ready for processing'}
                {workflowStatus.currentStage === 'processing' && 'Extracting transactions from uploaded files'}
                {workflowStatus.currentStage === 'matching' && 'Matching bank and ledger transactions'}
                {workflowStatus.currentStage === 'review' && 'Review and approve transaction matches'}
                {workflowStatus.currentStage === 'failed' && 'Processing failed, please check your files'}
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between text-sm mb-2">
                <span>Progress</span>
                <span>{workflowStatus.stageProgress}%</span>
              </div>
              <Progress value={workflowStatus.stageProgress} className="h-2" />
            </div>

            {workflowStatus.currentStage === 'processing' && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-center space-x-2">
                  <RefreshCw className="h-4 w-4 text-yellow-600 animate-spin" />
                  <span className="text-sm text-yellow-800">
                    Processing files... This may take a few minutes.
                  </span>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Workflow Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Files Uploaded</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{workflowStatus.summary.totalFiles}</div>
            <p className="text-xs text-muted-foreground">
              {workflowStatus.summary.completedFiles} completed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Bank Transactions</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{workflowStatus.summary.bankTransactions}</div>
            <p className="text-xs text-muted-foreground">
              Extracted from statements
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ledger Transactions</CardTitle>
            <FileCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{workflowStatus.summary.ledgerTransactions}</div>
            <p className="text-xs text-muted-foreground">
              Extracted from ledger
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Matched Transactions</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{workflowStatus.summary.matchedTransactions}</div>
            <p className="text-xs text-muted-foreground">
              Auto-matched pairs
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Next Steps */}
      <Card>
        <CardHeader>
          <CardTitle>Next Steps</CardTitle>
          <CardDescription>
            Recommended actions based on your current workflow stage
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {workflowStatus.nextSteps.map((step, index) => (
              <div key={index} className="flex items-start space-x-4 p-4 border rounded-lg">
                <div className={`p-2 rounded-lg ${
                  step.priority === 'high' ? 'bg-red-100 text-red-600' :
                  step.priority === 'medium' ? 'bg-yellow-100 text-yellow-600' :
                  'bg-green-100 text-green-600'
                }`}>
                  <ArrowRight className="h-4 w-4" />
                </div>
                <div className="flex-1">
                  <h3 className="font-medium">{step.title}</h3>
                  <p className="text-sm text-gray-600 mt-1">{step.description}</p>
                  <div className="flex items-center space-x-4 mt-2">
                    <Badge variant="outline" className="text-xs">
                      {step.estimatedTime}
                    </Badge>
                    <Badge variant={
                      step.priority === 'high' ? 'destructive' :
                      step.priority === 'medium' ? 'default' : 'secondary'
                    } className="text-xs">
                      {step.priority} priority
                    </Badge>
                  </div>
                </div>
                {step.action === 'start_processing' && workflowStatus.currentStage === 'uploaded' && (
                  <Button
                    onClick={startProcessing}
                    disabled={processing}
                    size="sm"
                  >
                    {processing ? (
                      <RefreshCw className="h-4 w-4 animate-spin" />
                    ) : (
                      'Start'
                    )}
                  </Button>
                )}
                {step.action === 'upload_files' && (
                  <Link href="/dashboard/upload">
                    <Button size="sm">Upload</Button>
                  </Link>
                )}
                {step.action === 'review_matches' && (
                  <Link href="/dashboard/reconciliation">
                    <Button size="sm">Review</Button>
                  </Link>
                )}
                {step.action === 'generate_report' && (
                  <Link href="/dashboard/reports">
                    <Button size="sm">Generate</Button>
                  </Link>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Reconciliation Summary (if available) */}
      {reconciliationSummary && (
        <Card>
          <CardHeader>
            <CardTitle>Reconciliation Summary</CardTitle>
            <CardDescription>
              Overview of transaction matching results
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600">
                  {reconciliationSummary.matchedTransactions}
                </div>
                <p className="text-sm text-gray-600">Matched Transactions</p>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-yellow-600">
                  {reconciliationSummary.unmatchedBankTransactions + reconciliationSummary.unmatchedLedgerTransactions}
                </div>
                <p className="text-sm text-gray-600">Unmatched Transactions</p>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600">
                  {formatCurrency(reconciliationSummary.totalDiscrepancy)}
                </div>
                <p className="text-sm text-gray-600">Total Discrepancy</p>
              </div>
            </div>

            <div className="mt-6 flex justify-center space-x-4">
              <Link href="/dashboard/reconciliation/review">
                <Button>
                  <Eye className="mr-2 h-4 w-4" />
                  Review Details
                </Button>
              </Link>
              <Link href="/dashboard/reports">
                <Button variant="outline">
                  <Download className="mr-2 h-4 w-4" />
                  Export Report
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Files Status */}
      {workflowStatus.files.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Uploaded Files</CardTitle>
            <CardDescription>
              Status of your uploaded documents
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {workflowStatus.files.map((file: any) => (
                <div key={file.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <FileText className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="font-medium">{file.original_filename}</p>
                      <p className="text-sm text-gray-600">
                        {(file.file_size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Badge variant={
                      file.status === 'completed' ? 'default' :
                      file.status === 'processing' ? 'secondary' :
                      file.status === 'failed' ? 'destructive' : 'outline'
                    }>
                      {file.status === 'completed' ? 'Processed' :
                       file.status === 'processing' ? 'Processing' :
                       file.status === 'failed' ? 'Failed' : 'Uploaded'}
                    </Badge>
                    {file.status === 'processing' && (
                      <RefreshCw className="h-4 w-4 animate-spin text-blue-600" />
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
