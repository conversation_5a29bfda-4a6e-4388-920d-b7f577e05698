'use client'

import { useState, useEffect } from 'react'
import { useAuthFetch } from '@/lib/hooks/useAuthFetch'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  CheckCircle,
  XCircle,
  AlertTriangle,
  ArrowLeft,
  Filter,
  Search,
  Download,
  RefreshCw
} from 'lucide-react'
import Link from 'next/link'
import { Input } from '@/components/ui/input'

interface Transaction {
  id: string
  date: string
  amount: number
  description: string
  reference: string
  account?: string
  balance?: number
  transaction_type: 'bank_statement' | 'ledger_entry'
}

interface Reconciliation {
  id: string
  bank_transaction_id: string
  ledger_transaction_id: string
  status: string
  match_confidence: number
  amount_difference: number
  date_difference: number
  match_type: string
  notes: string
}

interface ReconciliationWithTransactions extends Reconciliation {
  bankTransaction: Transaction
  ledgerTransaction: Transaction
}

export default function ReviewPage() {
  const [loading, setLoading] = useState(true)
  const [matchedTransactions, setMatchedTransactions] = useState<ReconciliationWithTransactions[]>([])
  const [unmatchedBankTransactions, setUnmatchedBankTransactions] = useState<Transaction[]>([])
  const [unmatchedLedgerTransactions, setUnmatchedLedgerTransactions] = useState<Transaction[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState<'all' | 'high_confidence' | 'low_confidence'>('all')

  const { authFetch } = useAuthFetch()

  const fetchReconciliationData = async () => {
    try {
      setLoading(true)

      // Fetch matched transactions with their details
      const matchedResponse = await authFetch('/api/reconciliation/matches')
      setMatchedTransactions(matchedResponse || [])

      // Fetch unmatched transactions
      const unmatchedResponse = await authFetch('/api/reconciliation/unmatched')
      setUnmatchedBankTransactions(unmatchedResponse?.bank || [])
      setUnmatchedLedgerTransactions(unmatchedResponse?.ledger || [])

    } catch (error) {
      console.error('Error fetching reconciliation data:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchReconciliationData()
  }, [])

  const filteredMatches = matchedTransactions.filter(match => {
    const searchMatches =
      match.bankTransaction.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      match.bankTransaction.reference.toLowerCase().includes(searchTerm.toLowerCase()) ||
      match.ledgerTransaction.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      match.ledgerTransaction.reference.toLowerCase().includes(searchTerm.toLowerCase())

    const confidenceMatches =
      filterType === 'all' ||
      (filterType === 'high_confidence' && match.match_confidence >= 90) ||
      (filterType === 'low_confidence' && match.match_confidence < 90)

    return searchMatches && confidenceMatches
  })

  const getConfidenceBadge = (confidence: number) => {
    if (confidence >= 95) return <Badge className="bg-green-100 text-green-800">Excellent ({confidence}%)</Badge>
    if (confidence >= 85) return <Badge className="bg-blue-100 text-blue-800">Good ({confidence}%)</Badge>
    if (confidence >= 75) return <Badge className="bg-yellow-100 text-yellow-800">Fair ({confidence}%)</Badge>
    return <Badge className="bg-red-100 text-red-800">Low ({confidence}%)</Badge>
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'ETB',
      minimumFractionDigits: 2
    }).format(Math.abs(amount))
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading reconciliation data...</span>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/dashboard/reconciliation">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Overview
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">Transaction Review</h1>
            <p className="text-muted-foreground">Review matched and unmatched transactions</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={fetchReconciliationData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Matched Transactions</p>
                <p className="text-2xl font-bold text-green-600">{matchedTransactions.length}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Unmatched Bank</p>
                <p className="text-2xl font-bold text-orange-600">{unmatchedBankTransactions.length}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Unmatched Ledger</p>
                <p className="text-2xl font-bold text-red-600">{unmatchedLedgerTransactions.length}</p>
              </div>
              <XCircle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search transactions by description or reference..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-gray-400" />
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value as any)}
                className="border rounded-md px-3 py-2"
              >
                <option value="all">All Matches</option>
                <option value="high_confidence">High Confidence (90%+)</option>
                <option value="low_confidence">Low Confidence (&lt;90%)</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabs for different views */}
      <Tabs defaultValue="matched" className="space-y-4">
        <TabsList>
          <TabsTrigger value="matched">
            Matched Transactions ({filteredMatches.length})
          </TabsTrigger>
          <TabsTrigger value="unmatched-bank">
            Unmatched Bank ({unmatchedBankTransactions.length})
          </TabsTrigger>
          <TabsTrigger value="unmatched-ledger">
            Unmatched Ledger ({unmatchedLedgerTransactions.length})
          </TabsTrigger>
        </TabsList>

        {/* Matched Transactions */}
        <TabsContent value="matched">
          <div className="space-y-4">
            {filteredMatches.length === 0 ? (
              <Card>
                <CardContent className="pt-6 text-center">
                  <p className="text-muted-foreground">No matched transactions found.</p>
                </CardContent>
              </Card>
            ) : (
              filteredMatches.map((match) => (
                <Card key={match.id}>
                  <CardContent className="pt-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-2">
                        {getConfidenceBadge(match.match_confidence)}
                        <Badge variant="outline">{match.match_type}</Badge>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-muted-foreground">
                          Amount Diff: {formatCurrency(match.amount_difference)}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          Date Diff: {match.date_difference} days
                        </p>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      {/* Bank Transaction */}
                      <div className="border rounded-lg p-4 bg-blue-50">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-semibold text-blue-900">Bank Statement</h4>
                          <Badge variant="outline" className="bg-blue-100">
                            {formatDate(match.bankTransaction.date)}
                          </Badge>
                        </div>
                        <div className="space-y-2">
                          <p className="text-sm">
                            <span className="font-medium">Amount:</span> {formatCurrency(match.bankTransaction.amount)}
                          </p>
                          <p className="text-sm">
                            <span className="font-medium">Description:</span> {match.bankTransaction.description}
                          </p>
                          {match.bankTransaction.reference && (
                            <p className="text-sm">
                              <span className="font-medium">Reference:</span> {match.bankTransaction.reference}
                            </p>
                          )}
                          <p className="text-sm">
                            <span className="font-medium">Balance:</span> {formatCurrency(match.bankTransaction.balance || 0)}
                          </p>
                        </div>
                      </div>

                      {/* Ledger Transaction */}
                      <div className="border rounded-lg p-4 bg-green-50">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-semibold text-green-900">Ledger Entry</h4>
                          <Badge variant="outline" className="bg-green-100">
                            {formatDate(match.ledgerTransaction.date)}
                          </Badge>
                        </div>
                        <div className="space-y-2">
                          <p className="text-sm">
                            <span className="font-medium">Amount:</span> {formatCurrency(match.ledgerTransaction.amount)}
                          </p>
                          <p className="text-sm">
                            <span className="font-medium">Description:</span> {match.ledgerTransaction.description}
                          </p>
                          {match.ledgerTransaction.reference && (
                            <p className="text-sm">
                              <span className="font-medium">Reference:</span> {match.ledgerTransaction.reference}
                            </p>
                          )}
                          {match.ledgerTransaction.account && (
                            <p className="text-sm">
                              <span className="font-medium">Account:</span> {match.ledgerTransaction.account}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>

                    {match.notes && (
                      <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                        <p className="text-sm text-gray-600">
                          <span className="font-medium">Notes:</span> {match.notes}
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabsContent>

        {/* Unmatched Bank Transactions */}
        <TabsContent value="unmatched-bank">
          <div className="space-y-4">
            {unmatchedBankTransactions.length === 0 ? (
              <Card>
                <CardContent className="pt-6 text-center">
                  <p className="text-muted-foreground">No unmatched bank transactions found.</p>
                </CardContent>
              </Card>
            ) : (
              unmatchedBankTransactions.map((transaction) => (
                <Card key={transaction.id}>
                  <CardContent className="pt-6">
                    <div className="flex items-start justify-between">
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <Badge className="bg-orange-100 text-orange-800">Unmatched Bank</Badge>
                          <Badge variant="outline">{formatDate(transaction.date)}</Badge>
                        </div>
                        <h4 className="font-semibold">{transaction.description}</h4>
                        <div className="space-y-1 text-sm text-muted-foreground">
                          <p><span className="font-medium">Amount:</span> {formatCurrency(transaction.amount)}</p>
                          {transaction.reference && (
                            <p><span className="font-medium">Reference:</span> {transaction.reference}</p>
                          )}
                          <p><span className="font-medium">Balance:</span> {formatCurrency(transaction.balance || 0)}</p>
                        </div>
                      </div>
                      <Button variant="outline" size="sm">
                        Manual Match
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabsContent>

        {/* Unmatched Ledger Transactions */}
        <TabsContent value="unmatched-ledger">
          <div className="space-y-4">
            {unmatchedLedgerTransactions.length === 0 ? (
              <Card>
                <CardContent className="pt-6 text-center">
                  <p className="text-muted-foreground">No unmatched ledger transactions found.</p>
                </CardContent>
              </Card>
            ) : (
              unmatchedLedgerTransactions.map((transaction) => (
                <Card key={transaction.id}>
                  <CardContent className="pt-6">
                    <div className="flex items-start justify-between">
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <Badge className="bg-red-100 text-red-800">Unmatched Ledger</Badge>
                          <Badge variant="outline">{formatDate(transaction.date)}</Badge>
                        </div>
                        <h4 className="font-semibold">{transaction.description}</h4>
                        <div className="space-y-1 text-sm text-muted-foreground">
                          <p><span className="font-medium">Amount:</span> {formatCurrency(transaction.amount)}</p>
                          {transaction.reference && (
                            <p><span className="font-medium">Reference:</span> {transaction.reference}</p>
                          )}
                          {transaction.account && (
                            <p><span className="font-medium">Account:</span> {transaction.account}</p>
                          )}
                        </div>
                      </div>
                      <Button variant="outline" size="sm">
                        Manual Match
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}