'use client'

import { useState, useEffect } from 'react'

// Force dynamic rendering
export const dynamic = 'force-dynamic';
import { createClient } from '@/lib/supabase'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { WorkflowProgress, RECONCILIATION_STEPS } from '@/components/ui/workflow-progress'
import { NextStepsCard, getNextSteps } from '@/components/ui/next-steps-card'
import { UIStatus } from '@/lib/utils/status-mapping'
import {
  FileText,
  Upload,
  Download,
  Trash2,
  Eye,
  Search,
  Filter,
  MoreHorizontal,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  RefreshCw,
  FileSpreadsheet,
  FileImage,
  ArrowRight,
  Zap
} from 'lucide-react'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import Link from 'next/link'

interface FileData {
  id: string
  name: string
  type: 'bank_statement' | 'ledger' | 'other'
  format: 'pdf' | 'xlsx' | 'csv' | 'xls'
  size: number
  uploadedAt: string
  status: 'uploaded' | 'processing' | 'extracted' | 'failed'
  processedAt?: string
  transactionCount?: number
  downloadUrl?: string
  errorMessage?: string
}

interface FileStats {
  totalFiles: number
  processedFiles: number
  totalSize: number
  lastUpload: string
}

export default function FilesPage() {
  const [files, setFiles] = useState<FileData[]>([])
  const [stats, setStats] = useState<FileStats>({
    totalFiles: 0,
    processedFiles: 0,
    totalSize: 0,
    lastUpload: ''
  })

  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState<'all' | 'bank_statement' | 'ledger'>('all')
  const [filterStatus, setFilterStatus] = useState<'all' | 'uploaded' | 'processing' | 'extracted' | 'failed'>('all')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const supabase = createClient()

  const fetchFiles = async () => {
    try {
      setLoading(true)
      setError(null)

      // Get the current session
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) {
        setError('Not authenticated')
        return
      }

      // Fetch files from our API
      const response = await fetch('/api/files', {
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
        },
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to fetch files')
      }

      const data = await response.json()
      setFiles(data.files)
      setStats(data.stats)
    } catch (err) {
      console.error('Error fetching files:', err)
      setError(err instanceof Error ? err.message : 'Failed to load files')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchFiles()
  }, [])

  const formatFileSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    if (bytes === 0) return '0 Bytes'
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getFileIcon = (format: string) => {
    switch (format) {
      case 'pdf': return <FileText className="h-5 w-5 text-red-600" />
      case 'xlsx':
      case 'xls': return <FileSpreadsheet className="h-5 w-5 text-green-600" />
      case 'csv': return <FileSpreadsheet className="h-5 w-5 text-blue-600" />
      default: return <FileText className="h-5 w-5 text-gray-600" />
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'uploaded': return <Clock className="h-4 w-4 text-gray-600" />
      case 'processing': return <RefreshCw className="h-4 w-4 text-blue-600 animate-spin" />
      case 'extracted': return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'failed': return <XCircle className="h-4 w-4 text-red-600" />
      default: return null
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'uploaded': return 'bg-gray-100 text-gray-800'
      case 'processing': return 'bg-blue-100 text-blue-800'
      case 'extracted': return 'bg-green-100 text-green-800'
      case 'failed': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'bank_statement': return 'bg-blue-100 text-blue-800'
      case 'ledger': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const filteredFiles = files.filter(file => {
    const matchesSearch = file.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = filterType === 'all' || file.type === filterType
    const matchesStatus = filterStatus === 'all' || file.status === filterStatus
    return matchesSearch && matchesType && matchesStatus
  })

  const handleDeleteFile = async (fileId: string) => {
    // Mock delete - replace with actual API call
    setFiles(prev => prev.filter(f => f.id !== fileId))
  }

  const handleReprocessFile = async (fileId: string) => {
    // Mock reprocess - replace with actual API call
    setFiles(prev => prev.map(f =>
      f.id === fileId ? { ...f, status: 'processing' as const } : f
    ))
  }

  if (loading) {
    return (
      <div className="space-y-8">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-sm text-gray-600">Loading files...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight text-gray-900">File Management</h1>
            <p className="mt-2 text-gray-600">
              Manage your uploaded documents and track processing status
            </p>
          </div>
        </div>
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            {error}
            <Button
              variant="outline"
              size="sm"
              className="ml-4"
              onClick={fetchFiles}
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900">File Management</h1>
          <p className="mt-2 text-gray-600">
            Manage your uploaded documents and track processing status
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <Link href="/dashboard/upload">
            <Button>
              <Upload className="mr-2 h-4 w-4" />
              Upload Files
            </Button>
          </Link>
        </div>
      </div>

      {/* Workflow Progress */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Zap className="mr-2 h-5 w-5 text-blue-600" />
                Reconciliation Progress
              </CardTitle>
              <CardDescription>
                Track your progress through the bank reconciliation workflow
              </CardDescription>
            </CardHeader>
            <CardContent>
              <WorkflowProgress
                steps={RECONCILIATION_STEPS.map((step, index) => ({
                  ...step,
                  status: index === 0 ? 'completed' : index === 1 ? 'current' : 'pending'
                }))}
              />
            </CardContent>
          </Card>
        </div>

        <div>
          <NextStepsCard
            title="Next Steps"
            description="Continue your reconciliation process"
            currentStatus={files.length > 0 ? 'files_uploaded' : 'no_files' as UIStatus}
            steps={getNextSteps(files.length > 0 ? 'files_uploaded' : 'no_files' as UIStatus)}
          />
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Files</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalFiles}</div>
            <p className="text-xs text-muted-foreground">
              {stats.processedFiles} processed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Processing Rate</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round((stats.processedFiles / stats.totalFiles) * 100)}%
            </div>
            <Progress value={(stats.processedFiles / stats.totalFiles) * 100} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Size</CardTitle>
            <FileImage className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatFileSize(stats.totalSize)}</div>
            <p className="text-xs text-muted-foreground">
              Storage used
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Last Upload</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Today</div>
            <p className="text-xs text-muted-foreground">
              {formatDate(stats.lastUpload)}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle>Uploaded Files</CardTitle>
          <CardDescription>
            View and manage all your uploaded documents
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search files..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Tabs value={filterType} onValueChange={(value) => setFilterType(value as any)}>
              <TabsList>
                <TabsTrigger value="all">All Types</TabsTrigger>
                <TabsTrigger value="bank_statement">Bank Statements</TabsTrigger>
                <TabsTrigger value="ledger">Ledgers</TabsTrigger>
              </TabsList>
            </Tabs>
            <Tabs value={filterStatus} onValueChange={(value) => setFilterStatus(value as any)}>
              <TabsList>
                <TabsTrigger value="all">All Status</TabsTrigger>
                <TabsTrigger value="extracted">Extracted</TabsTrigger>
                <TabsTrigger value="processing">Processing</TabsTrigger>
                <TabsTrigger value="failed">Failed</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          <div className="space-y-4">
            {filteredFiles.map((file) => (
              <div key={file.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    {getFileIcon(file.format)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <p className="font-medium text-gray-900 truncate">{file.name}</p>
                      <Badge className={getTypeColor(file.type)}>
                        {file.type.replace('_', ' ')}
                      </Badge>
                      <Badge className={getStatusColor(file.status)}>
                        {file.status}
                      </Badge>
                    </div>
                    <div className="flex items-center space-x-4 mt-1">
                      <p className="text-sm text-gray-500">{formatFileSize(file.size)}</p>
                      <p className="text-sm text-gray-500">Uploaded: {formatDate(file.uploadedAt)}</p>
                      {file.transactionCount && (
                        <p className="text-sm text-gray-500">{file.transactionCount} transactions</p>
                      )}
                    </div>
                    {file.errorMessage && (
                      <p className="text-sm text-red-600 mt-1">{file.errorMessage}</p>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(file.status)}
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Button variant="ghost" size="sm">
                      <Eye className="h-4 w-4" />
                  </Button>
                  {file.status === 'extracted' && file.downloadUrl && (
                    <Button variant="ghost" size="sm">
                      <Download className="h-4 w-4" />
                    </Button>
                  )}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      {file.status === 'failed' && (
                        <DropdownMenuItem onClick={() => handleReprocessFile(file.id)}>
                          <RefreshCw className="mr-2 h-4 w-4" />
                          Reprocess
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuItem
                        onClick={() => handleDeleteFile(file.id)}
                        className="text-red-600"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            ))}
          </div>

          {filteredFiles.length === 0 && (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No files found matching your criteria.</p>
              <p className="text-sm text-gray-400 mt-1">Try adjusting your search or filters.</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Upload Reminder */}
      {files.length === 0 && (
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="text-blue-900">Get Started</CardTitle>
            <CardDescription className="text-blue-700">
              Upload your first documents to begin reconciliation
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-blue-900 mb-4">
              Upload bank statements (PDF) and ledger files (Excel/CSV) to start the automated reconciliation process.
            </p>
            <Link href="/dashboard/upload">
              <Button>
                <Upload className="mr-2 h-4 w-4" />
                Upload Your First File
              </Button>
            </Link>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
