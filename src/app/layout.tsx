import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { QueryProvider } from '@/components/providers/query-provider'
import { AuthProvider } from '@/components/providers/auth-provider'
import { CompanyProvider } from '@/components/providers/company-provider'
import { HydrationProvider } from '@/components/providers/hydration-provider'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Accounting Discrepancy Finder',
  description: 'Automate bank statement and ledger reconciliation',
  keywords: ['accounting', 'reconciliation', 'finance', 'bank statements'],
  robots: 'noindex, nofollow', // Prevent indexing for financial app
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="icon" href="/favicon.ico" />
      </head>
      <body className={inter.className} suppressHydrationWarning>
        <HydrationProvider>
          <QueryProvider>
            <AuthProvider>
              <CompanyProvider>
                {children}
              </CompanyProvider>
            </AuthProvider>
          </QueryProvider>
        </HydrationProvider>
      </body>
    </html>
  )
}