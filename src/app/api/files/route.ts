import { createClient } from '@supabase/supabase-js'
import { NextRequest, NextResponse } from 'next/server'

// Create Supabase client with service role for admin operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

export async function GET(request: NextRequest) {
  try {
    // Get the authenticated user from the request
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json({ error: 'No authorization header' }, { status: 401 })
    }

    // Verify the user's JWT token
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token)

    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    // Get user's companies
    const { data: userCompanies } = await supabaseAdmin
      .from('company_users')
      .select('company_id')
      .eq('user_id', user.id)

    if (!userCompanies || userCompanies.length === 0) {
      return NextResponse.json({ files: [], stats: { totalFiles: 0, processedFiles: 0, totalSize: 0, lastUpload: null } })
    }

    const companyIds = userCompanies.map(cu => cu.company_id)

    // Get files for user's companies
    const { data: files, error: filesError } = await supabaseAdmin
      .from('files')
      .select(`
        id,
        filename,
        original_filename,
        file_size,
        mime_type,
        status,
        created_at,
        updated_at,
        processing_completed_at,
        processing_error,
        metadata
      `)
      .in('company_id', companyIds)
      .order('created_at', { ascending: false })

    if (filesError) {
      console.error('Files query error:', filesError)
      return NextResponse.json({ error: 'Failed to fetch files' }, { status: 500 })
    }

    // Calculate stats
    const totalFiles = files.length
    const processedFiles = files.filter(f => f.status === 'completed').length
    const totalSize = files.reduce((sum, f) => sum + (f.file_size || 0), 0)
    const lastUpload = files.length > 0 ? files[0].created_at : null

    // Transform files for frontend
    const transformedFiles = files.map(file => ({
      id: file.id,
      name: file.original_filename,
      type: file.metadata?.file_type || 'other',
      format: file.original_filename.split('.').pop()?.toLowerCase() || 'unknown',
      size: file.file_size,
      uploadedAt: file.created_at,
      status: file.status === 'completed' ? 'processed' : 
              file.status === 'processing' ? 'processing' :
              file.status === 'failed' ? 'failed' : 'uploaded',
      processedAt: file.processing_completed_at,
      errorMessage: file.processing_error,
      // Add transaction count when we implement transaction parsing
      transactionCount: 0
    }))

    return NextResponse.json({
      files: transformedFiles,
      stats: {
        totalFiles,
        processedFiles,
        totalSize,
        lastUpload
      }
    })

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
