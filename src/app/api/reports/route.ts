import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'
import ReportGenerator from '@/lib/services/report-generator'
import TransactionMatcher from '@/lib/services/transaction-matcher'
import DiscrepancyAnalyzer from '@/lib/services/discrepancy-analyzer'

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type')
    const status = searchParams.get('status')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = parseInt(searchParams.get('offset') || '0')

    // Get company ID for the user
    const { data: companyUser } = await supabase
      .from('company_users')
      .select('company_id')
      .eq('user_id', user.id)
      .single()

    if (!companyUser) {
      return NextResponse.json(
        { error: 'User not associated with any company' },
        { status: 403 }
      )
    }

    // Initialize report generator
    const reportGenerator = new ReportGenerator()

    // Get reports with filters
    const { reports, total } = await reportGenerator.getReports(
      (companyUser as any).company_id,
      {
        type: type as any,
        status: status as any,
        limit,
        offset
      }
    )

    // Get summary statistics
    const { data: statsData } = await (supabase as any)
      .from('reports')
      .select('type, status')
      .eq('company_id', (companyUser as any).company_id)

    const stats = {
      totalReports: statsData?.length || 0,
      completedReports: statsData?.filter((r: any) => r.status === 'completed').length || 0,
      totalDiscrepancies: 0, // Calculate from reports data
      totalAmount: 0, // Calculate from reports data
      lastGenerated: reports.length > 0 ? reports[0].generated_at : new Date().toISOString()
    }

    return NextResponse.json({
      reports,
      stats,
      pagination: {
        limit,
        offset,
        total
      }
    })

  } catch (error) {
    console.error('Reports API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      reportType,
      reportName,
      dateFrom,
      dateTo,
      bankFileId,
      ledgerFileId,
      includeJournalVouchers = true
    } = body

    if (!reportType) {
      return NextResponse.json(
        { error: 'Report type is required' },
        { status: 400 }
      )
    }

    // Get company ID for the user
    const { data: companyUser } = await supabase
      .from('company_users')
      .select('company_id')
      .eq('user_id', user.id)
      .single()

    if (!companyUser) {
      return NextResponse.json(
        { error: 'User not associated with any company' },
        { status: 403 }
      )
    }

    // Initialize services
    const reportGenerator = new ReportGenerator()
    const matcher = new TransactionMatcher()
    const discrepancyAnalyzer = new DiscrepancyAnalyzer()

    let report

    switch (reportType) {
      case 'reconciliation':
        // Get transactions for matching
        const { bankTransactions, ledgerTransactions } = await matcher.getTransactionsForMatching(
          (companyUser as any).company_id,
          bankFileId,
          ledgerFileId
        )

        if (bankTransactions.length === 0 || ledgerTransactions.length === 0) {
          return NextResponse.json(
            { error: 'No transactions found for reconciliation report' },
            { status: 400 }
          )
        }

        // Perform matching if not already done
        const matchingResult = await matcher.matchTransactions(bankTransactions, ledgerTransactions)

        // Analyze discrepancies
        const discrepancyResult = await discrepancyAnalyzer.analyzeDiscrepancies(
          (companyUser as any).company_id,
          matchingResult
        )

        // Generate journal vouchers if requested
        let journalVouchers: any[] = []
        if (includeJournalVouchers) {
          journalVouchers = await discrepancyAnalyzer.generateJournalVouchers(
            (companyUser as any).company_id,
            discrepancyResult.discrepancies
          )
        }

        // Generate reconciliation report
        report = await reportGenerator.generateReconciliationReport(
          (companyUser as any).company_id,
          matchingResult,
          discrepancyResult,
          journalVouchers,
          {
            bankStatementFileId: bankFileId,
            ledgerFileId: ledgerFileId,
            dateFrom,
            dateTo,
            reportName
          },
          user.id
        )
        break

      case 'discrepancy':
        // Get existing discrepancies
        const { discrepancies } = await discrepancyAnalyzer.getDiscrepancies(
          (companyUser as any).company_id
        )

        // Calculate discrepancy statistics
        const discrepancyStats = {
          totalDiscrepancies: discrepancies.length,
          bankOnlyCount: discrepancies.filter(d => d.type === 'bank_only').length,
          ledgerOnlyCount: discrepancies.filter(d => d.type === 'ledger_only').length,
          amountMismatchCount: discrepancies.filter(d => d.type === 'amount_mismatch').length,
          dateMismatchCount: discrepancies.filter(d => d.type === 'date_discrepancy').length,
          referenceMismatchCount: discrepancies.filter(d => d.type === 'reference_mismatch').length,
          totalAmountDifference: discrepancies.reduce((sum, d) => sum + d.amountDifference, 0),
          averageAmountDifference: 0,
          criticalDiscrepancies: discrepancies.filter(d => d.amountDifference > 1000).length,
          minorDiscrepancies: discrepancies.filter(d => d.amountDifference <= 100).length
        }

        const discrepancyAnalysisResult = {
          discrepancies,
          statistics: discrepancyStats,
          recommendations: []
        }

        report = await reportGenerator.generateDiscrepancyReport(
          (companyUser as any).company_id,
          discrepancyAnalysisResult,
          {
            dateFrom,
            dateTo,
            reportName
          },
          user.id
        )
        break

      case 'journal_voucher':
        // Get existing journal vouchers
        const { data: vouchersData } = await (supabase as any)
          .from('journal_vouchers')
          .select('*')
          .eq('company_id', (companyUser as any).company_id)

        const existingVouchers = (vouchersData || []).map((v: any) => ({
          id: v.id,
          voucherNumber: v.voucher_number,
          date: v.date,
          description: v.description,
          entries: v.entries,
          totalAmount: v.total_amount,
          status: v.status,
          discrepancyId: v.discrepancy_id
        }))

        report = await reportGenerator.generateJournalVoucherReport(
          (companyUser as any).company_id,
          existingVouchers,
          {
            dateFrom,
            dateTo,
            reportName
          },
          user.id
        )
        break

      default:
        return NextResponse.json(
          { error: 'Invalid report type' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      message: 'Report generated successfully',
      report: {
        id: report.id,
        name: report.name,
        type: report.type,
        status: report.status,
        generatedAt: report.generatedAt
      }
    })

  } catch (error) {
    console.error('Report generation error:', error)
    return NextResponse.json(
      { error: 'Internal server error during report generation' },
      { status: 500 }
    )
  }
}
