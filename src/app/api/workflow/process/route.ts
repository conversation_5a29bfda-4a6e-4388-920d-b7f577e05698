import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase/server';
import { WorkflowManager } from '@/lib/services/workflow-manager';
import { EnhancedTransactionMatcher } from '@/lib/services/enhanced-transaction-matcher';
import { AIDiscrepancyAnalyzer } from '@/lib/services/ai-discrepancy-analyzer';

// Define types for database entities
type CompanyUser = { company_id: string };

export async function POST(request: NextRequest) {
  let step: string = '';

  try {
    const supabase = createServerSupabaseClient();

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const {
      step: requestStep,
      bankFileId,
      ledgerFileId,
      options = {}
    }: {
      step: string;
      bankFileId?: string;
      ledgerFileId?: string;
      options?: any;
    } = body;

    step = requestStep;

    // Get company ID for the user
    const { data: companyUser } = await supabase
      .from('company_users')
      .select('company_id')
      .eq('user_id', user.id)
      .single();

    if (!companyUser) {
      return NextResponse.json(
        { error: 'User not associated with any company' },
        { status: 403 }
      );
    }

    const companyId = (companyUser as CompanyUser).company_id;

    console.log(`Processing workflow step: ${step} for company: ${companyId}`);

    let result: any = {};

    switch (step) {
      case 'processing':
        result = await processDocuments(companyId, supabase);
        break;

      case 'matching':
        result = await processMatching(companyId, bankFileId, ledgerFileId, supabase);
        break;

      case 'review':
        result = await processReview(companyId, options, supabase);
        break;

      case 'reports':
        result = await processReports(companyId, options, supabase);
        break;

      default:
        return NextResponse.json(
          { error: `Unknown workflow step: ${step}` },
          { status: 400 }
        );
    }

    // Update workflow state
    const workflowManager = new WorkflowManager();
    const workflowState = await workflowManager.getWorkflowState(companyId);

    await supabase
      .from('workflow_states')
      .upsert({
        company_id: companyId,
        current_step: workflowState.currentStep,
        status: workflowState.status,
        completed_steps: workflowState.completedSteps,
        progress: workflowState.progress,
        started_at: workflowState.startedAt?.toISOString(),
        completed_at: workflowState.completedAt?.toISOString(),
        error_message: workflowState.error,
        metadata: {
          last_step: step,
          result: result
        }
      } as any, {
        onConflict: 'company_id'
      });

    // Log the workflow operation
    await supabase.rpc('log_audit_event', {
      p_company_id: companyId,
      p_action: `workflow_${step}`,
      p_resource_type: 'workflow',
      p_new_values: {
        step,
        result,
        workflow_state: workflowState
      }
    } as any);

    return NextResponse.json({
      success: true,
      message: `Workflow step '${step}' completed successfully`,
      data: {
        step,
        result,
        workflowState,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error(`Workflow step error:`, error);

    // Log the error
    try {
      const supabase = createServerSupabaseClient();
      const { data: { user } } = await supabase.auth.getUser();

      if (user) {
        const { data: companyUser } = await supabase
          .from('company_users')
          .select('company_id')
          .eq('user_id', user.id)
          .single();

        if (companyUser) {
          await supabase
            .from('workflow_states')
            .upsert({
              company_id: (companyUser as CompanyUser).company_id,
              status: 'failed',
              error_message: error instanceof Error ? error.message : 'Unknown error',
              metadata: {
                error: error instanceof Error ? error.stack : String(error),
                failed_step: step
              }
            } as any, {
              onConflict: 'company_id'
            });
        }
      }
    } catch (logError) {
      console.error('Failed to log workflow error:', logError);
    }

    return NextResponse.json(
      {
        error: `Internal server error during workflow step: ${step}`,
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * Process documents (file extraction)
 */
async function processDocuments(companyId: string, supabase: any) {
  console.log('Processing documents for company:', companyId);

  // Update file statuses to processing
  const { data: files } = await supabase
    .from('files')
    .select('id, status')
    .eq('company_id', companyId)
    .eq('status', 'uploaded');

  if (!files || files.length === 0) {
    throw new Error('No uploaded files found for processing');
  }

  // Update files to processing status
  const { error: updateError } = await supabase
    .from('files')
    .update({
      status: 'processing',
      processing_started_at: new Date().toISOString()
    })
    .eq('company_id', companyId)
    .eq('status', 'uploaded');

  if (updateError) {
    throw new Error(`Failed to update file status: ${updateError.message}`);
  }

  // Simulate processing time (in real implementation, this would trigger E2B processing)
  await new Promise(resolve => setTimeout(resolve, 2000));

  // Update files to extracted status (simulated)
  const { error: completeError } = await supabase
    .from('files')
    .update({
      status: 'extracted',
      processing_completed_at: new Date().toISOString()
    })
    .eq('company_id', companyId)
    .eq('status', 'processing');

  if (completeError) {
    throw new Error(`Failed to complete file processing: ${completeError.message}`);
  }

  return {
    processedFiles: files.length,
    status: 'completed',
    message: `Successfully processed ${files.length} files`
  };
}

/**
 * Process transaction matching
 */
async function processMatching(companyId: string, bankFileId: string | undefined, ledgerFileId: string | undefined, supabase: any) {
  console.log('Processing transaction matching for company:', companyId);

  const matcher = new EnhancedTransactionMatcher();

  const matchingResult = await matcher.matchTransactions(
    companyId,
    bankFileId,
    ledgerFileId
  );

  await matcher.saveEnhancedMatchingResults(
    companyId,
    matchingResult,
    'system' // System user for automated matching
  );

  return {
    statistics: matchingResult.statistics,
    validation: matchingResult.validation,
    matches: {
      exact: matchingResult.exactMatches.length,
      finReference: matchingResult.finReferenceMatches.length,
      checkNumber: matchingResult.checkNumberMatches.length,
      fuzzy: matchingResult.fuzzyMatches.length
    },
    unmatched: {
      bank: matchingResult.unmatchedBankTransactions.length,
      ledger: matchingResult.unmatchedLedgerTransactions.length
    },
    status: 'completed',
    message: `Enhanced matching completed: ${matchingResult.statistics.matchPercentage}% match rate`
  };
}

/**
 * Process review (AI analysis)
 */
async function processReview(companyId: string, options: any, supabase: any) {
  console.log('Processing AI review for company:', companyId);

  const analyzer = new AIDiscrepancyAnalyzer();

  const analyses = await analyzer.analyzeDiscrepancies(companyId);

  let journalVouchers = [];
  if (options.generateJournalVouchers) {
    journalVouchers = await analyzer.generateJournalVoucherSuggestions(companyId);
  }

  return {
    analysesCount: analyses.length,
    journalVouchersCount: journalVouchers.length,
    status: 'completed',
    message: `AI analysis completed: ${analyses.length} discrepancies analyzed`
  };
}

/**
 * Process reports generation
 */
async function processReports(companyId: string, options: any, supabase: any) {
  console.log('Processing reports generation for company:', companyId);

  // Get reconciliation statistics
  const { data: reconciliations } = await supabase
    .from('reconciliations')
    .select('status, match_type, match_confidence')
    .eq('company_id', companyId);

  // Get discrepancy statistics
  const { data: discrepancies } = await supabase
    .from('discrepancies')
    .select('status, severity, discrepancy_type')
    .eq('company_id', companyId);

  // Create report entry
  const { data: report, error: reportError } = await supabase
    .from('reports')
    .insert({
      company_id: companyId,
      report_type: 'reconciliation_summary',
      status: 'completed',
      metadata: {
        reconciliations: reconciliations?.length || 0,
        discrepancies: discrepancies?.length || 0,
        generated_at: new Date().toISOString(),
        options
      }
    })
    .select()
    .single();

  if (reportError) {
    throw new Error(`Failed to generate report: ${reportError.message}`);
  }

  return {
    reportId: report.id,
    reconciliationsCount: reconciliations?.length || 0,
    discrepanciesCount: discrepancies?.length || 0,
    status: 'completed',
    message: 'Reconciliation report generated successfully'
  };
}

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient();

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const companyId = searchParams.get('companyId');

    // Get company ID for the user if not provided
    let targetCompanyId = companyId;
    if (!targetCompanyId) {
      const { data: companyUser } = await supabase
        .from('company_users')
        .select('company_id')
        .eq('user_id', user.id)
        .single();

      if (!companyUser) {
        return NextResponse.json(
          { error: 'User not associated with any company' },
          { status: 403 }
        );
      }
      targetCompanyId = (companyUser as CompanyUser).company_id;
    }

    // Get workflow state
    const { data: workflowState } = await supabase
      .from('workflow_states')
      .select('*')
      .eq('company_id', targetCompanyId)
      .single();

    // Get workflow step results
    const { data: stepResults } = await supabase
      .from('workflow_step_results')
      .select('*')
      .eq('company_id', targetCompanyId)
      .order('created_at', { ascending: true });

    return NextResponse.json({
      workflowState,
      stepResults: stepResults || [],
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error fetching workflow state:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
