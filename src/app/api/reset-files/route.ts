import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function POST(request: NextRequest) {
  try {
    // Reset all failed files back to uploaded status
    const { data, error } = await supabase
      .from('files')
      .update({
        status: 'uploaded',
        processing_error: null,
        processing_started_at: null,
        processing_completed_at: null
      })
      .eq('status', 'failed')
      .select()

    if (error) {
      console.error('Error resetting files:', error)
      return NextResponse.json({ error: 'Failed to reset files' }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      message: `Reset ${data?.length || 0} files back to uploaded status`,
      resetFiles: data
    })

  } catch (error) {
    console.error('Error in reset-files API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
