import { NextRequest, NextResponse } from 'next/server'
import { SimpleDocumentProcessor } from '@/lib/services/simple-document-processor'
import { createClient } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient()

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { fileId, documentType } = body

    if (!fileId || !documentType) {
      return NextResponse.json(
        { error: 'File ID and document type are required' },
        { status: 400 }
      )
    }

    if (!['bank_statement', 'ledger'].includes(documentType)) {
      return NextResponse.json(
        { error: 'Invalid document type. Must be bank_statement or ledger' },
        { status: 400 }
      )
    }

    // Get company ID for the user
    const { data: companyUser, error: companyError } = await supabase
      .from('company_users')
      .select('company_id')
      .eq('user_id', user.id)
      .single()

    if (companyError || !companyUser) {
      return NextResponse.json(
        { error: 'User not associated with any company' },
        { status: 403 }
      )
    }

    // Get file record
    const { data: fileRecord, error: fileError } = await supabase
      .from('files')
      .select('*')
      .eq('id', fileId)
      .eq('company_id', (companyUser as { company_id: string }).company_id)
      .single()

    if (fileError || !fileRecord) {
      return NextResponse.json(
        { error: 'File not found' },
        { status: 404 }
      )
    }

    const typedFileRecord = fileRecord as any

    // Check if file is ready for processing
    if (!['uploaded', 'uploading'].includes(typedFileRecord.status)) {
      return NextResponse.json(
        { error: `File is not ready for processing. Current status: ${typedFileRecord.status}` },
        { status: 400 }
      )
    }

    // Initialize document processor
    const processor = new SimpleDocumentProcessor()

    // Determine file type from filename
    const fileName = typedFileRecord.original_filename
    let fileType: 'pdf' | 'xlsx' | 'xls' | 'csv'

    if (fileName.endsWith('.pdf')) {
      fileType = 'pdf'
    } else if (fileName.endsWith('.xlsx')) {
      fileType = 'xlsx'
    } else if (fileName.endsWith('.xls')) {
      fileType = 'xls'
    } else if (fileName.endsWith('.csv')) {
      fileType = 'csv'
    } else {
      return NextResponse.json(
        { error: 'Unsupported file type' },
        { status: 400 }
      )
    }

    // Process the document
    const result = await processor.processDocument({
      fileId: typedFileRecord.id,
      fileName: fileName,
      fileType,
      documentType: documentType as 'bank_statement' | 'ledger',
      companyId: (companyUser as { company_id: string }).company_id
    })

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: 'File processed successfully',
        data: {
          fileId: result.fileId,
          transactionCount: result.transactions?.length || 0,
          processingTime: result.processingTime,
          aiModel: result.aiModel
        }
      })
    } else {
      return NextResponse.json({
        success: false,
        error: result.error || 'Processing failed',
        fileId: result.fileId
      }, { status: 500 })
    }

  } catch (error) {
    console.error('Processing error:', error)
    return NextResponse.json(
      { error: 'Internal server error during processing' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const supabase = createClient()

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const fileId = searchParams.get('fileId')

    if (!fileId) {
      return NextResponse.json(
        { error: 'File ID required' },
        { status: 400 }
      )
    }

    // Get company ID for the user
    const { data: companyUser } = await supabase
      .from('company_users')
      .select('company_id')
      .eq('user_id', user.id)
      .single()

    if (!companyUser) {
      return NextResponse.json(
        { error: 'User not associated with any company' },
        { status: 403 }
      )
    }

    // Initialize processor to get status
    const processor = new SimpleDocumentProcessor()
    const status = await processor.getProcessingStatus(fileId)

    // Get file details
    const { data: fileRecord, error } = await supabase
      .from('files')
      .select('*')
      .eq('id', fileId)
      .eq('company_id', (companyUser as { company_id: string }).company_id)
      .single()

    if (error || !fileRecord) {
      return NextResponse.json(
        { error: 'File not found' },
        { status: 404 }
      )
    }

    const typedFileRecord2 = fileRecord as any

    return NextResponse.json({
      fileId: typedFileRecord2.id,
      fileName: typedFileRecord2.original_filename,
      status: status.status,
      error: status.error,
      transactionCount: status.transactionCount,
      createdAt: typedFileRecord2.created_at,
      updatedAt: typedFileRecord2.updated_at,
      processingStartedAt: typedFileRecord2.processing_started_at,
      processingCompletedAt: typedFileRecord2.processing_completed_at
    })

  } catch (error) {
    console.error('Status check error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
