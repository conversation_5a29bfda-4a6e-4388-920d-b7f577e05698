import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'
import { Sandbox } from '@e2b/code-interpreter'
import { Database } from '@/types/database'
import { PostgrestFilterBuilder } from '@supabase/postgrest-js'
import { typedUpdate, typedInsert } from '@/lib/supabase/helpers'

// Define types for database entities
type CompanyUser = { company_id: string }

interface FileType {
  id: string
  company_id: string
  mime_type: string
  status: string
}

// Define types for Supabase tables
type FileUpdate = Database['public']['Tables']['files']['Update']
type TransactionInsert = Database['public']['Tables']['transactions']['Insert']
type ReconciliationInsert = Database['public']['Tables']['reconciliations']['Insert']

// Define Transaction interface to match Json type
interface Transaction {
  id?: string
  date: string
  description: string
  reference: string
  amount: number
  debit?: number
  credit?: number
  balance?: number
  account_id?: string
  account?: string
  category?: string
  account_description?: string
  journal?: string
  [key: string]: string | number | boolean | null | undefined | Record<string, any>
}

interface MatchResult {
  bankTransaction: Transaction
  ledgerTransaction: Transaction
  matchType: 'reference' | 'date_amount' | 'fuzzy'
  confidence: number
  amountDifference: number
  dateDifference: number
}

export async function POST(request: NextRequest) {
  try {
    // Create server-side Supabase client with the latest implementation
    const supabase = createServerSupabaseClient()

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { fileIds } = body

    if (!fileIds || !Array.isArray(fileIds) || fileIds.length === 0) {
      return NextResponse.json(
        { error: 'File IDs are required' },
        { status: 400 }
      )
    }

    // Get company ID for the user
    const { data: companyUser, error: companyError } = await supabase
      .from('company_users')
      .select('company_id')
      .eq('user_id', user.id)
      .single() as { data: { company_id: string } | null, error: any }

    if (companyError || !companyUser) {
      return NextResponse.json(
        { error: 'User not associated with any company' },
        { status: 403 }
      )
    }

    // Get files by IDs
    const { data: files, error: filesError } = await supabase
      .from('files')
      .select('*')
      .in('id', fileIds) as { data: FileType[] | null, error: any }

    if (filesError || !files || files.length === 0) {
      return NextResponse.json(
        { error: 'Files not found or access denied' },
        { status: 404 }
      )
    }

    // Update files status to processing
    const updateData: Database['public']['Tables']['files']['Update'] = {
      status: 'processing',
      processing_started_at: new Date().toISOString()
    }

    await typedUpdate(supabase, 'files', updateData, 'id', fileIds)

    try {
      // Create E2B sandbox for secure processing
      const sandbox = await Sandbox.create()

      try {
        // Load transactions from database instead of files
        const { data: bankTransactionsData, error: bankError } = await supabase
          .from('transactions')
          .select('id, date, amount, description, reference, account, category, balance')
          .eq('transaction_type', 'bank_statement')
          .eq('company_id', companyUser.company_id) as { data: any[] | null, error: any }

        const { data: ledgerTransactionsData, error: ledgerError } = await supabase
          .from('transactions')
          .select('id, date, amount, description, reference, account, category, balance')
          .eq('transaction_type', 'ledger_entry')
          .eq('company_id', companyUser.company_id) as { data: any[] | null, error: any }

        if (bankError || ledgerError) {
          throw new Error(`Failed to fetch transactions: ${bankError?.message || ledgerError?.message}`)
        }

        if (!bankTransactionsData || !ledgerTransactionsData) {
          throw new Error('No transaction data found')
        }

        // Convert database format to Transaction interface
        const bankTransactions: Transaction[] = bankTransactionsData.map((t: any) => ({
          id: t.id || '',
          date: t.date || '',
          amount: parseFloat(t.amount?.toString() || '0'),
          description: t.description || '',
          reference: t.reference || '',
          account: t.account || '',
          category: t.category || '',
          balance: parseFloat(t.balance?.toString() || '0')
        }))

        const ledgerTransactions: Transaction[] = ledgerTransactionsData.map((t: any) => ({
          id: t.id || '',
          date: t.date || '',
          amount: parseFloat(t.amount?.toString() || '0'),
          description: t.description || '',
          reference: t.reference || '',
          account: t.account || '',
          category: t.category || '',
          balance: parseFloat(t.balance?.toString() || '0')
        }))

        console.log(`Processing ${bankTransactions.length} bank transactions and ${ledgerTransactions.length} ledger transactions`)

        // Perform reconciliation using E2B sandbox
        const reconciliationScript = `
import json
import pandas as pd
from datetime import datetime, timedelta
from difflib import SequenceMatcher
import re

def normalize_amount(amount):
    """Normalize amount to handle different formats"""
    if isinstance(amount, str):
        # Remove currency symbols and spaces
        amount = re.sub(r'[^0-9.-]', '', amount)
        try:
            return float(amount)
        except:
            return 0.0
    return float(amount) if amount else 0.0

def normalize_date(date_str):
    """Normalize date string to datetime"""
    if isinstance(date_str, str):
        try:
            return datetime.strptime(date_str, '%Y-%m-%d')
        except:
            try:
                return datetime.strptime(date_str, '%m/%d/%Y')
            except:
                try:
                    return datetime.strptime(date_str, '%d/%m/%Y')
                except:
                    return None
    return date_str

def similarity(a, b):
    """Calculate similarity between two strings"""
    if not a or not b:
        return 0.0
    return SequenceMatcher(None, str(a).lower(), str(b).lower()).ratio()

def match_by_reference(bank_txns, ledger_txns):
    """Match transactions by reference number"""
    matches = []
    used_ledger = set()

    for bank_txn in bank_txns:
        if not bank_txn.get('reference'):
            continue

        bank_ref = str(bank_txn['reference']).strip().upper()
        if not bank_ref:
            continue

        for i, ledger_txn in enumerate(ledger_txns):
            if i in used_ledger:
                continue

            ledger_ref = str(ledger_txn.get('reference', '')).strip().upper()
            if not ledger_ref:
                continue

            # Exact reference match
            if bank_ref == ledger_ref:
                matches.append({
                    'bank_transaction': bank_txn,
                    'ledger_transaction': ledger_txn,
                    'match_type': 'reference',
                    'confidence': 0.95,
                    'amount_difference': abs(normalize_amount(bank_txn.get('amount', 0)) - normalize_amount(ledger_txn.get('amount', 0))),
                    'date_difference': 0
                })
                used_ledger.add(i)
                break

            # Partial reference match
            elif bank_ref in ledger_ref or ledger_ref in bank_ref:
                matches.append({
                    'bank_transaction': bank_txn,
                    'ledger_transaction': ledger_txn,
                    'match_type': 'reference',
                    'confidence': 0.85,
                    'amount_difference': abs(normalize_amount(bank_txn.get('amount', 0)) - normalize_amount(ledger_txn.get('amount', 0))),
                    'date_difference': 0
                })
                used_ledger.add(i)
                break

    return matches, used_ledger

def match_by_date_amount(bank_txns, ledger_txns, used_ledger):
    """Match transactions by date and amount with strict 1:1 matching"""
    matches = []
    used_bank = set()

    for i, bank_txn in enumerate(bank_txns):
        if i in used_bank:
            continue

        bank_date = normalize_date(bank_txn.get('date'))
        bank_amount = normalize_amount(bank_txn.get('amount', 0))

        if not bank_date or bank_amount == 0:
            continue

        best_match = None
        best_score = 0
        best_j = -1

        for j, ledger_txn in enumerate(ledger_txns):
            if j in used_ledger:
                continue

            ledger_date = normalize_date(ledger_txn.get('date'))
            ledger_amount = normalize_amount(ledger_txn.get('amount', 0))

            if not ledger_date or ledger_amount == 0:
                continue

            # Check date difference (within 2 days for stricter matching)
            date_diff = abs((bank_date - ledger_date).days)
            if date_diff > 2:
                continue

            # Check amount match (must be very close)
            amount_diff = abs(bank_amount - ledger_amount)
            amount_tolerance = max(abs(bank_amount) * 0.005, 0.01)  # 0.5% or 0.01 unit tolerance (much stricter)

            if amount_diff <= amount_tolerance:
                # Calculate match score - higher is better
                date_score = max(0, (3 - date_diff) / 3.0)  # 1.0 for same day, decreasing
                amount_score = max(0, 1.0 - (amount_diff / max(abs(bank_amount), 1.0)))  # 1.0 for exact match
                total_score = (date_score * 0.3) + (amount_score * 0.7)  # Weight amount matching more heavily

                if total_score > best_score and total_score > 0.8:  # Require high confidence
                    best_match = {
                        'bank_transaction': bank_txn,
                        'ledger_transaction': ledger_txn,
                        'match_type': 'date_amount',
                        'confidence': 0.85 if date_diff == 0 and amount_diff < 0.01 else 0.75,
                        'amount_difference': amount_diff,
                        'date_difference': date_diff
                    }
                    best_score = total_score
                    best_j = j

        # Only add the best match if it meets our criteria
        if best_match and best_score > 0.8:
            matches.append(best_match)
            used_ledger.add(best_j)
            used_bank.add(i)

    return matches

def match_by_fuzzy_description(bank_txns, ledger_txns, used_ledger, used_bank):
    """Fuzzy match transactions by description with strict 1:1 matching"""
    matches = []

    for i, bank_txn in enumerate(bank_txns):
        if i in used_bank:
            continue

        bank_desc = str(bank_txn.get('description', '')).strip().lower()
        bank_amount = normalize_amount(bank_txn.get('amount', 0))
        bank_date = normalize_date(bank_txn.get('date'))

        if not bank_desc or len(bank_desc) < 3:
            continue

        best_match = None
        best_similarity = 0
        best_j = -1

        for j, ledger_txn in enumerate(ledger_txns):
            if j in used_ledger:
                continue

            ledger_desc = str(ledger_txn.get('description', '')).strip().lower()
            ledger_amount = normalize_amount(ledger_txn.get('amount', 0))
            ledger_date = normalize_date(ledger_txn.get('date'))

            if not ledger_desc or len(ledger_desc) < 3:
                continue

            # Calculate description similarity
            similarity = difflib.SequenceMatcher(None, bank_desc, ledger_desc).ratio()

            if similarity < 0.7:  # Raised threshold for fuzzy matching
                continue

            # Additional validation checks
            date_diff = abs((bank_date - ledger_date).days) if bank_date and ledger_date else 999
            amount_diff = abs(bank_amount - ledger_amount)
            amount_ratio = amount_diff / max(abs(bank_amount), abs(ledger_amount), 1.0)

            # Strict criteria for fuzzy matching
            if (date_diff <= 7 and amount_ratio <= 0.5) or similarity > 0.9:  # Either close in date/amount OR very high description similarity
                combined_score = similarity * 0.7 + (1.0 - min(amount_ratio, 1.0)) * 0.2 + (1.0 - min(date_diff / 7.0, 1.0)) * 0.1

                if combined_score > best_similarity and combined_score > 0.7:
                    best_match = {
                        'bank_transaction': bank_txn,
                        'ledger_transaction': ledger_txn,
                        'match_type': 'fuzzy',
                        'confidence': min(similarity, 0.8),  # Cap fuzzy confidence at 80%
                        'amount_difference': amount_diff,
                        'date_difference': date_diff
                    }
                    best_similarity = combined_score
                    best_j = j

        # Only add the best match if it meets our strict criteria
        if best_match and best_similarity > 0.7:
            matches.append(best_match)
            used_ledger.add(best_j)
            used_bank.add(i)

    return matches

# Load transaction data
bank_transactions = ${JSON.stringify(bankTransactions)}
ledger_transactions = ${JSON.stringify(ledgerTransactions)}

print(f"Starting reconciliation with {len(bank_transactions)} bank transactions and {len(ledger_transactions)} ledger transactions")

# Perform 3-tier matching
all_matches = []
used_ledger = set()

# 1. Reference matching (highest confidence)
ref_matches, used_ledger = match_by_reference(bank_transactions, ledger_transactions)
all_matches.extend(ref_matches)
print(f"Reference matches: {len(ref_matches)}")

# 2. Date + Amount matching (medium confidence)
used_bank = set(i for i, _ in enumerate(bank_transactions) if any(m['bank_transaction'] == bank_transactions[i] for m in ref_matches))
date_amount_matches = match_by_date_amount(bank_transactions, ledger_transactions, used_ledger)
all_matches.extend(date_amount_matches)
print(f"Date+Amount matches: {len(date_amount_matches)}")

# Update used sets
for match in date_amount_matches:
    for i, txn in enumerate(bank_transactions):
        if txn == match['bank_transaction']:
            used_bank.add(i)
    for i, txn in enumerate(ledger_transactions):
        if txn == match['ledger_transaction']:
            used_ledger.add(i)

# 3. Fuzzy description matching (lower confidence)
fuzzy_matches = match_by_fuzzy_description(bank_transactions, ledger_transactions, used_ledger, used_bank)
all_matches.extend(fuzzy_matches)
print(f"Fuzzy matches: {len(fuzzy_matches)}")

# Calculate summary statistics
total_bank_amount = sum(normalize_amount(txn.get('amount', 0)) for txn in bank_transactions)
total_ledger_amount = sum(normalize_amount(txn.get('amount', 0)) for txn in ledger_transactions)
matched_bank_amount = sum(normalize_amount(match['bank_transaction'].get('amount', 0)) for match in all_matches)
matched_ledger_amount = sum(normalize_amount(match['ledger_transaction'].get('amount', 0)) for match in all_matches)

summary = {
    'total_bank_transactions': len(bank_transactions),
    'total_ledger_transactions': len(ledger_transactions),
    'total_matches': len(all_matches),
    'reference_matches': len(ref_matches),
    'date_amount_matches': len(date_amount_matches),
    'fuzzy_matches': len(fuzzy_matches),
    'unmatched_bank_transactions': len(bank_transactions) - len(set(i for i, txn in enumerate(bank_transactions) if any(m['bank_transaction'] == txn for m in all_matches))),
    'unmatched_ledger_transactions': len(ledger_transactions) - len(used_ledger) - len([m for m in date_amount_matches + fuzzy_matches]),
    'total_bank_amount': total_bank_amount,
    'total_ledger_amount': total_ledger_amount,
    'matched_bank_amount': matched_bank_amount,
    'matched_ledger_amount': matched_ledger_amount,
    'balance_difference': total_bank_amount - total_ledger_amount,
    'match_rate': (len(all_matches) / max(len(bank_transactions), len(ledger_transactions))) * 100
}

result = {
    'matches': all_matches,
    'summary': summary
}

print(f"\\nReconciliation Summary:")
print(f"Total matches: {summary['total_matches']}")
print(f"Match rate: {summary['match_rate']:.1f}%")
print(f"Balance difference: {summary['balance_difference']:,.2f} ETB")

# Return the result
result
`

        const result = await sandbox.runCode(reconciliationScript)

        if (result.error) {
          throw new Error(`Reconciliation processing failed: ${result.error}`)
        }

        // Parse the results from the Python script
        const reconciliationData = result.results as any
        console.log('Reconciliation completed:', reconciliationData.summary)

        // Store transactions in database
        const transactionInserts = []

        // Store bank transactions
        for (const txn of bankTransactions) {
          transactionInserts.push({
            company_id: companyUser.company_id,
            file_id: files.find(f => (f as FileType).mime_type === 'application/pdf')?.id || files[0].id,
            transaction_type: 'bank_statement',
            date: txn.date,
            amount: txn.amount,
            description: txn.description,
            reference: txn.reference,
            balance: txn.balance,
            raw_data: txn as unknown as Database['public']['Tables']['transactions']['Insert']['raw_data']
          })
        }

        // Store ledger transactions
        for (const txn of ledgerTransactions) {
          transactionInserts.push({
            company_id: companyUser.company_id,
            file_id: files.find(f => (f as FileType).mime_type?.includes('sheet') || (f as FileType).mime_type?.includes('excel'))?.id || files[0].id,
            transaction_type: 'ledger',
            date: txn.date,
            amount: txn.amount,
            description: txn.description,
            reference: txn.reference,
            account: txn.account_id,
            balance: txn.balance,
            raw_data: txn as unknown as Database['public']['Tables']['transactions']['Insert']['raw_data']
          })
        }

        // Clear existing transactions for this company (for demo purposes)
        await supabase
          .from('transactions')
          .delete()
          .eq('company_id', companyUser.company_id)

        // Clear existing reconciliations for this company to prevent duplicates
        await supabase
          .from('reconciliations')
          .delete()
          .eq('company_id', companyUser.company_id)

        // Insert transactions in batches
        const { error: transactionError } = await typedInsert(supabase, 'transactions', transactionInserts as unknown as TransactionInsert[])

        if (transactionError) {
          console.error('Error inserting transactions:', transactionError)
          throw new Error('Failed to store transactions')
        }

        // Store reconciliation matches
        const reconciliationInserts = [];

        for (const match of reconciliationData.matches) {
          reconciliationInserts.push({
            company_id: companyUser.company_id,
            bank_transaction_id: match.bankTransaction.id,
            ledger_transaction_id: match.ledgerTransaction.id,
            status: 'matched',
            match_confidence: match.confidence,
            amount_difference: match.amountDifference,
            date_difference: match.dateDifference,
            notes: `Matched by ${match.matchType} matching`
          })
        }

        if (reconciliationInserts.length > 0) {
          const { error: reconciliationError } = await typedInsert(supabase, 'reconciliations', reconciliationInserts as unknown as ReconciliationInsert[])

          if (reconciliationError) {
            console.error('Error inserting reconciliations:', reconciliationError)
            throw new Error('Failed to store reconciliation matches')
          }
        }

        // Update files status to completed
        const completedUpdateData: Database['public']['Tables']['files']['Update'] = {
          status: 'completed',
          processing_completed_at: new Date().toISOString(),
          metadata: {
            transactions_extracted: bankTransactions.length + ledgerTransactions.length,
            reconciliation_summary: reconciliationData.summary
          }
        }

        await typedUpdate(supabase, 'files', completedUpdateData, 'id', fileIds)

        return NextResponse.json({
          success: true,
          message: 'Reconciliation completed successfully',
          summary: reconciliationData.summary,
          matches: reconciliationData.matches.length,
          processedFiles: files.length
        })

      } finally {
        // Clean up sandbox resources
        await sandbox.kill()
      }

    } catch (processingError) {
      console.error('Processing error:', processingError)

      // Update files status to failed
      const failedUpdateData: Database['public']['Tables']['files']['Update'] = {
        status: 'failed',
        processing_error: processingError instanceof Error ? processingError.message : String(processingError)
      }

      await typedUpdate(supabase, 'files', failedUpdateData, 'id', fileIds)

      return NextResponse.json(
        { error: `Processing failed: ${processingError instanceof Error ? processingError.message : String(processingError)}` },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('Process reconciliation API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
