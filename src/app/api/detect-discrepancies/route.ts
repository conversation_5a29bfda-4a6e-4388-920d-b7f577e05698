import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase/server';
import { DiscrepancyDetector } from '@/lib/services/discrepancy-detector';

// Define types for database entities
type CompanyUser = { company_id: string };

export async function POST(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient();

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get company ID for the user
    const { data: companyUser } = await supabase
      .from('company_users')
      .select('company_id')
      .eq('user_id', user.id)
      .single();

    if (!companyUser) {
      return NextResponse.json(
        { error: 'User not associated with any company' },
        { status: 403 }
      );
    }

    const companyId = (companyUser as CompanyUser).company_id;

    console.log(`Starting discrepancy detection for company: ${companyId}`);

    // Initialize discrepancy detector
    const detector = new DiscrepancyDetector();

    // Detect and populate discrepancies
    const result = await detector.detectAndPopulateDiscrepancies(companyId);

    // Get updated statistics
    const statistics = await detector.getDiscrepancyStatistics(companyId);

    // Log the detection operation
    await supabase.rpc('log_audit_event', {
      p_company_id: companyId,
      p_action: 'discrepancy_detection',
      p_resource_type: 'discrepancy',
      p_new_values: {
        detection_result: result,
        statistics,
        detected_at: new Date().toISOString()
      }
    } as any);

    // Update workflow state to indicate discrepancies are populated
    await supabase
      .from('workflow_step_results')
      .upsert({
        company_id: companyId,
        step: 'matching',
        status: 'completed',
        progress: 100,
        message: `Matching and discrepancy detection completed: ${result.totalDiscrepancies} discrepancies found`,
        can_proceed: true,
        completed_at: new Date().toISOString(),
        metadata: {
          discrepancy_detection: result,
          statistics
        }
      } as any, {
        onConflict: 'company_id,step'
      });

    return NextResponse.json({
      success: true,
      message: 'Discrepancy detection completed successfully',
      data: {
        detectionResult: result,
        statistics,
        detectedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Discrepancy detection error:', error);

    return NextResponse.json(
      {
        error: 'Internal server error during discrepancy detection',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient();

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get company ID for the user
    const { data: companyUser } = await supabase
      .from('company_users')
      .select('company_id')
      .eq('user_id', user.id)
      .single();

    if (!companyUser) {
      return NextResponse.json(
        { error: 'User not associated with any company' },
        { status: 403 }
      );
    }

    const companyId = (companyUser as CompanyUser).company_id;

    // Get discrepancy statistics
    const detector = new DiscrepancyDetector();
    const statistics = await detector.getDiscrepancyStatistics(companyId);

    return NextResponse.json({
      success: true,
      data: {
        statistics,
        company_id: companyId
      }
    });

  } catch (error) {
    console.error('Error fetching discrepancy statistics:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}