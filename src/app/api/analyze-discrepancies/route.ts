import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase/server';
import { AIDiscrepancyAnalyzer } from '@/lib/services/ai-discrepancy-analyzer';

// Define types for database entities
type CompanyUser = { company_id: string };

export async function POST(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient();

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const {
      generateJournalVouchers = false,
      analysisOptions = {}
    }: {
      generateJournalVouchers?: boolean;
      analysisOptions?: any;
    } = body;

    // Get company ID for the user
    const { data: companyUser } = await supabase
      .from('company_users')
      .select('company_id')
      .eq('user_id', user.id)
      .single();

    if (!companyUser) {
      return NextResponse.json(
        { error: 'User not associated with any company' },
        { status: 403 }
      );
    }

    const companyId = (companyUser as CompanyUser).company_id;

    // Initialize AI discrepancy analyzer
    const analyzer = new AIDiscrepancyAnalyzer();

    console.log(`Starting AI discrepancy analysis for company: ${companyId}`);

    // Perform AI analysis
    const analyses = await analyzer.analyzeDiscrepancies(companyId);

    let journalVouchers: any[] = [];
    if (generateJournalVouchers) {
      journalVouchers = await analyzer.generateJournalVoucherSuggestions(companyId);
    }

    // Log the analysis operation
    await supabase.rpc('log_audit_event', {
      p_company_id: companyId,
      p_action: 'ai_discrepancy_analysis',
      p_resource_type: 'discrepancy',
      p_new_values: {
        analyses_count: analyses.length,
        journal_vouchers_count: journalVouchers.length,
        analysis_options: analysisOptions
      }
    } as any);

    // Update workflow state
    await supabase
      .from('workflow_step_results')
      .upsert({
        company_id: companyId,
        step: 'review',
        status: 'completed',
        progress: 100,
        message: `AI analysis completed: ${analyses.length} discrepancies analyzed`,
        can_proceed: true,
        completed_at: new Date().toISOString(),
        metadata: {
          analyses_count: analyses.length,
          journal_vouchers_count: journalVouchers.length,
          analysis_options: analysisOptions
        }
      } as any, {
        onConflict: 'company_id,step'
      });

    // Calculate statistics
    const statistics = {
      totalDiscrepancies: analyses.length,
      bySeverity: {
        critical: analyses.filter(a => a.severity === 'critical').length,
        high: analyses.filter(a => a.severity === 'high').length,
        medium: analyses.filter(a => a.severity === 'medium').length,
        low: analyses.filter(a => a.severity === 'low').length
      },
      byType: {
        unmatched_bank: analyses.filter(a => a.discrepancyType === 'unmatched_bank').length,
        unmatched_ledger: analyses.filter(a => a.discrepancyType === 'unmatched_ledger').length,
        amount_mismatch: analyses.filter(a => a.discrepancyType === 'amount_mismatch').length,
        date_mismatch: analyses.filter(a => a.discrepancyType === 'date_mismatch').length,
        reference_mismatch: analyses.filter(a => a.discrepancyType === 'reference_mismatch').length
      },
      byAction: {
        manual_review: analyses.filter(a => a.suggestedAction === 'manual_review').length,
        auto_correct: analyses.filter(a => a.suggestedAction === 'auto_correct').length,
        ignore: analyses.filter(a => a.suggestedAction === 'ignore').length,
        investigate: analyses.filter(a => a.suggestedAction === 'investigate').length
      },
      averageConfidence: analyses.length > 0
        ? Math.round((analyses.reduce((sum, a) => sum + a.confidence, 0) / analyses.length) * 100) / 100
        : 0
    };

    return NextResponse.json({
      success: true,
      message: 'AI discrepancy analysis completed successfully',
      data: {
        analyses: analyses.map(a => ({
          id: a.id,
          discrepancyType: a.discrepancyType,
          severity: a.severity,
          amount: a.amount,
          description: a.description,
          aiExplanation: a.aiExplanation,
          aiRecommendation: a.aiRecommendation,
          suggestedAction: a.suggestedAction,
          confidence: a.confidence,
          metadata: a.metadata,
          status: a.status
        })),
        journalVouchers: journalVouchers.map(jv => ({
          id: jv.id,
          description: jv.description,
          debitAccount: jv.debitAccount,
          creditAccount: jv.creditAccount,
          amount: jv.amount,
          reference: jv.reference,
          explanation: jv.explanation,
          confidence: jv.confidence
        })),
        statistics,
        analysis_timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('AI discrepancy analysis error:', error);

    // Log the error
    try {
      const supabase = createServerSupabaseClient();
      const { data: { user } } = await supabase.auth.getUser();

      if (user) {
        const { data: companyUser } = await supabase
          .from('company_users')
          .select('company_id')
          .eq('user_id', user.id)
          .single();

        if (companyUser) {
          await supabase
            .from('workflow_step_results')
            .upsert({
              company_id: (companyUser as CompanyUser).company_id,
              step: 'review',
              status: 'failed',
              progress: 0,
              message: 'AI analysis failed',
              can_proceed: false,
              error_message: error instanceof Error ? error.message : 'Unknown error',
              metadata: {
                error: error instanceof Error ? error.stack : String(error)
              }
            } as any, {
              onConflict: 'company_id,step'
            });
        }
      }
    } catch (logError) {
      console.error('Failed to log analysis error:', logError);
    }

    return NextResponse.json(
      {
        error: 'Internal server error during AI discrepancy analysis',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient();

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const companyId = searchParams.get('companyId');
    const status = searchParams.get('status');
    const severity = searchParams.get('severity');
    const discrepancyType = searchParams.get('discrepancyType');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Get company ID for the user if not provided
    let targetCompanyId = companyId;
    if (!targetCompanyId) {
      const { data: companyUser } = await supabase
        .from('company_users')
        .select('company_id')
        .eq('user_id', user.id)
        .single();

      if (!companyUser) {
        return NextResponse.json(
          { error: 'User not associated with any company' },
          { status: 403 }
        );
      }
      targetCompanyId = (companyUser as CompanyUser).company_id;
    }

    // Build query for discrepancies
    let query = supabase
      .from('discrepancies')
      .select(`
        *,
        bank_transaction:bank_transaction_id(
          id, date, amount, description, reference,
          check_number, voucher_number, fin_reference
        ),
        ledger_transaction:ledger_transaction_id(
          id, date, amount, description, reference,
          check_number, voucher_number, fin_reference
        )
      `)
      .eq('company_id', targetCompanyId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (status && ['pending', 'analyzed', 'resolved', 'ignored'].includes(status)) {
      query = query.eq('status', status);
    }

    if (severity && ['low', 'medium', 'high', 'critical'].includes(severity)) {
      query = query.eq('severity', severity);
    }

    if (discrepancyType && ['unmatched_bank', 'unmatched_ledger', 'amount_mismatch', 'date_mismatch', 'reference_mismatch'].includes(discrepancyType)) {
      query = query.eq('discrepancy_type', discrepancyType);
    }

    const { data: discrepancies, error } = await query;

    if (error) {
      console.error('Error fetching discrepancies:', error);
      return NextResponse.json(
        { error: 'Failed to fetch discrepancy data' },
        { status: 500 }
      );
    }

    // Get summary statistics
    const { data: stats } = await supabase
      .from('discrepancies')
      .select('status, severity, discrepancy_type, suggested_action, confidence')
      .eq('company_id', targetCompanyId as string);

    const statistics = {
      total: stats?.length || 0,
      byStatus: {
        pending: stats?.filter((s: any) => s.status === 'pending').length || 0,
        analyzed: stats?.filter((s: any) => s.status === 'analyzed').length || 0,
        resolved: stats?.filter((s: any) => s.status === 'resolved').length || 0,
        ignored: stats?.filter((s: any) => s.status === 'ignored').length || 0
      },
      bySeverity: {
        critical: stats?.filter((s: any) => s.severity === 'critical').length || 0,
        high: stats?.filter((s: any) => s.severity === 'high').length || 0,
        medium: stats?.filter((s: any) => s.severity === 'medium').length || 0,
        low: stats?.filter((s: any) => s.severity === 'low').length || 0
      },
      byType: {
        unmatched_bank: stats?.filter((s: any) => s.discrepancy_type === 'unmatched_bank').length || 0,
        unmatched_ledger: stats?.filter((s: any) => s.discrepancy_type === 'unmatched_ledger').length || 0,
        amount_mismatch: stats?.filter((s: any) => s.discrepancy_type === 'amount_mismatch').length || 0,
        date_mismatch: stats?.filter((s: any) => s.discrepancy_type === 'date_mismatch').length || 0,
        reference_mismatch: stats?.filter((s: any) => s.discrepancy_type === 'reference_mismatch').length || 0
      },
      byAction: {
        manual_review: stats?.filter((s: any) => s.suggested_action === 'manual_review').length || 0,
        auto_correct: stats?.filter((s: any) => s.suggested_action === 'auto_correct').length || 0,
        ignore: stats?.filter((s: any) => s.suggested_action === 'ignore').length || 0,
        investigate: stats?.filter((s: any) => s.suggested_action === 'investigate').length || 0
      },
      averageConfidence: stats && stats.length > 0
        ? Math.round((stats.reduce((sum: number, s: any) => sum + (s.confidence || 0), 0) / stats.length) * 100) / 100
        : 0
    };

    return NextResponse.json({
      discrepancies: discrepancies || [],
      statistics,
      pagination: {
        limit,
        offset,
        total: statistics.total
      }
    });

  } catch (error) {
    console.error('Error fetching discrepancy data:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
