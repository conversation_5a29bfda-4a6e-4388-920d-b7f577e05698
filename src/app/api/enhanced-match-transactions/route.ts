import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase/server';
import { EnhancedTransactionMatcher } from '@/lib/services/enhanced-transaction-matcher';

// Define types for database entities
type CompanyUser = { company_id: string };

export async function POST(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient();

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const {
      bankFileId,
      ledgerFileId,
      matchingOptions = {}
    }: {
      bankFileId?: string;
      ledgerFileId?: string;
      matchingOptions?: any;
    } = body;

    // Get company ID for the user
    const { data: companyUser } = await supabase
      .from('company_users')
      .select('company_id')
      .eq('user_id', user.id)
      .single();

    if (!companyUser) {
      return NextResponse.json(
        { error: 'User not associated with any company' },
        { status: 403 }
      );
    }

    const companyId = (companyUser as CompanyUser).company_id;

    // Initialize enhanced transaction matcher
    const matcher = new EnhancedTransactionMatcher();

    console.log(`Starting enhanced RFSA matching for company: ${companyId}`);

    // Perform enhanced matching with RFSA patterns
    const matchingResult = await matcher.matchTransactions(
      companyId,
      bankFileId,
      ledgerFileId
    );

    console.log('Enhanced matching completed:', {
      exact: matchingResult.exactMatches.length,
      finReference: matchingResult.finReferenceMatches.length,
      checkNumber: matchingResult.checkNumberMatches.length,
      fuzzy: matchingResult.fuzzyMatches.length,
      unmatchedBank: matchingResult.unmatchedBankTransactions.length,
      unmatchedLedger: matchingResult.unmatchedLedgerTransactions.length
    });

    // Save enhanced matching results to database
    await matcher.saveEnhancedMatchingResults(
      companyId,
      matchingResult,
      user.id
    );

    // After matching, detect and populate discrepancies
    console.log('Starting discrepancy detection after matching...');
    const discrepancyResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3001'}/api/detect-discrepancies`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': request.headers.get('cookie') || ''
      }
    });

    let discrepancyResult = null;
    if (discrepancyResponse.ok) {
      const discrepancyData = await discrepancyResponse.json();
      discrepancyResult = discrepancyData.data;
      console.log('Discrepancy detection completed:', discrepancyResult.detectionResult);
    } else {
      console.warn('Discrepancy detection failed, but continuing with matching results');
    }

    // Log the matching operation
    await supabase.rpc('log_audit_event', {
      p_company_id: companyId,
      p_action: 'enhanced_transaction_matching',
      p_resource_type: 'reconciliation',
      p_new_values: {
        bank_file_id: bankFileId,
        ledger_file_id: ledgerFileId,
        statistics: matchingResult.statistics,
        validation: matchingResult.validation,
        algorithm_version: 'v2.0_rfsa',
        discrepancy_detection: discrepancyResult?.detectionResult
      }
    } as any);

    // Update workflow state to indicate matching is complete
    await supabase
      .from('workflow_step_results')
      .upsert({
        company_id: companyId,
        step: 'matching',
        status: 'completed',
        progress: 100,
        message: `Enhanced matching completed: ${matchingResult.statistics.matchPercentage}% match rate${discrepancyResult ? `, ${discrepancyResult.detectionResult.totalDiscrepancies} discrepancies detected` : ''}`,
        can_proceed: true,
        completed_at: new Date().toISOString(),
        metadata: {
          statistics: matchingResult.statistics,
          validation: matchingResult.validation,
          algorithm_version: 'v2.0_rfsa'
        }
      } as any, {
        onConflict: 'company_id,step'
      });

    return NextResponse.json({
      success: true,
      message: 'Enhanced RFSA transaction matching completed successfully',
      data: {
        statistics: matchingResult.statistics,
        validation: matchingResult.validation,
        matches: {
          exact: matchingResult.exactMatches.length,
          finReference: matchingResult.finReferenceMatches.length,
          checkNumber: matchingResult.checkNumberMatches.length,
          fuzzy: matchingResult.fuzzyMatches.length
        },
        unmatched: {
          bank: matchingResult.unmatchedBankTransactions.length,
          ledger: matchingResult.unmatchedLedgerTransactions.length
        },
        algorithm_version: 'v2.0_rfsa',
        processing_time: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Enhanced transaction matching error:', error);

    // Log the error
    try {
      const supabase = createServerSupabaseClient();
      const { data: { user } } = await supabase.auth.getUser();

      if (user) {
        const { data: companyUser } = await supabase
          .from('company_users')
          .select('company_id')
          .eq('user_id', user.id)
          .single();

        if (companyUser) {
          await supabase
            .from('workflow_step_results')
            .upsert({
              company_id: (companyUser as CompanyUser).company_id,
              step: 'matching',
              status: 'failed',
              progress: 0,
              message: 'Enhanced matching failed',
              can_proceed: false,
              error_message: error instanceof Error ? error.message : 'Unknown error',
              metadata: {
                error: error instanceof Error ? error.stack : String(error),
                algorithm_version: 'v2.0_rfsa'
              }
            } as any, {
              onConflict: 'company_id,step'
            });
        }
      }
    } catch (logError) {
      console.error('Failed to log matching error:', logError);
    }

    return NextResponse.json(
      {
        error: 'Internal server error during enhanced transaction matching',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient();

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const companyId = searchParams.get('companyId');
    const status = searchParams.get('status');
    const matchType = searchParams.get('matchType');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Get company ID for the user if not provided
    let targetCompanyId = companyId;
    if (!targetCompanyId) {
      const { data: companyUser } = await supabase
        .from('company_users')
        .select('company_id')
        .eq('user_id', user.id)
        .single();

      if (!companyUser) {
        return NextResponse.json(
          { error: 'User not associated with any company' },
          { status: 403 }
        );
      }
      targetCompanyId = (companyUser as CompanyUser).company_id;
    }

    // Build query for enhanced reconciliations
    let query = supabase
      .from('reconciliations')
      .select(`
        *,
        bank_transaction:bank_transaction_id(
          id, date, amount, description, reference,
          check_number, voucher_number, fin_reference,
          transaction_code, branch_code, serial_number
        ),
        ledger_transaction:ledger_transaction_id(
          id, date, amount, description, reference,
          check_number, voucher_number, fin_reference,
          transaction_code, branch_code, serial_number
        )
      `)
      .eq('company_id', targetCompanyId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (status && ['pending', 'matched', 'discrepancy', 'reviewed', 'auto_matched', 'manual_matched', 'potential_match'].includes(status)) {
      query = query.eq('status', status);
    }

    if (matchType && ['exact', 'fin_reference', 'check_number', 'amount_date', 'fuzzy', 'manual'].includes(matchType)) {
      query = query.eq('match_type', matchType);
    }

    const { data: reconciliations, error } = await query;

    if (error) {
      console.error('Error fetching enhanced reconciliations:', error);
      return NextResponse.json(
        { error: 'Failed to fetch reconciliation data' },
        { status: 500 }
      );
    }

    // Get summary statistics with enhanced breakdown
    const { data: stats } = await supabase
      .from('reconciliations')
      .select('status, match_type, match_confidence')
      .eq('company_id', targetCompanyId as string);

    const statistics = {
      total: stats?.length || 0,
      matched: stats?.filter((s: any) => ['matched', 'auto_matched', 'manual_matched'].includes(s.status)).length || 0,
      pending: stats?.filter((s: any) => s.status === 'pending').length || 0,
      discrepancies: stats?.filter((s: any) => s.status === 'discrepancy').length || 0,
      matchTypes: {
        exact: stats?.filter((s: any) => s.match_type === 'exact').length || 0,
        fin_reference: stats?.filter((s: any) => s.match_type === 'fin_reference').length || 0,
        check_number: stats?.filter((s: any) => s.match_type === 'check_number').length || 0,
        fuzzy: stats?.filter((s: any) => s.match_type === 'fuzzy').length || 0,
        manual: stats?.filter((s: any) => s.match_type === 'manual').length || 0
      },
      averageConfidence: stats && stats.length > 0
        ? Math.round((stats.reduce((sum: number, s: any) => sum + (s.match_confidence || 0), 0) / stats.length) * 100) / 100
        : 0
    };

    // Get workflow state
    const { data: workflowState } = await supabase
      .from('workflow_states')
      .select('*')
      .eq('company_id', targetCompanyId)
      .single();

    return NextResponse.json({
      reconciliations: reconciliations || [],
      statistics,
      workflowState,
      pagination: {
        limit,
        offset,
        total: statistics.total
      },
      algorithm_version: 'v2.0_rfsa'
    });

  } catch (error) {
    console.error('Error fetching enhanced reconciliation data:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
