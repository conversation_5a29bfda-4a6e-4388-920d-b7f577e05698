import { createClient } from '@supabase/supabase-js'
import { NextRequest, NextResponse } from 'next/server'

// NOTE: This API uses 'accounting_companies' table instead of 'companies'
// to avoid conflicts with existing loyalty program data.
// Run fix-database-schema.sql to create the required tables.

// Create Supabase client with service role for admin operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

export async function POST(request: NextRequest) {
  try {
    // Get the authenticated user from the request
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json({ error: 'No authorization header' }, { status: 401 })
    }

    // Verify the user's JWT token
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token)

    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    // Check if user already has a company
    const { data: existingCompany } = await supabaseAdmin
      .from('company_users')
      .select('company_id')
      .eq('user_id', user.id)
      .limit(1)
      .single()

    if (existingCompany) {
      return NextResponse.json({ companyId: existingCompany.company_id })
    }

    // Get user profile or create it if it doesn't exist
    let { data: userProfile } = await supabaseAdmin
      .from('user_profiles')
      .select('email, full_name')
      .eq('id', user.id)
      .single()

    if (!userProfile) {
      // Create user profile if it doesn't exist
      const { data: newProfile, error: profileError } = await supabaseAdmin
        .from('user_profiles')
        .insert({
          id: user.id,
          email: user.email || '',
          full_name: user.user_metadata?.full_name || null
        })
        .select('email, full_name')
        .single()

      if (profileError) {
        console.error('Profile creation error:', profileError)
        return NextResponse.json({ error: 'Failed to create user profile' }, { status: 500 })
      }
      
      userProfile = newProfile
    }

    const companyName = userProfile.full_name
      ? `${userProfile.full_name}'s Company`
      : `${userProfile.email.split('@')[0]}'s Company`

    const companySlug = companyName.toLowerCase()
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')

    // Create the company using service role (using accounting_companies table)
    const { data: newCompany, error: companyError } = await supabaseAdmin
      .from('accounting_companies')
      .insert({
        name: companyName,
        slug: companySlug,
        settings: {
          currency: 'USD',
          fiscal_year_start: '01-01',
          timezone: 'America/New_York',
          reconciliation_settings: {
            auto_match_threshold: 95.0,
            date_tolerance_days: 3,
            amount_tolerance_cents: 0
          }
        }
      })
      .select('id')
      .single()

    if (companyError) {
      console.error('Company creation error:', companyError)

      // Check if the error is due to missing tables
      if (companyError.message?.includes('relation "accounting_companies" does not exist')) {
        return NextResponse.json({
          error: 'Database schema not set up. Please run fix-database-schema.sql to create the required tables.',
          details: 'The accounting app requires specific database tables that are not present in the current schema.'
        }, { status: 500 })
      }

      // Check for duplicate slug error
      if (companyError.code === '23505' && companyError.message?.includes('slug')) {
        // Try to find existing company with this slug and associate user with it
        const { data: existingCompanyWithSlug } = await supabaseAdmin
          .from('accounting_companies')
          .select('id')
          .eq('slug', companySlug)
          .single()

        if (existingCompanyWithSlug) {
          // Associate user with existing company
          const { error: userCompanyError } = await supabaseAdmin
            .from('company_users')
            .insert({
              company_id: existingCompanyWithSlug.id,
              user_id: user.id,
              role: 'admin',
              accepted_at: new Date().toISOString()
            })

          if (!userCompanyError) {
            return NextResponse.json({ companyId: existingCompanyWithSlug.id })
          }
        }
      }

      return NextResponse.json({ error: 'Failed to create company' }, { status: 500 })
    }

    // Associate user with the company using service role
    const { error: userCompanyError } = await supabaseAdmin
      .from('company_users')
      .insert({
        company_id: newCompany.id,
        user_id: user.id,
        role: 'admin',
        accepted_at: new Date().toISOString()
      })

    if (userCompanyError) {
      console.error('User company association error:', userCompanyError)
      return NextResponse.json({ error: 'Failed to associate user with company' }, { status: 500 })
    }

    return NextResponse.json({ companyId: newCompany.id })

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}