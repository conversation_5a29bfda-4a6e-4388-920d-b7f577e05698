import { NextRequest, NextResponse } from 'next/server'
import { SimpleDocumentProcessor } from '@/lib/services/simple-document-processor'
import { createServerSupabaseClient } from '@/lib/supabase/server'
import { Database } from '@/types/database'
import { typedUpdate, typedInsert } from '@/lib/supabase/helpers'

// Define types for database entities
type CompanyUser = { company_id: string }
type FileRecord = {
  id: string
  filename: string
  status: string
  metadata: Record<string, any>
  created_at: string
  updated_at: string
}
type FileUpdate = Database['public']['Tables']['files']['Update']

export async function POST(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse form data
    const formData = await request.formData()
    const file = formData.get('file') as File
    const fileType = formData.get('type') as string // 'bank_statement' or 'ledger'
    const mistralApiKey = formData.get('mistral_key') as string
    const geminiApiKey = formData.get('gemini_key') as string

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      )
    }

    if (!fileType || !['bank_statement', 'ledger'].includes(fileType)) {
      return NextResponse.json(
        { error: 'Invalid file type. Must be bank_statement or ledger' },
        { status: 400 }
      )
    }

    // Validate API keys based on file type
    if (fileType === 'bank_statement' && !mistralApiKey) {
      return NextResponse.json(
        { error: 'Mistral API key required for bank statement processing' },
        { status: 400 }
      )
    }

    if (fileType === 'ledger' && !geminiApiKey) {
      return NextResponse.json(
        { error: 'Gemini API key required for ledger processing' },
        { status: 400 }
      )
    }

    // Get company ID for the user
    const { data: companyUser } = await supabase
      .from('company_users')
      .select('company_id')
      .eq('user_id', user.id)
      .single()

    if (!companyUser) {
      return NextResponse.json(
        { error: 'User not associated with any company' },
        { status: 403 }
      )
    }

    // Check if file record exists (should be created by upload process)
    const { data: fileRecord } = await supabase
      .from('files')
      .select('*')
      .eq('filename', file.name)
      .eq('company_id', (companyUser as CompanyUser).company_id)
      .eq('status', 'completed') // Files are completed upload, ready for processing
      .single()

    if (!fileRecord) {
      return NextResponse.json(
        { error: 'File not found or not ready for processing' },
        { status: 404 }
      )
    }

    // Update file status to processing
    const processingUpdateData: Database['public']['Tables']['files']['Update'] = {
      status: 'processing',
      updated_at: new Date().toISOString()
    }
    
    await typedUpdate(supabase, 'files', processingUpdateData, 'id', (fileRecord as FileRecord).id)

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer()
    const buffer = Buffer.from(arrayBuffer)

    // Initialize document processor
    const processor = new SimpleDocumentProcessor()

    try {
      const result = await processor.processDocument({
        fileId: (fileRecord as FileRecord).id,
        fileName: file.name,
        fileType: file.name.endsWith('.pdf') ? 'pdf' : 
                  file.name.endsWith('.xlsx') ? 'xlsx' :
                  file.name.endsWith('.xls') ? 'xls' : 'csv',
        documentType: fileType as 'bank_statement' | 'ledger',
        companyId: (companyUser as CompanyUser).company_id
      })

      if (result.success && result.transactions) {
        // Transactions are already stored by the processor
        const transactions = result.transactions

        // Update file status to completed
        const completedUpdateData: Database['public']['Tables']['files']['Update'] = {
          status: 'completed',
          metadata: {
            ...((fileRecord as FileRecord).metadata || {}),
            processing_result: result as any
          },
          updated_at: new Date().toISOString()
        }
        
        await typedUpdate(supabase, 'files', completedUpdateData, 'id', (fileRecord as FileRecord).id)

        return NextResponse.json({
          success: true,
          message: 'File processed successfully',
          data: {
            transactions_count: transactions.length,
            file_id: (fileRecord as FileRecord).id,
            processing_result: result
          }
        })

      } else {
        // Update file status to failed
        const failedUpdateData: Database['public']['Tables']['files']['Update'] = {
          status: 'failed',
          metadata: {
            ...((fileRecord as FileRecord).metadata || {}),
            processing_result: result as any
          },
          updated_at: new Date().toISOString()
        }
        
        await typedUpdate(supabase, 'files', failedUpdateData, 'id', (fileRecord as FileRecord).id)

        return NextResponse.json({
          success: false,
          error: result.error || 'Processing failed',
          file_id: (fileRecord as FileRecord).id
        }, { status: 500 })
      }

    } catch (processingError) {
      console.error('Processing failed:', processingError)
      throw processingError
    }

  } catch (error) {
    console.error('Processing error:', error)
    return NextResponse.json(
      { error: 'Internal server error during processing' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const fileId = searchParams.get('file_id')

    if (!fileId) {
      return NextResponse.json(
        { error: 'File ID required' },
        { status: 400 }
      )
    }

    // Get company ID for the user
    const { data: companyUser } = await supabase
      .from('company_users')
      .select('company_id')
      .eq('user_id', user.id)
      .single()

    if (!companyUser) {
      return NextResponse.json(
        { error: 'User not associated with any company' },
        { status: 403 }
      )
    }

    // Get processing status
    const { data: fileRecord, error } = await supabase
      .from('files')
      .select('*')
      .eq('id', fileId)
      .eq('company_id', (companyUser as CompanyUser).company_id)
      .single()

    if (error || !fileRecord) {
      return NextResponse.json(
        { error: 'File not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      file_id: (fileRecord as FileRecord).id,
      name: (fileRecord as FileRecord).filename,
      status: (fileRecord as FileRecord).status,
      processing_result: ((fileRecord as FileRecord).metadata)?.processing_result,
      created_at: (fileRecord as FileRecord).created_at,
      updated_at: (fileRecord as FileRecord).updated_at
    })

  } catch (error) {
    console.error('Status check error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}