import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'
import TransactionMatcher, { MatchingOptions } from '@/lib/services/transaction-matcher'

// Define types for database entities
type CompanyUser = { company_id: string }

export async function POST(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { 
      bankFileId, 
      ledgerFileId, 
      options 
    }: { 
      bankFileId?: string
      ledgerFileId?: string
      options?: Partial<MatchingOptions>
    } = body

    // Get company ID for the user
    const { data: companyUser } = await supabase
      .from('company_users')
      .select('company_id')
      .eq('user_id', user.id)
      .single()

    if (!companyUser) {
      return NextResponse.json(
        { error: 'User not associated with any company' },
        { status: 403 }
      )
    }

    // Initialize transaction matcher
    const matcher = new TransactionMatcher()

    // Get transactions for matching
    const { bankTransactions, ledgerTransactions } = await matcher.getTransactionsForMatching(
      (companyUser as CompanyUser).company_id,
      bankFileId,
      ledgerFileId
    )

    if (bankTransactions.length === 0) {
      return NextResponse.json(
        { error: 'No bank transactions found for matching' },
        { status: 400 }
      )
    }

    if (ledgerTransactions.length === 0) {
      return NextResponse.json(
        { error: 'No ledger transactions found for matching' },
        { status: 400 }
      )
    }

    // Merge options with defaults to ensure all required fields are present
    const defaultOptions = {
      exactMatchFields: ['reference', 'date', 'amount'] as ('reference' | 'date' | 'amount')[],
      fuzzyMatchThreshold: 70,
      dateToleranceDays: 3,
      amountTolerancePercentage: 5,
      descriptionSimilarityThreshold: 60
    }
    
    const mergedOptions = { ...defaultOptions, ...options }

    // Perform matching
    const matchingResult = await matcher.matchTransactions(
      bankTransactions,
      ledgerTransactions,
      mergedOptions
    )

    // Save matching results to database
    await matcher.saveMatchingResults(
      (companyUser as CompanyUser).company_id,
      matchingResult,
      user.id
    )

    // Log the matching operation
    await supabase.rpc('log_audit_event', {
      p_company_id: (companyUser as CompanyUser).company_id,
      p_action: 'transaction_matching',
      p_resource_type: 'reconciliation',
      p_new_values: {
        bank_file_id: bankFileId,
        ledger_file_id: ledgerFileId,
        statistics: matchingResult.statistics
      }
    } as any)

    return NextResponse.json({
      success: true,
      message: 'Transaction matching completed successfully',
      data: {
        statistics: matchingResult.statistics,
        exactMatches: matchingResult.exactMatches.length,
        fuzzyMatches: matchingResult.fuzzyMatches.length,
        heuristicMatches: matchingResult.heuristicMatches.length,
        potentialMatches: matchingResult.potentialMatches.length,
        unmatchedBank: matchingResult.unmatchedBankTransactions.length,
        unmatchedLedger: matchingResult.unmatchedLedgerTransactions.length
      }
    })

  } catch (error) {
    console.error('Transaction matching error:', error)
    return NextResponse.json(
      { error: 'Internal server error during transaction matching' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const companyId = searchParams.get('companyId')
    const status = searchParams.get('status')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')

    // Get company ID for the user if not provided
    let targetCompanyId = companyId
    if (!targetCompanyId) {
      const { data: companyUser } = await supabase
        .from('company_users')
        .select('company_id')
        .eq('user_id', user.id)
        .single()

      if (!companyUser) {
        return NextResponse.json(
          { error: 'User not associated with any company' },
          { status: 403 }
        )
      }
      targetCompanyId = (companyUser as CompanyUser).company_id
    }

    // Build query for reconciliations
    let query = supabase
      .from('reconciliations')
      .select(`
        *,
        bank_transaction:bank_transaction_id(id, date, amount, description, reference),
        ledger_transaction:ledger_transaction_id(id, date, amount, description, reference)
      `)
      .eq('company_id', targetCompanyId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (status && ['pending', 'matched', 'discrepancy', 'reviewed', 'auto_matched', 'manual_matched', 'potential_match'].includes(status)) {
      query = query.eq('status', status as string)
    }

    const { data: reconciliations, error } = await query

    if (error) {
      console.error('Error fetching reconciliations:', error)
      return NextResponse.json(
        { error: 'Failed to fetch reconciliation data' },
        { status: 500 }
      )
    }

    // Get summary statistics
    const { data: stats } = await supabase
      .from('reconciliations')
      .select('status')
      .eq('company_id', targetCompanyId as string)

    const statistics = {
      total: stats?.length || 0,
      matched: stats?.filter((s: any) => ['matched', 'auto_matched', 'manual_matched'].includes(s.status)).length || 0,
      pending: stats?.filter((s: any) => s.status === 'pending').length || 0,
      discrepancies: stats?.filter((s: any) => s.status === 'discrepancy').length || 0
    }

    return NextResponse.json({
      reconciliations: reconciliations || [],
      statistics,
      pagination: {
        limit,
        offset,
        total: statistics.total
      }
    })

  } catch (error) {
    console.error('Error fetching reconciliation data:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
