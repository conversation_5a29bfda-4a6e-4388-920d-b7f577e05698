import { createClient } from '@supabase/supabase-js'
import { NextRequest, NextResponse } from 'next/server'

// Create Supabase client with service role for admin operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

export async function POST(request: NextRequest) {
  try {
    // Get the authenticated user from the request
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json({ error: 'No authorization header' }, { status: 401 })
    }

    // Verify the user's JWT token
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token)

    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    // Parse form data
    const formData = await request.formData()
    const file = formData.get('file') as File
    const documentType = formData.get('documentType') as string
    const companyId = formData.get('companyId') as string

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }

    if (!companyId) {
      return NextResponse.json({ error: 'No company ID provided' }, { status: 400 })
    }

    // Verify user has access to this company
    const { data: companyUser } = await supabaseAdmin
      .from('company_users')
      .select('company_id')
      .eq('user_id', user.id)
      .eq('company_id', companyId)
      .single()

    if (!companyUser) {
      return NextResponse.json({ error: 'User does not have access to this company' }, { status: 403 })
    }

    // Create a unique file name with timestamp
    const fileExt = file.name.split('.').pop()
    const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`
    const filePath = `uploads/${user.id}/${fileName}`

    // Upload to Supabase Storage
    const { data: storageData, error: storageError } = await supabaseAdmin.storage
      .from('financial-documents')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false
      })

    if (storageError) {
      console.error('Storage upload error:', storageError)
      return NextResponse.json({ error: `Upload failed: ${storageError.message}` }, { status: 500 })
    }

    // Create file record in database using service role
    const { data: fileRecord, error: dbError } = await supabaseAdmin
      .from('files')
      .insert({
        company_id: companyId,
        uploaded_by: user.id,
        filename: fileName,
        original_filename: file.name,
        file_size: file.size,
        mime_type: file.type,
        storage_path: storageData.path,
        status: 'uploading',
        metadata: {
          upload_timestamp: new Date().toISOString(),
          file_type: documentType !== 'all' ? documentType : (file.type.includes('pdf') ? 'bank_statement' : 'ledger')
        }
      })
      .select('id')
      .single()

    if (dbError) {
      console.error('Database insert error:', dbError)
      // Clean up uploaded file if database insert fails
      await supabaseAdmin.storage
        .from('financial-documents')
        .remove([storageData.path])

      return NextResponse.json({ error: `Database error: ${dbError.message}` }, { status: 500 })
    }

    // Update file status to 'uploaded' after successful upload
    const { error: statusUpdateError } = await supabaseAdmin
      .from('files')
      .update({
        status: 'uploaded',
        updated_at: new Date().toISOString()
      })
      .eq('id', fileRecord.id)

    if (statusUpdateError) {
      console.error('Status update error:', statusUpdateError)
      // Don't fail the upload, just log the error
    }

    return NextResponse.json({
      success: true,
      fileId: fileRecord.id,
      storagePath: storageData.path
    })

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
