import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'

// Define types for API responses
type CompanyUser = { company_id: string }

type ReconciliationSummary = {
  total_bank_transactions: number
  total_ledger_transactions: number
  matched_transactions: number
  unmatched_bank_transactions: number
  unmatched_ledger_transactions: number
  total_discrepancy: number
  reconciliation_status: string
}

type TransactionType = {
  transaction_type: string
}

type ReconciliationType = {
  status: string
  match_confidence: number
}

export async function GET(request: NextRequest) {
  try {
    // Create server-side Supabase client with the latest implementation
    const supabase = createServerSupabaseClient()

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')

    // Get company ID for the user
    const { data: companyUser } = await supabase
      .from('company_users')
      .select('company_id')
      .eq('user_id', user.id)
      .single()

    if (!companyUser) {
      return NextResponse.json(
        { error: 'User not associated with any company' },
        { status: 403 }
      )
    }

    // Get reconciliation summary using the database function
    const { data: summaryData, error: summaryError } = await supabase
      .rpc('get_reconciliation_summary', {
        p_company_id: (companyUser as CompanyUser).company_id
      } as any)

    if (summaryError) {
      console.error('Error getting reconciliation summary:', summaryError)
      return NextResponse.json(
        { error: 'Failed to get reconciliation summary' },
        { status: 500 }
      )
    }

    const summary = (summaryData?.[0] as ReconciliationSummary) || {
      total_bank_transactions: 0,
      total_ledger_transactions: 0,
      matched_transactions: 0,
      unmatched_bank_transactions: 0,
      unmatched_ledger_transactions: 0,
      total_discrepancy: 0,
      reconciliation_status: 'pending'
    } as ReconciliationSummary

    // Get transactions for reconciliation display
    const { data: transactionsData, error: transactionsError } = await supabase
      .rpc('get_reconciliation_transactions', {
        p_company_id: (companyUser as CompanyUser).company_id,
        p_status: status,
        p_limit: limit,
        p_offset: offset  // Pass as integer, not string
      } as any)

    if (transactionsError) {
      console.error('Error getting reconciliation transactions:', transactionsError)
      return NextResponse.json(
        { error: 'Failed to get reconciliation transactions' },
        { status: 500 }
      )
    }

    // Ensure transactionsData is an array before accessing length
    const transactions = Array.isArray(transactionsData) ? transactionsData : [];

    return NextResponse.json({
      summary: {
        totalBankTransactions: summary.total_bank_transactions,
        totalLedgerTransactions: summary.total_ledger_transactions,
        matchedTransactions: summary.matched_transactions,
        unmatchedBankTransactions: summary.unmatched_bank_transactions,
        unmatchedLedgerTransactions: summary.unmatched_ledger_transactions,
        totalDiscrepancy: typeof summary.total_discrepancy === 'string' ? parseFloat(summary.total_discrepancy) : summary.total_discrepancy,
        reconciliationStatus: summary.reconciliation_status
      },
      transactions,
      pagination: {
        limit,
        offset,
        total: transactions.length
      }
    })

  } catch (error) {
    console.error('Reconciliation API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Create server-side Supabase client with the latest implementation
    const supabase = createServerSupabaseClient()

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { action, bankTransactionId, ledgerTransactionId, reconciliationId, notes } = body

    // Get company ID for the user
    const { data: companyUser } = await supabase
      .from('company_users')
      .select('company_id')
      .eq('user_id', user.id)
      .single()

    if (!companyUser) {
      return NextResponse.json(
        { error: 'User not associated with any company' },
        { status: 403 }
      )
    }

    switch (action) {
      case 'manual_match':
        if (!bankTransactionId || !ledgerTransactionId) {
          return NextResponse.json(
            { error: 'Bank transaction ID and ledger transaction ID are required for manual matching' },
            { status: 400 }
          )
        }

        const { data: matchResult, error: matchError } = await supabase
          .rpc('manual_match_transactions', {
            p_company_id: (companyUser as CompanyUser).company_id,
            p_bank_transaction_id: bankTransactionId,
            p_ledger_transaction_id: ledgerTransactionId,
            p_user_id: user.id,
            p_notes: notes
          } as any)

        if (matchError) {
          console.error('Error creating manual match:', matchError)
          return NextResponse.json(
            { error: 'Failed to create manual match' },
            { status: 500 }
          )
        }

        return NextResponse.json({
          success: true,
          message: 'Transactions matched manually',
          reconciliationId: matchResult
        })

      case 'unmatch':
        if (!reconciliationId) {
          return NextResponse.json(
            { error: 'Reconciliation ID is required for unmatching' },
            { status: 400 }
          )
        }

        const { error: unmatchError } = await supabase
          .rpc('unmatch_transactions', {
            p_reconciliation_id: reconciliationId,
            p_company_id: (companyUser as CompanyUser).company_id,
            p_user_id: user.id
          } as any)

        if (unmatchError) {
          console.error('Error unmatching transactions:', unmatchError)
          return NextResponse.json(
            { error: 'Failed to unmatch transactions' },
            { status: 500 }
          )
        }

        return NextResponse.json({
          success: true,
          message: 'Transactions unmatched successfully'
        })

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('Reconciliation action error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
