import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'

export async function GET() {
  try {
    const supabase = createServerSupabaseClient()

    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get the user's company (assuming one company per user for now)
    const { data: companyUser, error: companyError } = await supabase
      .from('company_users')
      .select('company_id')
      .eq('user_id', user.id)
      .single()

    if (companyError || !companyUser) {
      return NextResponse.json({ error: 'Company not found' }, { status: 404 })
    }

    // Fetch matched transactions with their details
    // Instead of filtering by status = 'matched', let's look for reconciliations that have both bank and ledger transaction IDs
    const { data: reconciliations, error: reconciliationError } = await supabase
      .from('reconciliations')
      .select(`
        id,
        bank_transaction_id,
        ledger_transaction_id,
        status,
        match_confidence,
        amount_difference,
        date_difference,
        match_type,
        notes
      `)
      .eq('company_id', (companyUser as any).company_id)
      .not('bank_transaction_id', 'is', null)
      .not('ledger_transaction_id', 'is', null) as any

    if (reconciliationError) {
      console.error('Error fetching reconciliations:', reconciliationError)
      return NextResponse.json({ error: 'Failed to fetch reconciliations' }, { status: 500 })
    }

    // Get all transaction IDs
    const bankTransactionIds = reconciliations?.map((r: any) => r.bank_transaction_id) || []
    const ledgerTransactionIds = reconciliations?.map((r: any) => r.ledger_transaction_id) || []

    // Fetch bank transactions
    const { data: bankTransactions, error: bankError } = await supabase
      .from('transactions')
      .select('*')
      .in('id', bankTransactionIds) as any

    if (bankError) {
      console.error('Error fetching bank transactions:', bankError)
      return NextResponse.json({ error: 'Failed to fetch bank transactions' }, { status: 500 })
    }

    // Fetch ledger transactions
    const { data: ledgerTransactions, error: ledgerError } = await supabase
      .from('transactions')
      .select('*')
      .in('id', ledgerTransactionIds) as any

    if (ledgerError) {
      console.error('Error fetching ledger transactions:', ledgerError)
      return NextResponse.json({ error: 'Failed to fetch ledger transactions' }, { status: 500 })
    }

    // Create lookup maps
    const bankMap = new Map(bankTransactions?.map((t: any) => [t.id, t]) || [])
    const ledgerMap = new Map(ledgerTransactions?.map((t: any) => [t.id, t]) || [])

    // Combine reconciliations with transaction details
    const matchedTransactions = reconciliations?.map((reconciliation: any) => ({
      ...reconciliation,
      bankTransaction: bankMap.get(reconciliation.bank_transaction_id),
      ledgerTransaction: ledgerMap.get(reconciliation.ledger_transaction_id)
    })).filter((match: any) => match.bankTransaction && match.ledgerTransaction) || []

    return NextResponse.json(matchedTransactions)

  } catch (error) {
    console.error('Unexpected error in matches API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}