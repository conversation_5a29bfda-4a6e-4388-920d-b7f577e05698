import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'

export async function GET() {
  try {
    const supabase = createServerSupabaseClient()

    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get the user's company
    const { data: companyUser, error: companyError } = await supabase
      .from('company_users')
      .select('company_id')
      .eq('user_id', user.id)
      .single()

    if (companyError || !companyUser) {
      return NextResponse.json({ error: 'Company not found' }, { status: 404 })
    }

    // Get all matched transaction IDs
    const { data: reconciliations, error: reconciliationError } = await supabase
      .from('reconciliations')
      .select('bank_transaction_id, ledger_transaction_id')
      .eq('company_id', (companyUser as any).company_id)
      .eq('status', 'matched') as any

    if (reconciliationError) {
      console.error('Error fetching reconciliations:', reconciliationError)
      return NextResponse.json({ error: 'Failed to fetch reconciliations' }, { status: 500 })
    }

    const matchedBankIds = new Set(reconciliations?.map((r: any) => r.bank_transaction_id) || [])
    const matchedLedgerIds = new Set(reconciliations?.map((r: any) => r.ledger_transaction_id) || [])

    // Fetch all transactions for this company
    const { data: allTransactions, error: transactionError } = await supabase
      .from('transactions')
      .select('*')
      .eq('company_id', (companyUser as any).company_id) as any

    if (transactionError) {
      console.error('Error fetching transactions:', transactionError)
      return NextResponse.json({ error: 'Failed to fetch transactions' }, { status: 500 })
    }

    // Filter unmatched transactions
    const unmatchedBank = allTransactions?.filter((t: any) =>
      t.transaction_type === 'bank_statement' && !matchedBankIds.has(t.id)
    ) || []

    const unmatchedLedger = allTransactions?.filter((t: any) =>
      t.transaction_type === 'ledger_entry' && !matchedLedgerIds.has(t.id)
    ) || []

    return NextResponse.json({
      bank: unmatchedBank,
      ledger: unmatchedLedger
    })

  } catch (error) {
    console.error('Unexpected error in unmatched API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}