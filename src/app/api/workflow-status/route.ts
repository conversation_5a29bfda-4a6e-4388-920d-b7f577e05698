import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'

// Define types for database entities
type CompanyUser = { company_id: string }

type FileType = {
  id: string
  company_id: string
  mime_type: string
  status: string
}

type TransactionType = {
  transaction_type: string
}

type ReconciliationType = {
  status: string
  match_confidence: number
}

export async function GET(request: NextRequest) {
  try {
    // Create server-side Supabase client with the latest implementation
    const supabase = createServerSupabaseClient()

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get company ID for the user
    const { data: companyUser } = await supabase
      .from('company_users')
      .select('company_id')
      .eq('user_id', user.id)
      .single()

    type CompanyUser = { company_id: string }

    if (!companyUser) {
      return NextResponse.json(
        { error: 'User not associated with any company' },
        { status: 403 }
      )
    }

    // Get files status
    const { data: files, error: filesError } = await supabase
      .from('files')
      .select('*')
      .eq('company_id', (companyUser as CompanyUser).company_id)
      .order('created_at', { ascending: false })

    type FileType = {
      id: string
      status: string
      company_id: string
      mime_type: string
    }

    if (filesError) {
      console.error('Error getting files:', filesError)
      return NextResponse.json(
        { error: 'Failed to get files status' },
        { status: 500 }
      )
    }

    // Get transaction counts
    const { data: transactionCounts, error: transactionError } = await supabase
      .from('transactions')
      .select('transaction_type')
      .eq('company_id', (companyUser as CompanyUser).company_id)

    type TransactionType = {
      transaction_type: string
    }

    const bankTransactionCount = transactionCounts?.filter(t => (t as TransactionType).transaction_type === 'bank_statement').length || 0
    const ledgerTransactionCount = transactionCounts?.filter(t => (t as TransactionType).transaction_type === 'ledger_entry').length || 0

    // Get reconciliation status with proper counting
    const { data: reconciliations, error: reconciliationError } = await supabase
      .from('reconciliations')
      .select('status, match_confidence, bank_transaction_id, ledger_transaction_id')
      .eq('company_id', (companyUser as CompanyUser).company_id)

    type ReconciliationType = {
      status: string
      match_confidence: number
      bank_transaction_id: string
      ledger_transaction_id: string
    }

    const matchedCount = reconciliations?.filter(r => ['matched', 'auto_matched'].includes((r as ReconciliationType).status)).length || 0
    const reviewedCount = reconciliations?.filter(r => (r as ReconciliationType).status === 'reviewed').length || 0
    const uniqueBankMatched = new Set(reconciliations?.filter(r => ['matched', 'auto_matched'].includes((r as ReconciliationType).status))
      .map(r => (r as ReconciliationType).bank_transaction_id)).size
    const uniqueLedgerMatched = new Set(reconciliations?.filter(r => ['matched', 'auto_matched'].includes((r as ReconciliationType).status))
      .map(r => (r as ReconciliationType).ledger_transaction_id)).size

    // Determine workflow stage
    let currentStage = 'upload'
    let stageProgress = 0

    if (files && files.length > 0) {
      const hasProcessingFiles = files.some(f => (f as FileType).status === 'processing')
      const hasCompletedFiles = files.some(f => (f as FileType).status === 'completed')
      const hasFailedFiles = files.some(f => (f as FileType).status === 'failed')

      if (hasProcessingFiles) {
        currentStage = 'processing'
        stageProgress = 50
      } else if (hasCompletedFiles && (bankTransactionCount > 0 || ledgerTransactionCount > 0)) {
        if (matchedCount > 0) {
          currentStage = 'review'
          stageProgress = 75
        } else {
          currentStage = 'matching'
          stageProgress = 60
        }
      } else if (hasFailedFiles) {
        currentStage = 'failed'
        stageProgress = 0
      } else if (files.every(f => (f as FileType).status === 'uploaded')) {
        currentStage = 'uploaded'
        stageProgress = 25
      }
    }

    // Calculate next steps
    const nextSteps = []

    if (currentStage === 'upload' || (files && files.length === 0)) {
      nextSteps.push({
        action: 'upload_files',
        title: 'Upload Documents',
        description: 'Upload your bank statements and ledger files',
        priority: 'high',
        estimatedTime: '2 minutes'
      })
    } else if (currentStage === 'uploaded') {
      nextSteps.push({
        action: 'start_processing',
        title: 'Start Processing',
        description: 'Begin extracting transactions from uploaded files',
        priority: 'high',
        estimatedTime: '3-5 minutes'
      })
    } else if (currentStage === 'processing') {
      nextSteps.push({
        action: 'wait_processing',
        title: 'Processing in Progress',
        description: 'AI is extracting and analyzing your transactions',
        priority: 'medium',
        estimatedTime: '2-3 minutes remaining'
      })
    } else if (currentStage === 'matching') {
      nextSteps.push({
        action: 'start_reconciliation',
        title: 'Start Reconciliation',
        description: 'Begin matching bank and ledger transactions',
        priority: 'high',
        estimatedTime: '1-2 minutes'
      })
    } else if (currentStage === 'review') {
      nextSteps.push({
        action: 'review_matches',
        title: 'Review Matches',
        description: 'Review and approve transaction matches',
        priority: 'medium',
        estimatedTime: '5-10 minutes'
      })
      nextSteps.push({
        action: 'generate_report',
        title: 'Generate Report',
        description: 'Create final reconciliation report',
        priority: 'low',
        estimatedTime: '1 minute'
      })
    }

    return NextResponse.json({
      currentStage,
      stageProgress,
      files: files || [],
      summary: {
        totalFiles: files?.length || 0,
        uploadedFiles: files?.filter(f => (f as FileType).status === 'uploaded').length || 0,
        processingFiles: files?.filter(f => (f as FileType).status === 'processing').length || 0,
        completedFiles: files?.filter(f => (f as FileType).status === 'completed').length || 0,
        failedFiles: files?.filter(f => (f as FileType).status === 'failed').length || 0,
        bankTransactions: bankTransactionCount,
        ledgerTransactions: ledgerTransactionCount,
        matchedTransactions: Math.min(uniqueBankMatched, uniqueLedgerMatched), // Proper 1:1 match count
        reviewedTransactions: reviewedCount,
        totalReconciliationRecords: matchedCount, // Total reconciliation records for debugging
        uniqueLedgerMatched: uniqueLedgerMatched // For debugging
      },
      nextSteps
    })

  } catch (error) {
    console.error('Workflow status API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
