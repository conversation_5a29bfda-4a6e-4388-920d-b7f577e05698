import { redirect } from 'next/navigation'
import { supabase } from '@/lib/supabase'
import { LandingPage } from '@/components/landing-page'

// Force dynamic rendering
export const dynamic = 'force-dynamic'

export default async function HomePage() {
  // Check if user is authenticated
  const { data: { session } } = await supabase.auth.getSession()

  if (session) {
    redirect('/dashboard')
  }

  return <LandingPage />
}