'use client'

import { useEffect, useState } from 'react'
import { createClient } from '@/lib/supabase'

export default function TestAuthPage() {
  const [authState, setAuthState] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const supabase = createClient()

  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Check session
        const { data: { session }, error: sessionError } = await supabase.auth.getSession()
        
        // Check user
        const { data: { user }, error: userError } = await supabase.auth.getUser()

        // Test API call
        const apiResponse = await fetch('/api/test-auth')
        const apiData = await apiResponse.json()

        setAuthState({
          session: session ? { user: session.user?.email, expires_at: session.expires_at } : null,
          sessionError: sessionError?.message,
          user: user ? { id: user.id, email: user.email } : null,
          userError: userError?.message,
          apiResponse: apiData,
          apiStatus: apiResponse.status
        })
      } catch (error) {
        setAuthState({
          error: error instanceof Error ? error.message : String(error)
        })
      } finally {
        setLoading(false)
      }
    }

    checkAuth()
  }, [])

  if (loading) {
    return <div className="p-8">Loading auth state...</div>
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Authentication Test</h1>
      <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
        {JSON.stringify(authState, null, 2)}
      </pre>
      
      <div className="mt-4">
        <button 
          onClick={() => window.location.reload()} 
          className="bg-blue-500 text-white px-4 py-2 rounded"
        >
          Refresh
        </button>
      </div>
    </div>
  )
}
