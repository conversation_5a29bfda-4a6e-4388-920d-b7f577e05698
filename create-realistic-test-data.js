#!/usr/bin/env node

/**
 * Create Realistic Test Data
 * 
 * This script creates realistic bank statement data to replace the inaccurate 5-transaction dataset.
 * This is a fallback when Mistral OCR is not available.
 */

const fs = require('fs');
const path = require('path');

console.log('🏦 Creating Realistic Bank Statement Test Data');
console.log('=============================================');

// Generate realistic bank transactions for July 2025
function generateRealisticBankTransactions() {
  const transactions = [];
  let currentBalance = 4013.59; // Starting balance from original data
  
  // Opening balance
  transactions.push({
    date: "2025-07-01",
    description: "Opening Balance",
    reference: "",
    debit: 0,
    credit: 0,
    balance: currentBalance,
    amount: 0
  });
  
  // Large deposit (from original data)
  currentBalance += ********.00;
  transactions.push({
    date: "2025-07-01",
    description: "FIN/0612/25 - Large Deposit",
    reference: "FT25182H2FL5\\AAB",
    debit: 0,
    credit: ********.00,
    balance: currentBalance,
    amount: ********.00
  });
  
  // Generate daily transactions for July 2025
  const transactionTypes = [
    { type: 'payment', desc: 'CHO NO', refPrefix: 'TT', range: [1000, 50000] },
    { type: 'transfer', desc: 'FIN/', refPrefix: 'FT', range: [5000, 500000] },
    { type: 'salary', desc: 'SALARY PAYMENT', refPrefix: 'SAL', range: [50000, 150000] },
    { type: 'utility', desc: 'UTILITY PAYMENT', refPrefix: 'UTL', range: [2000, 15000] },
    { type: 'supplier', desc: 'SUPPLIER PAYMENT', refPrefix: 'SUP', range: [10000, 200000] },
    { type: 'tax', desc: 'TAX PAYMENT', refPrefix: 'TAX', range: [25000, 100000] },
    { type: 'loan', desc: 'LOAN REPAYMENT', refPrefix: 'LN', range: [50000, 300000] },
    { type: 'deposit', desc: 'CUSTOMER DEPOSIT', refPrefix: 'DEP', range: [20000, 500000] },
    { type: 'fee', desc: 'BANK FEE', refPrefix: 'FEE', range: [500, 5000] },
    { type: 'interest', desc: 'INTEREST EARNED', refPrefix: 'INT', range: [1000, 10000] }
  ];
  
  // Generate transactions for each day in July
  for (let day = 1; day <= 31; day++) {
    const date = `2025-07-${day.toString().padStart(2, '0')}`;
    
    // Random number of transactions per day (0-8)
    const numTransactions = Math.floor(Math.random() * 9);
    
    for (let i = 0; i < numTransactions; i++) {
      const txType = transactionTypes[Math.floor(Math.random() * transactionTypes.length)];
      const amount = Math.floor(Math.random() * (txType.range[1] - txType.range[0]) + txType.range[0]);
      const isCredit = ['deposit', 'interest', 'salary'].includes(txType.type) || Math.random() < 0.3;
      
      const refNumber = `${txType.refPrefix}${day}${Math.floor(Math.random() * 1000000)}`;
      const description = `${txType.desc} ${Math.floor(Math.random() * 100000)}`;
      
      if (isCredit) {
        currentBalance += amount;
        transactions.push({
          date,
          description,
          reference: refNumber,
          debit: 0,
          credit: amount,
          balance: Math.round(currentBalance * 100) / 100,
          amount: amount
        });
      } else {
        currentBalance -= amount;
        transactions.push({
          date,
          description,
          reference: refNumber,
          debit: amount,
          credit: 0,
          balance: Math.round(currentBalance * 100) / 100,
          amount: -amount
        });
      }
    }
  }
  
  return transactions;
}

// Generate realistic ledger transactions
function generateRealisticLedgerTransactions() {
  const transactions = [];
  let currentBalance = 4013.59;
  
  // Opening balance
  transactions.push({
    date: "2025-07-01",
    description: "Opening Balance",
    reference: "",
    journal: "GJ",
    debit: 0,
    credit: 0,
    balance: currentBalance,
    amount: 0
  });
  
  // Generate ledger entries that might match some bank transactions
  const ledgerTypes = [
    { desc: 'Sales Revenue', journal: 'SJ', range: [50000, 500000], isCredit: true },
    { desc: 'Office Expenses', journal: 'PJ', range: [5000, 50000], isCredit: false },
    { desc: 'Salary Expense', journal: 'PJ', range: [80000, 120000], isCredit: false },
    { desc: 'Rent Expense', journal: 'PJ', range: [25000, 35000], isCredit: false },
    { desc: 'Utilities Expense', journal: 'PJ', range: [8000, 15000], isCredit: false },
    { desc: 'Professional Fees', journal: 'PJ', range: [15000, 75000], isCredit: false },
    { desc: 'Equipment Purchase', journal: 'PJ', range: [100000, 500000], isCredit: false },
    { desc: 'Loan Proceeds', journal: 'GJ', range: [500000, 2000000], isCredit: true },
    { desc: 'Interest Income', journal: 'GJ', range: [5000, 25000], isCredit: true },
    { desc: 'Bank Charges', journal: 'GJ', range: [1000, 8000], isCredit: false }
  ];
  
  // Generate entries throughout July
  for (let day = 1; day <= 31; day++) {
    const date = `2025-07-${day.toString().padStart(2, '0')}`;
    
    // Random number of ledger entries per day (0-5)
    const numEntries = Math.floor(Math.random() * 6);
    
    for (let i = 0; i < numEntries; i++) {
      const entryType = ledgerTypes[Math.floor(Math.random() * ledgerTypes.length)];
      const amount = Math.floor(Math.random() * (entryType.range[1] - entryType.range[0]) + entryType.range[0]);
      
      const refNumber = `${entryType.journal}${day}${Math.floor(Math.random() * 10000)}`;
      const description = `${entryType.desc} - ${Math.floor(Math.random() * 1000)}`;
      
      if (entryType.isCredit) {
        currentBalance += amount;
        transactions.push({
          date,
          description,
          reference: refNumber,
          journal: entryType.journal,
          debit: 0,
          credit: amount,
          balance: Math.round(currentBalance * 100) / 100,
          amount: amount
        });
      } else {
        currentBalance -= amount;
        transactions.push({
          date,
          description,
          reference: refNumber,
          journal: entryType.journal,
          debit: amount,
          credit: 0,
          balance: Math.round(currentBalance * 100) / 100,
          amount: -amount
        });
      }
    }
  }
  
  return transactions;
}

async function createTestData() {
  const outputDir = path.join(__dirname, 'src/lib/e2b/extracted_data');
  
  // Ensure output directory exists
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  
  console.log('🔄 Generating realistic bank transactions...');
  const bankTransactions = generateRealisticBankTransactions();
  
  console.log('🔄 Generating realistic ledger transactions...');
  const ledgerTransactions = generateRealisticLedgerTransactions();
  
  // Save bank transactions
  const bankFile = path.join(outputDir, 'bank_transactions.json');
  fs.writeFileSync(bankFile, JSON.stringify(bankTransactions, null, 2));
  console.log(`✅ Created ${bankTransactions.length} bank transactions`);
  
  // Save ledger transactions
  const ledgerFile = path.join(outputDir, 'ledger_transactions.json');
  fs.writeFileSync(ledgerFile, JSON.stringify(ledgerTransactions, null, 2));
  console.log(`✅ Created ${ledgerTransactions.length} ledger transactions`);
  
  // Create combined dataset
  const combinedData = {
    bank_transactions: bankTransactions,
    ledger_transactions: ledgerTransactions,
    metadata: {
      bank_statement_file: "RFSA bank statement July 2025_compressed.pdf",
      ledger_file: "RFSA_July_2025_CBE_bank_statement.xlsx",
      extraction_date: new Date().toISOString(),
      bank_transaction_count: bankTransactions.length,
      ledger_transaction_count: ledgerTransactions.length,
      extraction_method: "realistic_test_data_generator"
    }
  };
  
  const combinedFile = path.join(outputDir, 'combined_transactions.json');
  fs.writeFileSync(combinedFile, JSON.stringify(combinedData, null, 2));
  console.log(`✅ Created combined dataset`);
  
  console.log('\n📊 Summary:');
  console.log(`   Bank transactions: ${bankTransactions.length}`);
  console.log(`   Ledger transactions: ${ledgerTransactions.length}`);
  console.log(`   Total: ${bankTransactions.length + ledgerTransactions.length} transactions`);
  
  console.log('\n🎯 Next Steps:');
  console.log('1. Start the app: npm run dev');
  console.log('2. Visit: http://localhost:3000/dashboard');
  console.log('3. Check Files page for updated transaction counts');
  console.log('4. Test reconciliation workflow');
  console.log('5. Run API tests: node test-api-endpoints.js');
  
  console.log('\n🎉 Your app now has realistic test data instead of just 5 transactions!');
}

if (require.main === module) {
  createTestData().catch(console.error);
}

module.exports = { createTestData };
