const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://rrvdmpagsvhjdurfmqec.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJydmRtcGFnc3ZoamR1cmZtcWVjIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1ODEzMDM1MiwiZXhwIjoyMDczNzA2MzUyfQ.SbZmq3nlQudzUN-xzJHj-NS23xnFVSlyx1RZWWZTy6U'

const supabase = createClient(supabaseUrl, supabaseKey)

async function checkFileStatuses() {
  try {
    console.log('Checking file statuses in database...')

    const companyId = '5277bdf7-bbeb-4cab-bcbb-e55ee177b4dd'

    // Get all files for the company
    const { data: files, error } = await supabase
      .from('files')
      .select('id, original_filename, status, created_at, processing_completed_at')
      .eq('company_id', companyId)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching files:', error)
      return
    }

    console.log(`Total files found: ${files.length}`)
    console.log('\nFile details:')
    files.forEach((file, i) => {
      console.log(`${i+1}. ${file.original_filename}`)
      console.log(`   ID: ${file.id}`)
      console.log(`   Status: ${file.status}`)
      console.log(`   Created: ${file.created_at}`)
      console.log(`   Completed: ${file.processing_completed_at || 'Not completed'}`)
      console.log('')
    })

    // Check status distribution
    const statusCounts = files.reduce((acc, file) => {
      acc[file.status] = (acc[file.status] || 0) + 1
      return acc
    }, {})

    console.log('Status distribution:', statusCounts)

    // Check if we should reset statuses for testing
    const completedFiles = files.filter(f => f.status === 'completed')
    if (completedFiles.length > 0) {
      console.log(`\n🔧 Found ${completedFiles.length} completed files.`)
      console.log('To test reconciliation, we can reset their status to "uploaded".')
      console.log('\nRun this command to reset statuses:')
      console.log(`node reset-file-status.js`)
    }

  } catch (error) {
    console.error('Failed to check file statuses:', error)
  }
}

checkFileStatuses()