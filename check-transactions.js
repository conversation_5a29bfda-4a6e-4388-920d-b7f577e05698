const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://rrvdmpagsvhjdurfmqec.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJydmRtcGFnc3ZoamR1cmZtcWVjIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1ODEzMDM1MiwiZXhwIjoyMDczNzA2MzUyfQ.SbZmq3nlQudzUN-xzJHj-NS23xnFVSlyx1RZWWZTy6U'

const supabase = createClient(supabaseUrl, supabaseKey)

async function checkTransactions() {
  try {
    console.log('Checking transactions in database...')

    const companyId = '5277bdf7-bbeb-4cab-bcbb-e55ee177b4dd'

    // Get transaction counts by type
    const { data: transactions, error } = await supabase
      .from('transactions')
      .select('transaction_type, file_id, id, amount')
      .eq('company_id', companyId)

    if (error) {
      console.error('Error fetching transactions:', error)
      return
    }

    console.log(`Total transactions found: ${transactions.length}`)

    // Group by transaction type
    const byType = transactions.reduce((acc, tx) => {
      acc[tx.transaction_type] = (acc[tx.transaction_type] || 0) + 1
      return acc
    }, {})

    console.log('Transactions by type:', byType)

    // Group by file_id
    const byFile = transactions.reduce((acc, tx) => {
      acc[tx.file_id] = (acc[tx.file_id] || 0) + 1
      return acc
    }, {})

    console.log('Transactions by file_id:', byFile)

    // Show sample transactions
    console.log('\nSample bank_statement transactions:')
    const bankTxns = transactions.filter(tx => tx.transaction_type === 'bank_statement').slice(0, 3)
    bankTxns.forEach(tx => {
      console.log(`  ID: ${tx.id}, Amount: ${tx.amount}, File: ${tx.file_id}`)
    })

    console.log('\nSample ledger_entry transactions:')
    const ledgerTxns = transactions.filter(tx => tx.transaction_type === 'ledger_entry').slice(0, 3)
    ledgerTxns.forEach(tx => {
      console.log(`  ID: ${tx.id}, Amount: ${tx.amount}, File: ${tx.file_id}`)
    })

  } catch (error) {
    console.error('Failed to check transactions:', error)
  }
}

checkTransactions()