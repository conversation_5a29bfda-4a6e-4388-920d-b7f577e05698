#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

// Initialize Supabase client
const supabaseUrl = 'https://rrvdmpagsvhjdurfmqec.supabase.co'
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJydmRtcGFnc3ZoamR1cmZtcWVjIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1ODEzMDM1MiwiZXhwIjoyMDczNzA2MzUyfQ.SbZmq3nlQudzUN-xzJHj-NS23xnFVSlyx1RZWWZTy6U'

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function loadRealExtractedData() {
  try {
    console.log('🗑️ Clearing mock data and loading real E2B-extracted data...')

    const companyId = '5277bdf7-bbeb-4cab-bcbb-e55ee177b4dd'

    // 1. Clear existing transactions and reconciliations
    console.log('🧹 Clearing existing mock data...')

    await supabase.from('reconciliations').delete().eq('company_id', companyId)
    await supabase.from('transactions').delete().eq('company_id', companyId)

    // 2. Load real bank transactions
    console.log('📊 Loading real bank transactions...')
    const bankData = JSON.parse(fs.readFileSync('/Users/<USER>/Desktop/projects/acounting-app/src/lib/e2b/extracted_data/bank_transactions.json', 'utf8'))

    const bankTransactionsToInsert = bankData.map(tx => ({
      company_id: companyId,
      file_id: 'ce077564-e35d-4993-a178-c200a953dd43', // Latest bank file
      transaction_type: 'bank_statement',
      date: tx.date,
      amount: parseFloat(tx.amount),
      description: tx.description,
      reference: tx.reference || '',
      balance: parseFloat(tx.balance),
      raw_data: tx,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }))

    const { data: bankInsertResult, error: bankError } = await supabase
      .from('transactions')
      .insert(bankTransactionsToInsert)

    if (bankError) {
      console.error('❌ Error inserting bank transactions:', bankError)
      return
    }

    console.log(`✅ Inserted ${bankTransactionsToInsert.length} bank transactions`)

    // 3. Load real ledger transactions
    console.log('📚 Loading real ledger transactions...')
    const ledgerData = JSON.parse(fs.readFileSync('/Users/<USER>/Desktop/projects/acounting-app/src/lib/e2b/extracted_data/ledger_transactions_complete.json', 'utf8'))

    const ledgerTransactionsToInsert = ledgerData.map(tx => ({
      company_id: companyId,
      file_id: 'eece45b4-3c81-451c-818b-08c31a2ab7a3', // Latest ledger file
      transaction_type: 'ledger_entry',
      date: tx.date,
      amount: parseFloat(tx.amount),
      description: tx.description,
      reference: tx.reference || '',
      account: tx.account_id || '',
      balance: parseFloat(tx.balance),
      raw_data: tx,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }))

    const { data: ledgerInsertResult, error: ledgerError } = await supabase
      .from('transactions')
      .insert(ledgerTransactionsToInsert)

    if (ledgerError) {
      console.error('❌ Error inserting ledger transactions:', ledgerError)
      return
    }

    console.log(`✅ Inserted ${ledgerTransactionsToInsert.length} ledger transactions`)

    // 4. Verify the data
    console.log('🧪 Verifying loaded data...')
    const { data: transactionCounts, error: countError } = await supabase
      .from('transactions')
      .select('transaction_type')
      .eq('company_id', companyId)

    if (countError) {
      console.error('❌ Error counting transactions:', countError)
      return
    }

    const bankCount = transactionCounts.filter(t => t.transaction_type === 'bank_statement').length
    const ledgerCount = transactionCounts.filter(t => t.transaction_type === 'ledger_entry').length

    console.log('📈 Data loading completed!')
    console.log(`   Bank transactions: ${bankCount}`)
    console.log(`   Ledger transactions: ${ledgerCount}`)
    console.log('   Ready for AI-powered matching!')

  } catch (error) {
    console.error('❌ Error loading real extracted data:', error)
  }
}

// Run the data loading
loadRealExtractedData()