#!/bin/bash

echo "🧪 Testing Reconciliation API..."

# Test if server is running
echo "📡 Checking if server is running on port 3003..."
if ! curl -s http://localhost:3003 > /dev/null; then
    echo "❌ Server not running on port 3003"
    echo "🚀 Starting dev server..."
    npm run dev &
    sleep 10
fi

echo "📊 Testing seamless reconciliation endpoint..."

# Test the reconciliation API
response=$(curl -s -w "%{http_code}" -X POST \
  http://localhost:3003/api/seamless-reconciliation \
  -H "Content-Type: application/json" \
  -d '{"test": true}')

http_code="${response: -3}"
body="${response%???}"

echo "📋 HTTP Status: $http_code"
echo "📄 Response: $body"

if [ "$http_code" = "200" ]; then
    echo "✅ API call successful"

    # Parse the response to check for suspicious results
    if echo "$body" | grep -q '"matchedTransactions":1000'; then
        echo "⚠️  Still getting suspicious 1000 matches"
    else
        echo "✅ Results look realistic"
    fi
else
    echo "❌ API call failed"
fi

echo "✅ Test complete!"