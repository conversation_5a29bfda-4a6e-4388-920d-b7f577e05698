-- Migration: Add RFSA-specific fields to transactions table
-- Date: 2025-12-20
-- Description: Add fields to support Ethiopian banking patterns and enhanced matching

-- Add RFSA-specific fields to transactions table
ALTER TABLE transactions
ADD COLUMN IF NOT EXISTS check_number VARCHAR(50),
ADD COLUMN IF NOT EXISTS voucher_number VARCHAR(50),
ADD COLUMN IF NOT EXISTS fin_reference VARCHAR(100),
ADD COLUMN IF NOT EXISTS transaction_code VARCHAR(20),
ADD COLUMN IF NOT EXISTS branch_code VARCHAR(10),
ADD COLUMN IF NOT EXISTS serial_number VARCHAR(50),
ADD COLUMN IF NOT EXISTS processed_at TIMESTAMP WITH TIME ZONE;

-- Add indexes for better performance on RFSA fields
CREATE INDEX IF NOT EXISTS idx_transactions_check_number ON transactions(check_number) WHERE check_number IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_transactions_voucher_number ON transactions(voucher_number) WHERE voucher_number IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_transactions_fin_reference ON transactions(fin_reference) WHERE fin_reference IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_transactions_transaction_code ON transactions(transaction_code) WHERE transaction_code IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_transactions_branch_code ON transactions(branch_code) WHERE branch_code IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_transactions_serial_number ON transactions(serial_number) WHERE serial_number IS NOT NULL;

-- Add composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_transactions_company_type_date ON transactions(company_id, transaction_type, date);
CREATE INDEX IF NOT EXISTS idx_transactions_company_amount ON transactions(company_id, amount) WHERE amount IS NOT NULL;

-- Update reconciliations table to support enhanced matching
ALTER TABLE reconciliations
ADD COLUMN IF NOT EXISTS matched_fields TEXT[],
ADD COLUMN IF NOT EXISTS amount_difference NUMERIC,
ADD COLUMN IF NOT EXISTS date_difference INTEGER,
ADD COLUMN IF NOT EXISTS processing_time_ms INTEGER,
ADD COLUMN IF NOT EXISTS match_algorithm_version VARCHAR(20) DEFAULT 'v2.0';

-- Add indexes for reconciliations
CREATE INDEX IF NOT EXISTS idx_reconciliations_match_confidence ON reconciliations(match_confidence) WHERE match_confidence IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_reconciliations_match_type ON reconciliations(match_type) WHERE match_type IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_reconciliations_amount_difference ON reconciliations(amount_difference) WHERE amount_difference IS NOT NULL;

-- Add workflow state tracking table
CREATE TABLE IF NOT EXISTS workflow_states (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    current_step VARCHAR(20) NOT NULL,
    status VARCHAR(20) NOT NULL,
    completed_steps TEXT[] DEFAULT '{}',
    progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for workflow_states
CREATE INDEX IF NOT EXISTS idx_workflow_states_company_id ON workflow_states(company_id);
CREATE INDEX IF NOT EXISTS idx_workflow_states_current_step ON workflow_states(current_step);
CREATE INDEX IF NOT EXISTS idx_workflow_states_status ON workflow_states(status);

-- Add workflow step results table
CREATE TABLE IF NOT EXISTS workflow_step_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    step VARCHAR(20) NOT NULL,
    status VARCHAR(20) NOT NULL,
    progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
    message TEXT,
    can_proceed BOOLEAN DEFAULT FALSE,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for workflow_step_results
CREATE INDEX IF NOT EXISTS idx_workflow_step_results_company_id ON workflow_step_results(company_id);
CREATE INDEX IF NOT EXISTS idx_workflow_step_results_step ON workflow_step_results(step);
CREATE INDEX IF NOT EXISTS idx_workflow_step_results_status ON workflow_step_results(status);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add triggers for updated_at
DROP TRIGGER IF EXISTS update_workflow_states_updated_at ON workflow_states;
CREATE TRIGGER update_workflow_states_updated_at
    BEFORE UPDATE ON workflow_states
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_workflow_step_results_updated_at ON workflow_step_results;
CREATE TRIGGER update_workflow_step_results_updated_at
    BEFORE UPDATE ON workflow_step_results
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON COLUMN transactions.check_number IS 'Check number extracted from bank statement (e.g., CHQ NO ********)';
COMMENT ON COLUMN transactions.voucher_number IS 'Voucher number from ledger entry';
COMMENT ON COLUMN transactions.fin_reference IS 'FIN reference number (e.g., FIN/2052/25)';
COMMENT ON COLUMN transactions.transaction_code IS 'Bank transaction code';
COMMENT ON COLUMN transactions.branch_code IS 'Bank branch code';
COMMENT ON COLUMN transactions.serial_number IS 'Serial number for transaction';
COMMENT ON COLUMN transactions.processed_at IS 'Timestamp when transaction was processed';

COMMENT ON COLUMN reconciliations.matched_fields IS 'Array of fields that matched between transactions';
COMMENT ON COLUMN reconciliations.amount_difference IS 'Absolute difference in amounts between matched transactions';
COMMENT ON COLUMN reconciliations.date_difference IS 'Difference in days between transaction dates';
COMMENT ON COLUMN reconciliations.processing_time_ms IS 'Time taken to process this reconciliation in milliseconds';
COMMENT ON COLUMN reconciliations.match_algorithm_version IS 'Version of matching algorithm used';

COMMENT ON TABLE workflow_states IS 'Tracks the overall workflow state for each company';
COMMENT ON TABLE workflow_step_results IS 'Tracks individual step results within the workflow';
