/**
 * Seed Test Data Script
 * Populates the database with extracted transaction data for testing
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase credentials in .env.local');
  console.log('Required variables:');
  console.log('- NEXT_PUBLIC_SUPABASE_URL');
  console.log('- SUPABASE_SERVICE_ROLE_KEY (or NEXT_PUBLIC_SUPABASE_ANON_KEY)');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Load extracted data
const extractedDataPath = path.join(__dirname, 'src/lib/e2b/extracted_data/combined_transactions.json');
const extractedData = JSON.parse(fs.readFileSync(extractedDataPath, 'utf8'));

console.log('🌱 Seeding Test Data');
console.log('===================');

async function seedTestData() {
  try {
    // Step 1: Create or get test company
    console.log('\n1️⃣ Setting up test company...');
    
    const { data: existingCompany } = await supabase
      .from('companies')
      .select('id')
      .eq('slug', 'test-company')
      .single();

    let companyId;
    if (existingCompany) {
      companyId = existingCompany.id;
      console.log('✅ Using existing test company:', companyId);
    } else {
      const { data: newCompany, error: companyError } = await supabase
        .from('companies')
        .insert({
          name: 'Test Company Ltd',
          slug: 'test-company',
          settings: {
            currency: 'USD',
            fiscal_year_start: '2025-01-01'
          }
        })
        .select('id')
        .single();

      if (companyError) {
        console.error('❌ Error creating company:', companyError);
        return;
      }

      companyId = newCompany.id;
      console.log('✅ Created test company:', companyId);
    }

    // Step 2: Create test user profile (if needed)
    console.log('\n2️⃣ Setting up test user...');
    
    const { data: { user } } = await supabase.auth.getUser();
    let userId;
    
    if (user) {
      userId = user.id;
      console.log('✅ Using authenticated user:', userId);
      
      // Ensure user is associated with company
      const { error: companyUserError } = await supabase
        .from('company_users')
        .upsert({
          company_id: companyId,
          user_id: userId,
          role: 'admin'
        });

      if (companyUserError && !companyUserError.message.includes('duplicate')) {
        console.error('❌ Error associating user with company:', companyUserError);
        return;
      }
    } else {
      console.log('⚠️  No authenticated user found. You may need to sign in first.');
      userId = '********-0000-0000-0000-************'; // Placeholder
    }

    // Step 3: Create test files
    console.log('\n3️⃣ Creating test files...');
    
    const testFiles = [
      {
        filename: 'test-bank-statement.pdf',
        original_filename: 'RFSA bank statement July 2025_compressed.pdf',
        file_size: 1024000,
        mime_type: 'application/pdf',
        storage_path: 'test/bank-statement.pdf',
        status: 'completed',
        metadata: {
          document_type: 'bank_statement',
          processing_completed: true,
          transaction_count: extractedData.bank_transactions.length
        }
      },
      {
        filename: 'test-ledger.xlsx',
        original_filename: 'RFSA_July_2025_CBE_bank_statement.xlsx',
        file_size: 512000,
        mime_type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        storage_path: 'test/ledger.xlsx',
        status: 'completed',
        metadata: {
          document_type: 'ledger_entry',
          processing_completed: true,
          transaction_count: extractedData.ledger_transactions.length
        }
      }
    ];

    const fileIds = [];
    for (const fileData of testFiles) {
      const { data: file, error: fileError } = await supabase
        .from('files')
        .upsert({
          ...fileData,
          company_id: companyId,
          uploaded_by: userId,
          processing_completed_at: new Date().toISOString()
        })
        .select('id')
        .single();

      if (fileError) {
        console.error('❌ Error creating file:', fileError);
        return;
      }

      fileIds.push(file.id);
      console.log(`✅ Created file: ${fileData.original_filename} (${file.id})`);
    }

    // Step 4: Insert bank transactions
    console.log('\n4️⃣ Inserting bank transactions...');
    
    const bankTransactions = extractedData.bank_transactions
      .filter(tx => tx.amount !== undefined) // Skip opening balance
      .map(tx => ({
        id: crypto.randomUUID(),
        company_id: companyId,
        file_id: fileIds[0], // Bank statement file
        transaction_type: 'bank_statement',
        date: tx.date,
        amount: tx.amount,
        description: tx.description,
        reference: tx.reference || null,
        balance: tx.balance || null,
        raw_data: tx
      }));

    if (bankTransactions.length > 0) {
      const { error: bankError } = await supabase
        .from('transactions')
        .upsert(bankTransactions);

      if (bankError) {
        console.error('❌ Error inserting bank transactions:', bankError);
        return;
      }

      console.log(`✅ Inserted ${bankTransactions.length} bank transactions`);
    }

    // Step 5: Insert ledger transactions (batch in chunks of 100)
    console.log('\n5️⃣ Inserting ledger transactions...');
    
    const ledgerTransactions = extractedData.ledger_transactions
      .filter(tx => tx.amount !== undefined) // Skip opening balance
      .map(tx => ({
        id: crypto.randomUUID(),
        company_id: companyId,
        file_id: fileIds[1], // Ledger file
        transaction_type: 'ledger_entry',
        date: tx.date,
        amount: tx.amount,
        description: tx.description,
        reference: tx.reference || null,
        account: tx.account || null,
        category: tx.category || null,
        balance: tx.balance || null,
        raw_data: tx
      }));

    // Insert in chunks to avoid timeout
    const chunkSize = 100;
    for (let i = 0; i < ledgerTransactions.length; i += chunkSize) {
      const chunk = ledgerTransactions.slice(i, i + chunkSize);
      
      const { error: ledgerError } = await supabase
        .from('transactions')
        .upsert(chunk);

      if (ledgerError) {
        console.error(`❌ Error inserting ledger transactions chunk ${i / chunkSize + 1}:`, ledgerError);
        return;
      }

      console.log(`✅ Inserted chunk ${i / chunkSize + 1}/${Math.ceil(ledgerTransactions.length / chunkSize)} (${chunk.length} transactions)`);
    }

    console.log(`✅ Total ledger transactions inserted: ${ledgerTransactions.length}`);

    // Step 6: Verify data
    console.log('\n6️⃣ Verifying inserted data...');
    
    const { data: bankCount } = await supabase
      .from('transactions')
      .select('id', { count: 'exact' })
      .eq('company_id', companyId)
      .eq('transaction_type', 'bank_statement');

    const { data: ledgerCount } = await supabase
      .from('transactions')
      .select('id', { count: 'exact' })
      .eq('company_id', companyId)
      .eq('transaction_type', 'ledger_entry');

    console.log(`✅ Verification complete:`);
    console.log(`   - Bank transactions in DB: ${bankCount?.length || 0}`);
    console.log(`   - Ledger transactions in DB: ${ledgerCount?.length || 0}`);
    console.log(`   - Files created: ${fileIds.length}`);

    // Step 7: Create sample reconciliation data
    console.log('\n7️⃣ Creating sample reconciliation data...');
    
    // Find a few transactions that might match for demo purposes
    const sampleMatches = [];
    
    // Look for transactions with similar amounts
    for (const bankTx of bankTransactions.slice(0, 3)) {
      for (const ledgerTx of ledgerTransactions.slice(0, 10)) {
        if (Math.abs(bankTx.amount - ledgerTx.amount) < 0.01) {
          sampleMatches.push({
            company_id: companyId,
            bank_transaction_id: bankTx.id,
            ledger_transaction_id: ledgerTx.id,
            status: 'auto_matched',
            match_confidence: 95.0,
            amount_difference: Math.abs(bankTx.amount - ledgerTx.amount),
            date_difference: 0,
            match_type: 'exact',
            matched_fields: ['amount'],
            created_by: userId
          });
          break; // Only match each bank transaction once
        }
      }
    }

    if (sampleMatches.length > 0) {
      const { error: matchError } = await supabase
        .from('reconciliations')
        .upsert(sampleMatches);

      if (matchError) {
        console.error('❌ Error creating sample matches:', matchError);
      } else {
        console.log(`✅ Created ${sampleMatches.length} sample matches`);
      }
    }

    console.log('\n🎉 Test Data Seeding Complete!');
    console.log('==============================');
    console.log(`Company ID: ${companyId}`);
    console.log(`Bank Transactions: ${bankTransactions.length}`);
    console.log(`Ledger Transactions: ${ledgerTransactions.length}`);
    console.log(`Sample Matches: ${sampleMatches.length}`);
    
    console.log('\n🚀 Ready to test the application:');
    console.log('1. Start the app: npm run dev');
    console.log('2. Navigate to /dashboard/reconciliation');
    console.log('3. Click "Start Matching" to test the workflow');
    console.log('4. Review results and generate reports');

  } catch (error) {
    console.error('❌ Seeding failed:', error);
  }
}

// Run the seeding
seedTestData();
