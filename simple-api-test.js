#!/usr/bin/env node

/**
 * Simple API Test for Reconciliation Fix
 */

async function testReconciliationAPI() {
  console.log('🧪 Testing Reconciliation API Fix')

  try {
    // Get files first
    const filesResponse = await fetch('http://localhost:3001/api/workflow-status')

    if (!filesResponse.ok) {
      console.error('❌ Failed to get workflow status')
      return
    }

    const workflowData = await filesResponse.json()
    console.log('📊 Current Workflow Status:', workflowData.summary)

    // Check if there are files to process
    if (workflowData.files && workflowData.files.length > 0) {
      const fileIds = workflowData.files.map(f => f.id)
      console.log(`🔄 Found ${fileIds.length} files, triggering reconciliation...`)

      const reconResponse = await fetch('http://localhost:3001/api/process-reconciliation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ fileIds })
      })

      const reconResult = await reconResponse.text()
      console.log('🎯 Reconciliation Response:', reconResult)

    } else {
      console.log('ℹ️  No files found for processing')
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
}

testReconciliationAPI()