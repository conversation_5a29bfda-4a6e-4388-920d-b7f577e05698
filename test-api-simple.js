#!/usr/bin/env node

/**
 * Simple API Test Script
 * Tests the reconciliation API endpoints using curl
 */

const { spawn } = require('child_process');

const BASE_URL = 'http://localhost:3001';

console.log('🧪 Testing API Endpoints (Simple Version)');
console.log('=========================================');

// Helper function to run curl commands
function curlRequest(endpoint, options = {}) {
  return new Promise((resolve, reject) => {
    const url = `${BASE_URL}${endpoint}`;
    const args = ['-s', '-w', '\\n%{http_code}'];
    
    if (options.method && options.method !== 'GET') {
      args.push('-X', options.method);
    }
    
    if (options.headers) {
      Object.entries(options.headers).forEach(([key, value]) => {
        args.push('-H', `${key}: ${value}`);
      });
    }
    
    if (options.body) {
      args.push('-d', options.body);
    }
    
    args.push(url);
    
    const process = spawn('curl', args);
    
    let stdout = '';
    let stderr = '';
    
    process.stdout.on('data', (data) => {
      stdout += data.toString();
    });
    
    process.stderr.on('data', (data) => {
      stderr += data.toString();
    });
    
    process.on('close', (code) => {
      const lines = stdout.trim().split('\n');
      const httpCode = lines[lines.length - 1];
      const body = lines.slice(0, -1).join('\n');
      
      resolve({
        status: parseInt(httpCode),
        ok: parseInt(httpCode) >= 200 && parseInt(httpCode) < 300,
        body: body,
        error: stderr
      });
    });
    
    process.on('error', (error) => {
      reject(error);
    });
  });
}

async function testApiEndpoints() {
  console.log('\n🔍 Testing API Endpoints...');
  
  try {
    // Test 1: Health Check
    console.log('\n1️⃣ Testing GET /api/health');
    const healthResponse = await curlRequest('/api/health');
    
    if (healthResponse.ok) {
      console.log('✅ Health API working');
      console.log(`   Response: ${healthResponse.body}`);
    } else {
      console.log('❌ Health API failed:', healthResponse.status);
      return;
    }

    // Test 2: Get Reconciliation Data
    console.log('\n2️⃣ Testing GET /api/reconciliation');
    const reconciliationResponse = await curlRequest('/api/reconciliation');
    
    if (reconciliationResponse.ok) {
      console.log('✅ Reconciliation API working');
      try {
        const data = JSON.parse(reconciliationResponse.body);
        console.log(`   Summary: ${JSON.stringify(data.summary, null, 2)}`);
        console.log(`   Transactions: ${data.transactions?.length || 0}`);
      } catch (e) {
        console.log(`   Raw response: ${reconciliationResponse.body.substring(0, 200)}...`);
      }
    } else {
      console.log('❌ Reconciliation API failed:', reconciliationResponse.status);
      console.log('   Error:', reconciliationResponse.body);
    }

    // Test 3: Start Transaction Matching
    console.log('\n3️⃣ Testing POST /api/match-transactions');
    const matchingResponse = await curlRequest('/api/match-transactions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        options: {
          exactMatchFields: ['reference', 'date', 'amount'],
          fuzzyMatchThreshold: 70,
          dateToleranceDays: 3,
          amountTolerancePercentage: 5,
          descriptionSimilarityThreshold: 60
        }
      })
    });

    if (matchingResponse.ok) {
      console.log('✅ Transaction matching API working');
      try {
        const data = JSON.parse(matchingResponse.body);
        console.log(`   Result: ${JSON.stringify(data, null, 2)}`);
      } catch (e) {
        console.log(`   Raw response: ${matchingResponse.body.substring(0, 200)}...`);
      }
    } else {
      console.log('❌ Transaction matching API failed:', matchingResponse.status);
      console.log('   Error:', matchingResponse.body);
    }

    // Test 4: Get Reports
    console.log('\n4️⃣ Testing GET /api/reports');
    const reportsResponse = await curlRequest('/api/reports');
    
    if (reportsResponse.ok) {
      console.log('✅ Reports API working');
      try {
        const data = JSON.parse(reportsResponse.body);
        console.log(`   Reports: ${data.reports?.length || 0}`);
        console.log(`   Stats: ${JSON.stringify(data.stats, null, 2)}`);
      } catch (e) {
        console.log(`   Raw response: ${reportsResponse.body.substring(0, 200)}...`);
      }
    } else {
      console.log('❌ Reports API failed:', reportsResponse.status);
      console.log('   Error:', reportsResponse.body);
    }

    console.log('\n🎯 API Test Summary');
    console.log('==================');
    console.log('✅ API endpoint testing completed');
    console.log('📊 Check the results above for any failures');
    
  } catch (error) {
    console.error('❌ API testing failed:', error.message);
  }
}

// Main execution
async function main() {
  console.log('🔍 Checking if app is running...');
  
  try {
    const healthCheck = await curlRequest('/api/health');
    if (!healthCheck.ok) {
      console.log(`❌ App is not running on ${BASE_URL}`);
      console.log('\n💡 To start the app:');
      console.log('1. Run: npm run dev');
      console.log('2. Wait for the app to start');
      console.log('3. Run this test script again');
      return;
    }
  } catch (error) {
    console.log(`❌ App is not running on ${BASE_URL}`);
    console.log('Error:', error.message);
    return;
  }

  console.log('✅ App is running, starting API tests...');
  await testApiEndpoints();
}

main();
