const { execSync } = require('child_process');

console.log('Creating test discrepancies manually...');

// Create a simple SQL file to insert discrepancies
const sql = `
-- Insert a few test discrepancies
INSERT INTO discrepancies (
  id,
  company_id,
  discrepancy_type,
  amount,
  description,
  status,
  severity,
  metadata,
  created_at
) VALUES
(
  gen_random_uuid(),
  '5277bdf7-bbeb-4cab-bcbb-e55ee177b4dd',
  'bank_only',
  50000.00,
  'Test bank transaction not found in ledger',
  'pending',
  'high',
  '{"test": true}',
  NOW()
),
(
  gen_random_uuid(),
  '5277bdf7-bbeb-4cab-bcbb-e55ee177b4dd',
  'ledger_only',
  25000.00,
  'Test ledger entry not found in bank statement',
  'pending',
  'medium',
  '{"test": true}',
  NOW()
),
(
  gen_random_uuid(),
  '5277bdf7-bbeb-4cab-bcbb-e55ee177b4dd',
  'amount_mismatch',
  100.00,
  'Test amount mismatch between bank and ledger',
  'pending',
  'low',
  '{"test": true}',
  NOW()
);
`;

require('fs').writeFileSync('temp_discrepancies.sql', sql);

try {
  // Use psql to execute the SQL
  const result = execSync('psql postgresql://postgres.kqgdscnfpqbqvgdtdoeg:<EMAIL>:5432/postgres -f temp_discrepancies.sql', {
    encoding: 'utf8'
  });
  console.log('SQL executed successfully:', result);
} catch (error) {
  console.error('Error executing SQL:', error.message);
}

// Clean up
require('fs').unlinkSync('temp_discrepancies.sql');

console.log('Test discrepancies created!');