// Fix the latest uploaded files that are stuck in "Completed" status
const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

async function fixLatestUploads() {
  try {
    console.log('🔧 Fixing latest uploaded files that are stuck in "Completed" status...')
    
    // First, get the latest files to see their IDs
    const { data: latestFiles, error: fetchError } = await supabaseAdmin
      .from('files')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(5)
    
    if (fetchError) {
      console.error('❌ Error fetching files:', fetchError)
      return
    }
    
    console.log('📄 Latest files:', latestFiles.map(f => ({ 
      id: f.id, 
      name: f.original_filename, 
      status: f.status 
    })))
    
    // Update all files with "completed" status to "uploaded"
    const filesToUpdate = latestFiles
      .filter(f => f.status === 'completed')
      .map(f => f.id)
    
    if (filesToUpdate.length === 0) {
      console.log('⚠️ No files with "completed" status found')
      return
    }
    
    console.log(`🔄 Updating ${filesToUpdate.length} files from "completed" to "uploaded"...`)
    
    const { data, error } = await supabaseAdmin
      .from('files')
      .update({ status: 'uploaded' })
      .in('id', filesToUpdate)
      
    if (error) {
      console.error('❌ Error updating files:', error)
      return
    }
    
    console.log('✅ Successfully updated file status to "uploaded"')
    console.log('🚀 Now go back to http://localhost:3002/dashboard/seamless')
    console.log('💡 The page should automatically detect your files and show the "Start Reconciliation" button!')
    
  } catch (error) {
    console.error('💥 Fix failed:', error)
  }
}

fixLatestUploads()
