{"timestamp": "2025-09-18T08:38:20.448Z", "testData": {"bankTransactions": 5, "ledgerTransactions": 540, "validBankTransactions": 4, "validLedgerTransactions": 539}, "matchingResults": {"potentialMatches": 0, "referenceMatches": 0, "amountMatches": 1, "dateMatches": 276, "matchPercentage": 0}, "discrepancies": {"bankOnly": 4, "ledgerOnly": 539, "amountMismatches": 0, "totalDiscrepancyAmount": *********.4799997}, "recommendations": [{"priority": "high", "action": "Review Bank-Only Transactions", "description": "4 bank transactions need corresponding ledger entries", "affectedTransactions": 4}, {"priority": "medium", "action": "Verify Outstanding Transactions", "description": "539 ledger entries may not have cleared the bank", "affectedTransactions": 539}]}