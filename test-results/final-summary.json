{"timestamp": "2025-09-18T18:06:40.270Z", "user": "<EMAIL>", "tests": {"Authentication": {"success": false, "error": "Company association check failed: Could not find a relationship between 'company_users' and 'accounting_companies' in the schema cache", "data": null, "timestamp": "2025-09-18T18:06:42.346Z"}, "File Upload": {"success": false, "error": "fetch is not a function", "data": null, "timestamp": "2025-09-18T18:06:42.369Z"}, "Processing Pipeline": {"success": false, "error": "fetch is not a function", "data": null, "timestamp": "2025-09-18T18:06:42.370Z"}, "Reconciliation Workflow": {"success": false, "error": "fetch is not a function", "data": null, "timestamp": "2025-09-18T18:06:42.372Z"}, "Report Generation": {"success": false, "error": "fetch is not a function", "data": null, "timestamp": "2025-09-18T18:06:42.496Z"}}, "summary": {"total": 5, "passed": 0, "failed": 5, "errors": ["Company association check failed: Could not find a relationship between 'company_users' and 'accounting_companies' in the schema cache", "fetch is not a function", "fetch is not a function", "fetch is not a function", "fetch is not a function"]}}