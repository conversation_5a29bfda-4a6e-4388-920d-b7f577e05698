# End-to-End Test Report

**Generated:** 2025-09-18T18:06:40.270Z
**Test User:** eshetud<PERSON><PERSON><EMAIL>
**Total Tests:** 5
**Passed:** 0
**Failed:** 5

## Test Results


### Authentication
- **Status:** ❌ FAILED
- **Timestamp:** 2025-09-18T18:06:42.346Z
- **Error:** Company association check failed: Could not find a relationship between 'company_users' and 'accounting_companies' in the schema cache



### File Upload
- **Status:** ❌ FAILED
- **Timestamp:** 2025-09-18T18:06:42.369Z
- **Error:** fetch is not a function



### Processing Pipeline
- **Status:** ❌ FAILED
- **Timestamp:** 2025-09-18T18:06:42.370Z
- **Error:** fetch is not a function



### Reconciliation Workflow
- **Status:** ❌ FAILED
- **Timestamp:** 2025-09-18T18:06:42.372Z
- **Error:** fetch is not a function



### Report Generation
- **Status:** ❌ FAILED
- **Timestamp:** 2025-09-18T18:06:42.496Z
- **Error:** fetch is not a function



## Summary

⚠️ 5 test(s) failed. Please review the failures above.

## Test Environment
- **Supabase URL:** https://rrvdmpagsvhjdurfmqec.supabase.co
- **Database Tables:** accounting_companies, user_profiles, company_users, files, transactions, reconciliations, reports, discrepancies
- **Test Files:** RFSA Ethiopian Bank Statement and Ledger (July 2025)
- **Python Environment:** System Python
