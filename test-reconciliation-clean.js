const { createServerSupabaseClient } = require('./src/lib/supabase/server')

async function testReconciliationWorkflow() {
  try {
    console.log('🧪 Starting reconciliation workflow test...')

    const supabase = createServerSupabaseClient()

    // First check our cleaned transaction counts
    const { data: bankTxns } = await supabase
      .from('transactions')
      .select('*')
      .eq('type', 'bank')

    const { data: ledgerTxns } = await supabase
      .from('transactions')
      .select('*')
      .eq('type', 'ledger')

    console.log(`📊 Current transaction counts:`)
    console.log(`  Bank transactions: ${bankTxns?.length || 0}`)
    console.log(`  Ledger transactions: ${ledgerTxns?.length || 0}`)
    console.log(`  Total: ${(bankTxns?.length || 0) + (ledgerTxns?.length || 0)}`)

    // Test reconciliation endpoint
    console.log('\n🔄 Testing seamless reconciliation API...')

    const response = await fetch('http://localhost:3000/api/seamless-reconciliation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        test: true
      })
    })

    const result = await response.json()
    console.log('📋 Reconciliation result:', result)

    if (result.success) {
      console.log(`✅ Found ${result.matchedTransactions} matched transactions`)
      console.log(`❌ Found ${result.unmatchedBankTransactions} unmatched bank transactions`)
      console.log(`❌ Found ${result.unmatchedLedgerTransactions} unmatched ledger transactions`)

      // Check if the matches make sense
      const totalProcessed = result.matchedTransactions + result.unmatchedBankTransactions + result.unmatchedLedgerTransactions
      console.log(`📊 Total transactions processed: ${totalProcessed}`)

      if (result.matchedTransactions === 1000 && result.unmatchedBankTransactions === 0 && result.unmatchedLedgerTransactions === 0) {
        console.log('⚠️  SUSPICIOUS: Still getting 1000/1000 matches')
      } else {
        console.log('✅ Results look more realistic')
      }
    } else {
      console.log('❌ Reconciliation failed:', result.error)
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
}

testReconciliationWorkflow()