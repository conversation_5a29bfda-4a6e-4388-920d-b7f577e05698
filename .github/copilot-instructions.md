# Copilot Instructions for AI Agents

## Project Overview

**Accounting Discrepancy Finder** - A secure, cloud-native web application for automating bank statement and ledger reconciliation. Built with Next.js 15 App Router, Supabase, TanStack Query, Shadcn UI, and E2B for secure file processing.

### Core Purpose

Eliminate manual reconciliation errors by automatically parsing financial documents, matching transactions, and generating discrepancy reports with journal voucher recommendations.

## Technology Stack & Architecture

### Frontend & UI

- **Framework**: Next.js 15 (App Router) - Server Components for security, Client Components for interactivity
- **UI Library**: Shadcn UI + Tailwind CSS - Accessible, customizable financial data components
- **State Management**: TanStack Query - Real-time data fetching with optimistic updates
- **Typography**: Inter font family for financial readability

### Backend & Security

- **Database**: Supabase (Postgres) with Row-Level Security (RLS) for multi-tenant isolation
- **Authentication**: Supabase Auth with MFA enforcement for financial operations
- **File Processing**: E2B sandboxes for secure, isolated Python execution (PDF/Excel parsing)
- **Storage**: Supabase Storage with encryption and auto-deletion policies
- **API**: Next.js Server Actions for type-safe, server-side operations

### File Processing Architecture

```
User Upload → Next.js API → Supabase Storage → E2B Python Sandbox → Postgres → UI Updates
```

## Key Directories and Files

### Core Application Structure

```
/app                          # Next.js App Router
├── (dashboard)/             # Authenticated dashboard layout
│   ├── reconciliation/      # Main reconciliation interface
│   └── reports/             # Reporting and analytics
├── api/                     # API routes for file processing
├── auth/                    # Authentication pages
└── globals.css              # Global styles with Tailwind

/components                   # Shadcn UI components
├── ui/                      # Base UI components
├── forms/                   # Form components with validation
├── tables/                  # Financial data table components
└── charts/                  # Data visualization components

/lib                         # Utilities and configurations
├── supabase/               # Supabase client and helpers
├── e2b/                    # E2B sandbox utilities
├── validations/            # Zod schemas for data validation
└── utils.ts                # Shared utility functions

/types                       # TypeScript type definitions
├── database.ts             # Supabase generated types
├── financial.ts            # Financial data types
└── api.ts                  # API response types
```

### Configuration Files

- `PRD-final.md`: Complete product requirements and architecture
- `E2B-Best-Practices.md`: Secure file processing implementation guide
- `example-docs/`: Sample bank statements for testing (July 2025 RFSA statements)

## Development Patterns & Conventions

### Security-First Development

- **Server Components**: Use for data fetching and sensitive operations
- **Input Validation**: Zod schemas for all user inputs and API responses
- **File Processing**: Always use E2B sandboxes - never process files on main server
- **Database Access**: All queries use RLS policies with `auth.uid()` filtering
- **Error Handling**: Never expose internal errors to users; log sensitively

### Financial Data Handling

- **Decimal Precision**: Use `Decimal` type for all monetary calculations
- **Audit Trails**: Log all data modifications with user attribution and timestamps
- **Data Validation**: Multi-layer validation (client, server, database)
- **Transaction Matching**: Implement fuzzy matching with confidence scores

### UI/UX Patterns

- **Progressive Disclosure**: Show summary first, details on demand
- **Real-time Updates**: Use TanStack Query for live processing status
- **Accessibility**: WCAG AA compliance for financial data tables
- **Error States**: Clear, actionable error messages with recovery options

## Critical Integration Points

### E2B Sandbox Integration

```typescript
// File processing pattern
async function processFinancialDocument(file: File) {
  const sandbox = await Sandbox.create({
    timeout: 300,
    memory: '2GB',
    networkAccess: false
  })

  try {
    // Upload to sandbox and process
    const results = await sandbox.run_code(pythonProcessingScript)
    return validateResults(results)
  } finally {
    await sandbox.close() // Always cleanup
  }
}
```

### Supabase RLS Patterns

```sql
-- Example RLS policy for transaction data
create policy "Users can only view their company transactions"
on transactions for select
using (
  (select auth.uid()) in (
    select user_id from company_users
    where company_id = transactions.company_id
  )
);
```

### TanStack Query Integration

```typescript
// Real-time processing status
const { data: processingStatus } = useQuery({
  queryKey: ['processing', fileId],
  queryFn: () => getProcessingStatus(fileId),
  refetchInterval: 2000, // Poll every 2 seconds
  enabled: status === 'processing'
})
```

## Build & Development Workflow

### Development Setup

```bash
npm install                    # Install dependencies
npx supabase start            # Start local Supabase
npm run dev                   # Start Next.js dev server
npm run db:generate-types     # Generate Supabase types
```

### Testing Strategy

- **Unit Tests**: Jest + React Testing Library for components
- **Integration Tests**: E2B sandbox processing workflows
- **E2E Tests**: Playwright for complete user journeys
- **Security Tests**: File upload validation and RLS policy testing

### Deployment Pipeline

1. **Staging**: Auto-deploy from `develop` branch to Vercel staging
2. **Security Scan**: Automated vulnerability scanning
3. **E2B Testing**: Validate sandbox processing in staging environment
4. **Production**: Manual deployment with rollback capability

## Data Models & Relationships

### Core Entities

- **Companies**: Multi-tenant organization structure
- **Users**: Authentication and role-based access
- **Files**: Uploaded documents with processing status
- **Transactions**: Parsed financial data with metadata
- **Reconciliations**: Matching results and discrepancy reports

### Financial Data Patterns

- **Transaction Types**: Bank statements vs. ledger entries
- **Matching Logic**: Reference-based, date+amount, fuzzy algorithms
- **Status Tracking**: Processing → Matched → Reviewed → Approved

## AI Agent Guidelines

### When Implementing Features

1. **Security First**: Always validate inputs and use RLS policies
2. **Follow Patterns**: Use established components and utilities
3. **Document Changes**: Update this file when adding new patterns
4. **Test Thoroughly**: Include unit tests and integration tests
5. **Performance**: Consider TanStack Query caching strategies

### Common Tasks

- **Adding UI Components**: Use Shadcn CLI and follow existing patterns
- **Database Changes**: Update Supabase migrations and regenerate types
- **File Processing**: Extend E2B sandbox scripts following security guidelines
- **API Routes**: Use Server Actions with proper error handling

### Debugging Financial Applications

- **Data Integrity**: Always verify transaction calculations
- **Audit Trails**: Check all database changes are logged
- **Security**: Verify RLS policies prevent unauthorized access
- **Performance**: Monitor E2B sandbox usage and costs

## Compliance & Standards

### Security Requirements

- **SOX Compliance**: Audit trails, access controls, data integrity
- **Data Privacy**: GDPR compliance with data portability and deletion
- **Financial Standards**: Proper decimal handling, transaction logging

### Code Quality

- **TypeScript**: Strict mode enabled, no `any` types
- **ESLint**: Financial application security rules
- **Prettier**: Consistent code formatting
- **Commit Conventions**: Conventional commits for clear history

---

**Always Reference**:

- `PRD-final.md` for product requirements and architecture decisions
  .
- USE MCP TOOLS AVAILABLE TO YOU EVERYTIME! Particularly when you get confused or you need help!!
- `E2B-Best-Practices.md` for secure file processing implementation
- Example files in `example-docs/` for testing data formats

**Security Reminder**: This application handles sensitive financial data. Every decision should prioritize security, auditability, and regulatory compliance.

---

_Last updated: 2025-09-17. Updated with finalized architecture and implementation patterns._
