#!/usr/bin/env node

/**
 * Test the Fixed Reconciliation Algorithm
 * This script will trigger a fresh reconciliation process with the corrected matching logic
 */

const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'http://127.0.0.1:54321'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function testFixedReconciliation() {
  console.log('🚀 Testing Fixed Reconciliation Algorithm')
  console.log('=' * 50)

  try {
    // First, clear existing bad data
    console.log('🧹 Clearing existing reconciliation data...')

    const { error: clearError } = await supabase
      .from('reconciliations')
      .delete()
      .neq('id', '00000000-0000-0000-0000-************') // Delete all

    if (clearError) {
      console.error('❌ Error clearing reconciliations:', clearError)
    } else {
      console.log('✅ Cleared existing reconciliation data')
    }

    // Check current transaction counts
    const { data: transactions, error: txnError } = await supabase
      .from('transactions')
      .select('transaction_type')

    if (txnError) {
      console.error('❌ Error fetching transactions:', txnError)
      return
    }

    const bankCount = transactions.filter(t => t.transaction_type === 'bank_statement').length
    const ledgerCount = transactions.filter(t => t.transaction_type === 'ledger_entry').length

    console.log(`📊 Current Data:`)
    console.log(`   Bank Transactions: ${bankCount}`)
    console.log(`   Ledger Transactions: ${ledgerCount}`)

    // Get file IDs for reconciliation
    const { data: files, error: filesError } = await supabase
      .from('files')
      .select('id')
      .order('created_at', { ascending: false })
      .limit(10)

    if (filesError || !files || files.length === 0) {
      console.error('❌ No files found for reconciliation')
      return
    }

    const fileIds = files.map(f => f.id)
    console.log(`🔄 Processing reconciliation with ${fileIds.length} files...`)

    // Trigger the fixed reconciliation process
    const response = await fetch('http://localhost:3001/api/process-reconciliation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        fileIds: fileIds
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('❌ Reconciliation API error:', response.status, errorText)
      return
    }

    const result = await response.json()
    console.log('✅ Reconciliation completed successfully!')
    console.log('📊 Results:', result.summary)

    // Verify the results
    console.log('\n🔍 Verifying Results...')

    const { data: newReconciliations, error: reconError } = await supabase
      .from('reconciliations')
      .select('*')

    if (reconError) {
      console.error('❌ Error fetching reconciliation results:', reconError)
      return
    }

    const uniqueBankMatched = new Set(newReconciliations.map(r => r.bank_transaction_id)).size
    const uniqueLedgerMatched = new Set(newReconciliations.map(r => r.ledger_transaction_id)).size

    console.log(`📈 Final Statistics:`)
    console.log(`   Total Reconciliation Records: ${newReconciliations.length}`)
    console.log(`   Unique Bank Transactions Matched: ${uniqueBankMatched}`)
    console.log(`   Unique Ledger Transactions Matched: ${uniqueLedgerMatched}`)
    console.log(`   Bank Match Rate: ${((uniqueBankMatched / bankCount) * 100).toFixed(1)}%`)
    console.log(`   Ledger Match Rate: ${((uniqueLedgerMatched / ledgerCount) * 100).toFixed(1)}%`)

    // Check for any remaining over-matching issues
    const overMatches = newReconciliations.reduce((acc, recon) => {
      acc[recon.ledger_transaction_id] = (acc[recon.ledger_transaction_id] || 0) + 1
      return acc
    }, {})

    const overMatchedLedgers = Object.entries(overMatches).filter(([id, count]) => count > 1)

    if (overMatchedLedgers.length > 0) {
      console.log(`⚠️  WARNING: Still found ${overMatchedLedgers.length} ledger transactions matched to multiple bank transactions:`)
      overMatchedLedgers.slice(0, 5).forEach(([id, count]) => {
        console.log(`   Ledger ${id}: ${count} matches`)
      })
    } else {
      console.log('✅ Perfect! No over-matching detected - each ledger transaction matched to at most one bank transaction')
    }

  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

// Run the test
testFixedReconciliation()