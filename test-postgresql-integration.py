#!/usr/bin/env python3
"""
Test PostgreSQL integration for the reconciliation workflow
This validates the complete database workflow
"""

import json
import os
from datetime import datetime
import psycopg2
from psycopg2.extras import RealDictCursor
import pandas as pd

class PostgreSQLReconciliationTest:
    """Test PostgreSQL integration for reconciliation workflow"""
    
    def __init__(self, connection_string=None):
        # Use local PostgreSQL or Supabase connection
        self.connection_string = connection_string or "postgresql://localhost:5432/reconciliation_test"
        self.conn = None
    
    def connect(self):
        """Connect to PostgreSQL database"""
        try:
            self.conn = psycopg2.connect(self.connection_string)
            print("✅ Connected to PostgreSQL database")
            return True
        except Exception as e:
            print(f"❌ Database connection failed: {e}")
            print("💡 This test requires PostgreSQL. Install with: brew install postgresql")
            return False
    
    def create_tables(self):
        """Create tables for reconciliation data"""
        if not self.conn:
            return False
            
        try:
            cursor = self.conn.cursor()
            
            # Create bank transactions table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS bank_transactions (
                    id SERIAL PRIMARY KEY,
                    date DATE NOT NULL,
                    description TEXT,
                    reference VARCHAR(100),
                    debit DECIMAL(15,2) DEFAULT 0,
                    credit DECIMAL(15,2) DEFAULT 0,
                    balance DECIMAL(15,2),
                    amount DECIMAL(15,2),
                    file_id VARCHAR(50),
                    created_at TIMESTAMP DEFAULT NOW()
                );
            """)
            
            # Create ledger transactions table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS ledger_transactions (
                    id SERIAL PRIMARY KEY,
                    date DATE NOT NULL,
                    description TEXT,
                    reference VARCHAR(100),
                    account_id VARCHAR(50),
                    account_description TEXT,
                    journal VARCHAR(50),
                    debit DECIMAL(15,2) DEFAULT 0,
                    credit DECIMAL(15,2) DEFAULT 0,
                    balance DECIMAL(15,2),
                    amount DECIMAL(15,2),
                    file_id VARCHAR(50),
                    created_at TIMESTAMP DEFAULT NOW()
                );
            """)
            
            # Create reconciliation matches table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS reconciliation_matches (
                    id SERIAL PRIMARY KEY,
                    bank_transaction_id INTEGER REFERENCES bank_transactions(id),
                    ledger_transaction_id INTEGER REFERENCES ledger_transactions(id),
                    match_type VARCHAR(20),
                    confidence DECIMAL(3,2),
                    amount_difference DECIMAL(15,2) DEFAULT 0,
                    reconciliation_date TIMESTAMP DEFAULT NOW(),
                    status VARCHAR(20) DEFAULT 'pending'
                );
            """)
            
            # Create reconciliation sessions table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS reconciliation_sessions (
                    id SERIAL PRIMARY KEY,
                    session_name VARCHAR(100),
                    bank_file_name VARCHAR(200),
                    ledger_file_name VARCHAR(200),
                    total_bank_transactions INTEGER,
                    total_ledger_transactions INTEGER,
                    matched_transactions INTEGER,
                    match_percentage DECIMAL(5,2),
                    balance_difference DECIMAL(15,2),
                    status VARCHAR(20) DEFAULT 'completed',
                    created_at TIMESTAMP DEFAULT NOW()
                );
            """)
            
            self.conn.commit()
            print("✅ Database tables created successfully")
            return True
            
        except Exception as e:
            print(f"❌ Error creating tables: {e}")
            return False
    
    def load_test_data(self):
        """Load test data from JSON files"""
        try:
            # Load bank transactions
            bank_file = "/Users/<USER>/Desktop/projects/acounting-app/src/lib/e2b/extracted_data/bank_transactions.json"
            with open(bank_file, 'r') as f:
                bank_data = json.load(f)
            
            # Load ledger transactions
            ledger_file = "/Users/<USER>/Desktop/projects/acounting-app/src/lib/e2b/extracted_data/ledger_transactions_complete.json"
            with open(ledger_file, 'r') as f:
                ledger_data = json.load(f)
            
            cursor = self.conn.cursor()
            
            # Insert bank transactions
            bank_count = 0
            for txn in bank_data:
                cursor.execute("""
                    INSERT INTO bank_transactions 
                    (date, description, reference, debit, credit, balance, amount, file_id)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    txn.get('date'),
                    txn.get('description', ''),
                    txn.get('reference', ''),
                    float(txn.get('debit', 0)),
                    float(txn.get('credit', 0)),
                    float(txn.get('balance', 0)),
                    float(txn.get('amount', 0)),
                    'RFSA_bank_statement_july_2025'
                ))
                bank_count += 1
            
            # Insert ledger transactions
            ledger_count = 0
            for txn in ledger_data:
                cursor.execute("""
                    INSERT INTO ledger_transactions 
                    (date, description, reference, account_id, account_description, journal, 
                     debit, credit, balance, amount, file_id)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    txn.get('date'),
                    txn.get('description', ''),
                    txn.get('reference', ''),
                    txn.get('account_id', ''),
                    txn.get('account_description', ''),
                    txn.get('journal', ''),
                    float(txn.get('debit', 0)),
                    float(txn.get('credit', 0)),
                    float(txn.get('balance', 0)),
                    float(txn.get('amount', 0)),
                    'RFSA_ledger_july_2025'
                ))
                ledger_count += 1
            
            self.conn.commit()
            print(f"✅ Loaded {bank_count} bank transactions and {ledger_count} ledger transactions")
            return bank_count, ledger_count
            
        except Exception as e:
            print(f"❌ Error loading test data: {e}")
            return 0, 0
    
    def run_sql_reconciliation(self):
        """Run reconciliation using SQL queries"""
        try:
            cursor = self.conn.cursor(cursor_factory=RealDictCursor)
            
            # Find exact amount and date matches
            cursor.execute("""
                SELECT 
                    b.id as bank_id,
                    l.id as ledger_id,
                    b.date,
                    b.amount as bank_amount,
                    l.amount as ledger_amount,
                    b.description as bank_desc,
                    l.description as ledger_desc,
                    ABS(b.amount - l.amount) as amount_diff
                FROM bank_transactions b
                JOIN ledger_transactions l ON b.date = l.date
                WHERE ABS(b.amount - l.amount) < 0.01
                ORDER BY b.date, ABS(b.amount - l.amount);
            """)
            
            exact_matches = cursor.fetchall()
            
            # Find close amount matches (within 1% or 100 ETB)
            cursor.execute("""
                SELECT 
                    b.id as bank_id,
                    l.id as ledger_id,
                    b.date,
                    b.amount as bank_amount,
                    l.amount as ledger_amount,
                    b.description as bank_desc,
                    l.description as ledger_desc,
                    ABS(b.amount - l.amount) as amount_diff
                FROM bank_transactions b
                JOIN ledger_transactions l ON b.date = l.date
                WHERE ABS(b.amount - l.amount) > 0.01 
                  AND (ABS(b.amount - l.amount) < 100 OR ABS(b.amount - l.amount) / GREATEST(ABS(b.amount), ABS(l.amount)) < 0.01)
                ORDER BY b.date, ABS(b.amount - l.amount)
                LIMIT 50;
            """)
            
            close_matches = cursor.fetchall()
            
            # Calculate summary statistics
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_bank,
                    SUM(amount) as total_bank_amount
                FROM bank_transactions;
            """)
            bank_stats = cursor.fetchone()
            
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_ledger,
                    SUM(amount) as total_ledger_amount
                FROM ledger_transactions;
            """)
            ledger_stats = cursor.fetchone()
            
            # Store reconciliation session
            cursor.execute("""
                INSERT INTO reconciliation_sessions 
                (session_name, bank_file_name, ledger_file_name, total_bank_transactions,
                 total_ledger_transactions, matched_transactions, match_percentage, balance_difference)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING id;
            """, (
                f"RFSA_Reconciliation_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "RFSA_bank_statement_july_2025.pdf",
                "RFSA_ledger_july_2025.xlsx",
                bank_stats['total_bank'],
                ledger_stats['total_ledger'],
                len(exact_matches) + len(close_matches),
                ((len(exact_matches) + len(close_matches)) / max(bank_stats['total_bank'], ledger_stats['total_ledger'])) * 100,
                bank_stats['total_bank_amount'] - ledger_stats['total_ledger_amount']
            ))
            
            session_id = cursor.fetchone()['id']
            
            # Store matches
            for match in exact_matches:
                cursor.execute("""
                    INSERT INTO reconciliation_matches 
                    (bank_transaction_id, ledger_transaction_id, match_type, confidence, amount_difference)
                    VALUES (%s, %s, %s, %s, %s);
                """, (
                    match['bank_id'],
                    match['ledger_id'],
                    'exact_match',
                    0.95,
                    match['amount_diff']
                ))
            
            for match in close_matches:
                cursor.execute("""
                    INSERT INTO reconciliation_matches 
                    (bank_transaction_id, ledger_transaction_id, match_type, confidence, amount_difference)
                    VALUES (%s, %s, %s, %s, %s);
                """, (
                    match['bank_id'],
                    match['ledger_id'],
                    'close_match',
                    0.80,
                    match['amount_diff']
                ))
            
            self.conn.commit()
            
            results = {
                'session_id': session_id,
                'exact_matches': len(exact_matches),
                'close_matches': len(close_matches),
                'total_matches': len(exact_matches) + len(close_matches),
                'bank_stats': dict(bank_stats),
                'ledger_stats': dict(ledger_stats),
                'sample_exact_matches': [dict(m) for m in exact_matches[:5]],
                'sample_close_matches': [dict(m) for m in close_matches[:5]]
            }
            
            print(f"✅ SQL Reconciliation completed - Session ID: {session_id}")
            print(f"   Exact matches: {len(exact_matches)}")
            print(f"   Close matches: {len(close_matches)}")
            print(f"   Total matches: {len(exact_matches) + len(close_matches)}")
            
            return results
            
        except Exception as e:
            print(f"❌ Error running SQL reconciliation: {e}")
            return None
    
    def generate_database_report(self, results):
        """Generate comprehensive database report"""
        if not results:
            return
            
        try:
            # Create test results directory
            test_results_dir = "/Users/<USER>/Desktop/projects/acounting-app/test-results"
            os.makedirs(test_results_dir, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Save database test results
            db_report_file = os.path.join(test_results_dir, f"postgresql_test_results_{timestamp}.json")
            with open(db_report_file, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            
            # Create summary report
            summary_file = os.path.join(test_results_dir, f"postgresql_summary_{timestamp}.txt")
            with open(summary_file, 'w') as f:
                f.write("POSTGRESQL RECONCILIATION TEST RESULTS\n")
                f.write("=" * 60 + "\n\n")
                f.write(f"Test Date: {datetime.now().isoformat()}\n")
                f.write(f"Session ID: {results['session_id']}\n\n")
                
                f.write("DATABASE STATISTICS:\n")
                f.write(f"Bank Transactions: {results['bank_stats']['total_bank']}\n")
                f.write(f"Ledger Transactions: {results['ledger_stats']['total_ledger']}\n")
                f.write(f"Bank Balance: {results['bank_stats']['total_bank_amount']:,.2f} ETB\n")
                f.write(f"Ledger Balance: {results['ledger_stats']['total_ledger_amount']:,.2f} ETB\n\n")
                
                f.write("MATCHING RESULTS:\n")
                f.write(f"Exact Matches: {results['exact_matches']}\n")
                f.write(f"Close Matches: {results['close_matches']}\n")
                f.write(f"Total Matches: {results['total_matches']}\n")
                f.write(f"Match Rate: {(results['total_matches'] / max(results['bank_stats']['total_bank'], results['ledger_stats']['total_ledger'])) * 100:.1f}%\n\n")
                
                f.write("SAMPLE EXACT MATCHES:\n")
                for i, match in enumerate(results['sample_exact_matches']):
                    f.write(f"{i+1}. {match['date']} - {match['bank_amount']} ETB - {match['bank_desc'][:50]}...\n")
                
                f.write("\nSAMPLE CLOSE MATCHES:\n")
                for i, match in enumerate(results['sample_close_matches']):
                    f.write(f"{i+1}. {match['date']} - Bank: {match['bank_amount']} ETB, Ledger: {match['ledger_amount']} ETB (Diff: {match['amount_diff']})\n")
            
            print(f"✅ Database report saved to: {db_report_file}")
            print(f"✅ Summary saved to: {summary_file}")
            
        except Exception as e:
            print(f"❌ Error generating database report: {e}")
    
    def cleanup(self):
        """Clean up database connection"""
        if self.conn:
            self.conn.close()
            print("✅ Database connection closed")
    
    def run_full_test(self):
        """Run complete PostgreSQL integration test"""
        print("=" * 60)
        print("POSTGRESQL RECONCILIATION INTEGRATION TEST")
        print("=" * 60)
        
        # Connect to database
        if not self.connect():
            print("❌ Skipping PostgreSQL test - database not available")
            return False
        
        # Create tables
        if not self.create_tables():
            return False
        
        # Load test data
        bank_count, ledger_count = self.load_test_data()
        if bank_count == 0 or ledger_count == 0:
            return False
        
        # Run SQL reconciliation
        results = self.run_sql_reconciliation()
        if not results:
            return False
        
        # Generate reports
        self.generate_database_report(results)
        
        # Cleanup
        self.cleanup()
        
        print("=" * 60)
        print("POSTGRESQL TEST COMPLETED SUCCESSFULLY")
        print("=" * 60)
        return True

def main():
    """Main test function"""
    tester = PostgreSQLReconciliationTest()
    success = tester.run_full_test()
    
    if success:
        print("🎉 All PostgreSQL integration tests passed!")
    else:
        print("⚠️  PostgreSQL tests skipped (database not available)")
        print("💡 To run full tests, install PostgreSQL: brew install postgresql")

if __name__ == "__main__":
    main()
